<?php
    if(C('LAYOUT_ON')) {
        echo '{__NOLAYOUT__}';
    }
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>跳转提示</title>
<style type="text/css">
*{ padding: 0; margin: 0; }
body{ background: #F2F2F2; font-family: '微软雅黑'; color: #333; font-size: 14px; }
.system-message{ padding: 24px 14px;text-align:center; }
.system-message h1{font-weight: normal; line-height: 120px; margin-bottom: 12px;}
.system-message .jump{ padding-top: 10px;color:#999;font-size:14px;}
.system-message .jump a{ color: #999;}
.system-message .success,.system-message .error{ line-height: 1.8em; font-size: 18px ;color:#FF7E16}
.system-message .detail{ font-size: 18px; line-height: 20px; margin-top: 12px; display:none}
</style>
</head>
<body>
<div class="system-message">
<?php if(isset($message)) {?>
<p class="success"><?php echo($message); ?></p>
<?php }else{?>
<p class="error"><?php echo($error); ?></p>
<?php }?>
<p class="detail"></p>
<p class="jump">
<a id="href" href="<?php echo($jumpUrl); ?>">页面跳转</a> 等待时间： <b id="wait"><?php echo($waitSecond); ?></b>
</p>
</div>
<script type="text/javascript">
(function(){
var wait = document.getElementById('wait'),href = document.getElementById('href').href;
var interval = setInterval(function(){
	var time = --wait.innerHTML;
	if(time <= 0) {
		location.href = href;
		clearInterval(interval);
	};
}, 1000);
})();
</script>
</body>
</html>
