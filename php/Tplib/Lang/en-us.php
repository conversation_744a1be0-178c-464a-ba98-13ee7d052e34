<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * ThinkPHP English language package
 */
return array(
    /* core language package */ 
    '_MODULE_NOT_EXIST_'     => "Module can't be loaded",
    '_CONTROLLER_NOT_EXIST_' =>	"Controller can't be loaded",
    '_ERROR_ACTION_'         => 'Illegal Action',
    '_LANGUAGE_NOT_LOAD_'    => "Can't load language package",
    '_TEMPLATE_NOT_EXIST_'   => "Template doesn't exist",
    '_MODULE_'               => 'Module',
    '_ACTION_'               => 'Action',
    '_MODEL_NOT_EXIST_'      => "Model can't be loaded",
    '_VALID_ACCESS_'         => 'No access',
    '_XML_TAG_ERROR_'        => 'XML tag syntax errors',
    '_DATA_TYPE_INVALID_'    => 'Illegal data objects!',
    '_OPERATION_WRONG_'      => 'Operation error occurs',
    '_NOT_LOAD_DB_'          => 'Unable to load the database',
    '_NO_DB_DRIVER_'         => 'Unable to load database driver',
    '_NOT_SUPPORT_DB_'       => 'The system is temporarily not support database',
    '_NO_DB_CONFIG_'         => 'Not define the database configuration',
    '_NOT_SUPPORT_'          => 'The system does not support',
    '_CACHE_TYPE_INVALID_'   => 'Unable to load the cache type',
    '_FILE_NOT_WRITABLE_'   => 'Directory (file) is not writable',
    '_METHOD_NOT_EXIST_'     => 'The method you requested  does not exist!',
    '_CLASS_NOT_EXIST_'      => 'Instantiating a class does not exist！',
    '_CLASS_CONFLICT_'       => 'Class name conflicts',
    '_TEMPLATE_ERROR_'       => 'Template Engine errors',
    '_CACHE_WRITE_ERROR_'    => 'Cache file write failed!',
    '_TAGLIB_NOT_EXIST_'     => 'Tag library is not defined',
    '_OPERATION_FAIL_'       => 'Operation failed!',
    '_OPERATION_SUCCESS_'    => 'Operation succeed!',
    '_SELECT_NOT_EXIST_'     => 'Record does not exist!',
    '_EXPRESS_ERROR_'        => 'Expression errors',
    '_TOKEN_ERROR_'          => "Form's token errors",
    '_RECORD_HAS_UPDATE_'    => 'Record has been updated',
    '_NOT_ALLOW_PHP_'        => 'PHP codes are not allowed in the template',
    '_PARAM_ERROR_'          => 'Parameter error or undefined',
    '_ERROR_QUERY_EXPRESS_'  => 'Query express error',       
);
