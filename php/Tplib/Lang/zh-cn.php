<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

/**
 * ThinkPHP 简体中文语言包
 */
return array(
    /* 核心语言变量 */  
    '_MODULE_NOT_EXIST_'     => '无法加载模块',
    '_CONTROLLER_NOT_EXIST_' =>	'无法加载控制器',
    '_ERROR_ACTION_'         => '非法操作',
    '_LANGUAGE_NOT_LOAD_'    => '无法加载语言包',
    '_TEMPLATE_NOT_EXIST_'   => '模板不存在',
    '_MODULE_'               => '模块',
    '_ACTION_'               => '操作',
    '_MODEL_NOT_EXIST_'      => '模型不存在或者没有定义',
    '_VALID_ACCESS_'         => '没有权限',
    '_XML_TAG_ERROR_'        => 'XML标签语法错误',
    '_DATA_TYPE_INVALID_'    => '非法数据对象！',
    '_OPERATION_WRONG_'      => '操作出现错误',
    '_NOT_LOAD_DB_'          => '无法加载数据库',
    '_NO_DB_DRIVER_'         => '无法加载数据库驱动',
    '_NOT_SUPPORT_DB_'       => '系统暂时不支持数据库',
    '_NO_DB_CONFIG_'         => '没有定义数据库配置',
    '_NOT_SUPPORT_'          => '系统不支持',
    '_CACHE_TYPE_INVALID_'   => '无法加载缓存类型',
    '_FILE_NOT_WRITABLE_'   => '目录（文件）不可写',
    '_METHOD_NOT_EXIST_'     => '方法不存在！',
    '_CLASS_NOT_EXIST_'      => '实例化一个不存在的类！',
    '_CLASS_CONFLICT_'       => '类名冲突',
    '_TEMPLATE_ERROR_'       => '模板引擎错误',
    '_CACHE_WRITE_ERROR_'    => '缓存文件写入失败！',
    '_TAGLIB_NOT_EXIST_'     => '标签库未定义',
    '_OPERATION_FAIL_'       => '操作失败！',
    '_OPERATION_SUCCESS_'    => '操作成功！',
    '_SELECT_NOT_EXIST_'     => '记录不存在！',
    '_EXPRESS_ERROR_'        => '表达式错误',
    '_TOKEN_ERROR_'          => '表单令牌错误',
    '_RECORD_HAS_UPDATE_'    => '记录已经更新',
    '_NOT_ALLOW_PHP_'        => '模板禁用PHP代码',
    '_PARAM_ERROR_'          => '参数错误或者未定义',
    '_ERROR_QUERY_EXPRESS_'  => '错误的查询条件',
);
