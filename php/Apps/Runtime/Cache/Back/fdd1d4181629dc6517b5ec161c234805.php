<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<div  class="x-body">
	<button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button>
	<button id="del_click" class="layui-btn" style="background:#ff6c60;" ><i class="fa fa-trash-o" aria-hidden="true" ></i> 删除</button>
	<script>
		$('#add_click').click(function(){
			var aa = "<?php echo U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']));?>";
			x_admin_show('添加内容', aa);
		});
	</script>
	<?php if(is_array($cat_small)): foreach($cat_small as $key=>$v): if($_GET['small'] == $v): ?>&nbsp;&nbsp; <a href="<?php echo U('index', array('cid'=>$_GET['cid'], 'type'=>$_GET['type'], 'small'=>$v));?>" style="color:red;"><?php echo ($v); ?></a>
			<?php else: ?>
			&nbsp;&nbsp; <a href="<?php echo U('index', array('cid'=>$_GET['cid'], 'type'=>$_GET['type'], 'small'=>$v));?>"><?php echo ($v); ?></a><?php endif; endforeach; endif; ?>
	<div style="float:right;">
		<form action="<?php echo U('index');?>" id="fms">
			<input name="cid" value="<?php echo ($_GET['cid']); ?>" type="hidden" />
			<input lay-verify="required"  name="key" id="key" placeholder="输入关键词" class="layui-input" style="width:200px;float: left;" />
			<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
			<div style="clear: both;"></div>
		</form>
		<script>
			$('#search_click').click(function(){
				if($('#key').val()==''){
					alert('请输入关键词');
					$('#key').focus();
					return false;
				}
			});
		</script>
	</div>
	<div style="clear: both;"></div>
	<form id="fm1">
		<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr style="text-align: left;">
					<th><label id="checkAll"><input type="checkbox" class="ver_mid" /> <span class="ver_mid">全选</span></label></th>
					<th width="400">标题</th>
					<th>加入时间</th>
					<th>排序</th>
					<!-- <th>推荐</th> -->
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<?php if(is_array($lists)): foreach($lists as $key=>$v): ?><tr>
						<td><label><input type="checkbox" name="id[]" value="<?php echo ($v["id"]); ?>" class="ver_mid per_check" /> <span class="ver_mid"><?php echo ($v["id"]); ?></span></label></td>
						<td><?php echo ($v["title"]); ?></td>
						<td><?php echo (date("Y-m-d H:i",$v["add_time"])); ?></td>
						<td><input class="layui-input order_blur clr_79"  value="<?php echo ($v["order_id"]); ?>" myid="<?php echo ($v["id"]); ?>" style="width:50px;"/></td>
<!-- 						<td>
							<a class="recom_click" myid="<?php echo ($v["id"]); ?>" href="javascript:;" title="推荐" >
								<?php if($v["is_recom"] == 1): ?><i class="fa fa-star" aria-hidden="true"  style="font-size: 14px;color:#ff6c60;"></i>
									<?php else: ?>
									<i class="fa fa-star" aria-hidden="true"  style="font-size: 14px;color:#999;"></i><?php endif; ?>
							</a>
						</td> -->
						<td>
							<a class="bg_g block_a edit_item" href="javascript:;" my_url="<?php echo U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']));?>" title="编辑" >
								<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
							</a>
							&nbsp;
							<a class="bg_r block_a del_item" href="javascript:;" my_url="<?php echo U('del', array('id'=>$v['id']));?>" title="删除" >
								<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
							</a>
			<!--
						&nbsp;&nbsp;
						<?php if($v["is_hot"] == 1): ?><a href="<?php echo U('runHot', array('id'=>$v['id']));?>" style="color:red;">荐</a>
						<?php else: ?>
							<a href="<?php echo U('runHot', array('id'=>$v['id']));?>">荐</a><?php endif; ?>
					<?php if(strpos(',201,202,203,204,205,206,', ','.$v['cat_id'].',') !== false): ?>&nbsp;&nbsp;
						<?php if($v["is_hidden"] == 1): ?><a href="<?php echo U('runHidden', array('id'=>$v['id']));?>" style="color:red;">隐</a>
						<?php else: ?>
							<a href="<?php echo U('runHidden', array('id'=>$v['id']));?>">隐</a><?php endif; endif; ?>
						&nbsp;&nbsp;
						<?php if($v["is_new"] == 1): ?><a href="<?php echo U('runNew', array('id'=>$v['id']));?>" style="color:red;">新</a>
						<?php else: ?>
							<a href="<?php echo U('runNew', array('id'=>$v['id']));?>">新</a><?php endif; ?>
						&nbsp;&nbsp;
						<?php if($_GET['jujiao'] == 1): if($v["is_jujiao"] == 1): ?><a href="<?php echo U('runJujiao', array('id'=>$v['id']));?>" style="color:red;" >聚焦</a>
							<?php else: ?>
								<a href="<?php echo U('runJujiao', array('id'=>$v['id']));?>" >聚焦</a><?php endif; endif; ?>
						&nbsp;&nbsp;
						<?php if($_GET['toutiao'] == 1): if($v["is_toutiao"] == 1): ?><a href="<?php echo U('runToutiao', array('id'=>$v['id']));?>" style="color:red;" >头条</a>
							<?php else: ?>
								<a href="<?php echo U('runToutiao', array('id'=>$v['id']));?>" >头条</a><?php endif; endif; ?> -->
						<?php if($is_jicheng == 1): ?><select name="status" id="status" class="status_sele" myid="<?php echo ($v["id"]); ?>" style="padding:0;height:30px;width:80px;">
								<option value="">待审核</option>
								<option value="2" <?php if($v["status"] == 2): ?>selected="true"<?php endif; ?>>已发布</option>
								<option value="3" <?php if($v["status"] == 3): ?>selected="true"<?php endif; ?>>审核失败</option>
							</select><?php endif; ?>
					</td>
				</tr><?php endforeach; endif; ?>
		</tbody>
	</table>
</form>
<div class="pager mt_10">
	<?php echo ($page); ?>
</div>
</div>
<script>
	$('#del_click').click(function(){
		if(!$('#fm1 input[name="id[]"]').is(':checked')){
			alert('请选择要删除的选项');
			return false;
		}else{
			if(confirm('确认删除?')){
				$.post('<?php echo U("delAll");?>', $('#fm1').serialize(), function(d){
					if(d.code==1){
						history.go(0);
					}
				},'json');
			}
		}
	});
	$('#checkAll').click(function(){
		$('#fm1 input[type=checkbox]').prop('checked', $(this).find('input[type=checkbox]').prop('checked'));
	});
	$('.status_sele').change(function(){
		if($(this).val()!=''){
			$.getJSON('<?php echo U("runCheck");?>', {id:$(this).attr('myid'), status:$(this).val()}, function(d){
				if(d.code==1){
					history.go(0);
				}
			});
		}
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.recom_click').click(function(){
		$.getJSON('<?php echo U("runRecom");?>',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.recom_click2').click(function(){
		$.getJSON('<?php echo U("runRecom2");?>',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.order_blur').blur(function(){
		$.getJSON('<?php echo U("runOrder");?>',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});
</script>
</body>
</html>