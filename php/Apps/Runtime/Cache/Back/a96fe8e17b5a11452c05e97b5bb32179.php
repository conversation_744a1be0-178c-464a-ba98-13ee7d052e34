<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html lang="zh-CN">

<head>

<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">



<script type="text/javascript" src="/Public/js/jquery.validate.min.js"></script>

<script type="text/javascript" src="/Public/js/jquery.form.js"></script>

</head>



	<div class="x-body">

		<form class="layui-form" >

				<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79 w_100">

					<tr><td style="width:200px;" >旧密码</td><td>

						<input placeholder="输入旧密码" class="layui-input" required name="old_password" type="password" lay-verify="required"/></td></tr>

					<tr><td >新密码</td><td>

						<input placeholder="输入新密码" class="layui-input" required name="password" id="password"  type="password" lay-verify="required"/></td></tr>

					<tr><td >新密码</td><td>

						<input placeholder="再次输入新密码" class="layui-input" required name="password2" id="password2" type="password" lay-verify="required"/></td></tr>

					<tr><td></td><td>

						 <button class="layui-btn" lay-submit lay-filter="formDemo">保存</button>

					</td></tr>

				</table>

			</form>





		<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('Tools/runUpload');?>">

			<input type="file" name="thumbs_hide" id="thumbs_hide" />

		</form>



	</div>



<script>

//Demo

layui.use('form', function(){

  var form = layui.form;



  //监听提交

  form.on('submit(formDemo)', function(data){

  	if($('#password').val()!=$('#password2').val()){

  		layer.alert('两次密码不一致');

  		return false;

  	}



  	$.post('<?php echo U("runPassword");?>',data.field, function(d){

    				if(d.code==1){

    					layer.msg(d.msg,function(){

    						history.go(0);

    					});

    				}else{

    					layer.msg(d.msg);

    				}

    			},'json');



    // layer.msg(JSON.stringify(data.field));

    return false;

  });

});

</script>





</body>

</html>