<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
	<div class="x-body">
	<form id="fm1"  class="layui-form" method="post">
	<input type="hidden" name="id" value="<?php echo ($res["id"]); ?>" />
		<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79 w_100" >
			<tr><td style="width:100px;">管理员名称：</td><td><input name="name" value="<?php echo ($res["name"]); ?>" lay-verify="required" class="layui-input"/> </td></tr>
			<tr><td>密码：</td><td><input type="password"  placeholder="不修改密码请留空" name="pwd"  class="layui-input"/> </td></tr>
			<tr style="display: none;"><td>权限列表：</td><td>
				<?php if(is_array($leftNav)): foreach($leftNav as $key=>$v): ?><div  style="margin-top:10px;font-size: 14px;color: #333;"><?php echo ($v["name"]); ?></div>
						<?php if(is_array($v["child"])): foreach($v["child"] as $key=>$v2): ?><div style="margin:5px 0 0 0;float:left;">
									<?php  $cat1_id = 'cat1_'.$v['id']; $cat2_id = $res['permission'][$cat1_id]; $cat2 = explode(',', $cat2_id); if(in_array($v2['id'], $cat2)){ ?>
										<input type="checkbox" name="cat1_<?php echo ($v["id"]); ?>[]" value="<?php echo ($v2["id"]); ?>" checked="true" title="<?php echo ($v2["name"]); ?>"  />
									<?php }else{ ?>
										<input type="checkbox" name="cat1_<?php echo ($v["id"]); ?>[]" value="<?php echo ($v2["id"]); ?>" title="<?php echo ($v2["name"]); ?>" checked="true" />
									<?php } ?>
							</div><?php endforeach; endif; ?>
						<div style="clear:both;"></div><?php endforeach; endif; ?>
			</td></tr>
			<tr><td></td><td><input type="submit" value=" 保存 " lay-submit lay-filter="formDemo"  class="layui-btn" /></td></tr>
		</table>
	</form>
</div>
<script>
  layui.use('form', function(){
  var form = layui.form;
  //监听提交
  form.on('submit(formDemo)', function(data){
  	// console.log(data);
    $.post("<?php echo U('runEdit');?>",$('#fm1').serialize(), function(d){
    	if(d.code == 1){
    		layer.alert("保存成功", {icon: 6},function () {
    			window.parent.location.reload();
    				var index = parent.layer.getFrameIndex(window.name);
	                //关闭当前frame
	                parent.layer.close(index);
            });
    	}
    },'json');
      return false;
  });
});
</script>
</body>
</html>