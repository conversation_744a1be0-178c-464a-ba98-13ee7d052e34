<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


</head>
<body>
	<div   class="x-body">
		<button id="add_click" class="layui-btn" ><i class="layui-icon"></i>添加</button>
		<script>
			$('#add_click').click(function(){
				var aa = "<?php echo U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']));?>";
				x_admin_show('添加内容', aa);
			});
		</script>

<!-- 		<div>
			<form id="fm1" >
				<input type="" name="key" id="key" placeholder="输入名称查询ID" class="layui-input dis_ib ver_mid"  style="width:200px;" />
				<input type="submit" value="搜索" class="layui-btn dis_ib ver_mid" />
				<span id="id_span"></span>
			</form>

			<script type="text/javascript">
				$('#fm1').submit(function(e){
					e.preventDefault();
					if($('#key').val()==''){
						alert('请输入名称');
						return false;
					}

					$.getJSON('<?php echo (U("searchID"));?>', {key:$('#key').val()}, function(d){
						console.log(d)
						$('#id_span').text('ID：'+d.id);
					});
				});
			</script>
		</div> -->

		<table  class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr>
					<th class="al_lt">名称</th>
					<th class="al_lt">排序</th>
					<th class="al_lt">操作</th>
				</tr>
			</thead>
			<tbody>
				<?php if(is_array($allCat)): foreach($allCat as $key=>$v): if($v['id'] != 186 and $v['pid'] != 186): ?><tr>
							<td><?php echo ($v["str"]); ?>
								<a href="#." my_url="<?php echo U('edit',array('id'=>$v['id']));?>" class="edit_item <?php if($v["pid"] == 0): ?>clr_r<?php endif; ?>"  title="编辑" style="font-size:14px;">
									<?php echo ($v["name"]); ?>
								</a>
								（ID：<?php echo ($v["id"]); ?>）
							</td>
							<td>
								<input class="layui-input order_blur" myid="<?php echo ($v["id"]); ?>" value="<?php echo ($v["order_id"]); ?>" style="width:50px;" />
								<input name="id[]" value="<?php echo ($v["id"]); ?>" type="hidden" />
							</td>
							<td>
								<a href="javascript:;" my_url="<?php echo U('add',array('id'=>$v['id']));?>" class="add_item" title="添加" ><i class="layui-icon">&#xe654;</i></a>
								<a href="javascript:;" my_url="<?php echo U('edit',array('id'=>$v['id']));?>" class="edit_item" title="编辑" ><i class="layui-icon">&#xe642;</i></a>
								<a href="javascript:;" my_url="<?php echo U('del', array('id'=>$v['id']));?>" class="del_item" title="删除" ><i class="layui-icon">&#xe640;</i></a>
							</td>
						</tr><?php endif; endforeach; endif; ?>
			</tbody>
		</table>
		<script>
			$('.add_item').click(function(){
				x_admin_show('添加',$(this).attr('my_url'));
			});
			$('.edit_item').click(function(){
				x_admin_show('编辑',$(this).attr('my_url'));
			});
			$('.del_item').click(function(){
				var aa = $(this);
				layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
					$.getJSON(aa.attr('my_url'), function(d){
						if(d.code==1){
							history.go(0);
						}else{
							alert(d.msg);
						}
					});
					layer.close(index);
				});
			});
			$('.order_blur').blur(function(){
				if($(this).val()!=''){
					$.getJSON('<?php echo U("runOrder");?>', {id:$(this).attr('myid'), order_id:$(this).val()}, function(d){
						if(d.code==1){
							history.go(0);
						}
					});
				}
			});
		</script>
	</div>
</body>
</html>