<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">

<title>后台登录</title>

<meta name="renderer" content="webkit|ie-comp|ie-stand">

<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

<meta http-equiv="Cache-Control" content="no-siteapp" />

<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

<link rel="stylesheet" href="/Public/xadmin/css/font.css">

<link rel="stylesheet" href="/Public/xadmin/css/xadmin.css">

<script type="text/javascript" src="/Public/xadmin/js/jquery.min.js"></script>

<script src="/Public/xadmin/lib/layui/layui.js" charset="utf-8"></script>

<script type="text/javascript" src="/Public/xadmin/js/xadmin.js"></script>

<link rel="stylesheet" href="/Public/css/back2.css">

<link rel="stylesheet" href="/Public/font-awesome/css/font-awesome.min.css">


	<script type="text/javascript" src="/Public/js/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/Public/js/jquery.form.js"></script>
	<script>
		$(function(){
//上传
$('#uploadForm').ajaxForm({
	dataType: 'json',
	success: function(data){
		if(data.up_msg=='OK'){
			data.up_msg = '上传成功！';
			$('.img_sample'+$('#witch_upload').val()).attr('src','/Attached/'+data.img_url);
			$('#thumbs'+$('#witch_upload').val()).val(data.img_url);
		}
		$('.tips'+$('#witch_upload').val()).text(data.up_msg);
	},
	beforeSubmit:function(){
		$('.tips'+$('#witch_upload').val()).text('正在上传……');
	}
});
$('.go_up').click(function(){
	$('#witch_upload').val($(this).attr('myid'));
	$('#thumbs_hide').click();
});
$('#thumbs_hide').change(function(){
	$('#uploadForm').submit();
});
});
</script>
</head>
<div class="x-body">
	<form action="<?php echo U('runAdd');?>" method="post"  id="fm1">
		<input id="witch_upload" type="hidden" />
		<table  class="tbl3 bg_f w_100 rad_10 mt_10 clr_79" width="100%">
			<?php if(is_array($lists)): foreach($lists as $key=>$v): if($v["type"] == 'image'): ?><tr><td class="al_rt"><?php echo ($v["key"]); ?></td>
						<td>
							<?php if($v["value"] != ''): ?><!-- <a href="/Attached/<?php echo ($v["value"]); ?>" target="_blank" title="查看原图"><img src="/Attached/<?php echo ($v["value"]); ?>" class="img_sample<?php echo ($v["id"]); ?> ver_mid" style="width:80px;height:80px;"/></a> -->
								<a href="/Attached/<?php echo ($v["value"]); ?>" target="_blank" title="查看原图">/Attached/<?php echo ($v["value"]); ?></a>
								<?php else: ?>
								<!-- <img src="/Public/images/back/no_pic.gif" class="img_sample<?php echo ($v["id"]); ?> ver_mid" style="width:80px;height:80px;"/> --><?php endif; ?>
							<input name="<?php echo ($v["id"]); ?>" id="thumbs<?php echo ($v["id"]); ?>"  value="<?php echo ($v["value"]); ?>" type="hidden"  />
							<input type="button" class="go_up layui-btn" myid="<?php echo ($v["id"]); ?>" value="上传"  />
							<!-- <a href="<?php echo U('delImg', array('id'=>$v['id']));?>" class="layui-btn" onclick="return confirm('确认删除？');">删除</a> -->
							<span class="tips<?php echo ($v["id"]); ?>" style="color:#999;">格式：<?php echo ($v["desc"]); ?></span>
						</td>
					</tr>
					<?php else: ?>
					<tr><td class="al_rt" width="200"><?php echo ($v["key"]); ?></td><td>
						<?php if($v["type"] == 'input'): ?><input name="<?php echo ($v["id"]); ?>"  value="<?php echo ($v["value"]); ?>"  class="layui-input" />
							<span class="clr_9  f_12"><?php echo ($v["desc"]); ?></span>
							<?php elseif($v["type"] == 'date'): ?>
							<input name="<?php echo ($v["id"]); ?>"  value="<?php echo ($v["value"]); ?>"  class="layui-input test_date"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'});"/>
							<span class="clr_9  f_12"><?php echo ($v["desc"]); ?></span>
							<?php elseif($v["type"] == 'textarea'): ?>
							<textarea name="<?php echo ($v["id"]); ?>"  class="layui-textarea"><?php echo ($v["value"]); ?></textarea>
							<span class="clr_9  f_12"><?php echo ($v["desc"]); ?></span><?php endif; ?>
					</td></tr><?php endif; endforeach; endif; ?>
			<tr><td></td><td><input type="submit" value=" 保存 " class="layui-btn" /></td></tr>
		</table>
	</form>
	<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="<?php echo U('runUpload');?>">
		<input type="file" name="thumbs_hide" id="thumbs_hide" />
	</form>
</div>
</body>
</html>