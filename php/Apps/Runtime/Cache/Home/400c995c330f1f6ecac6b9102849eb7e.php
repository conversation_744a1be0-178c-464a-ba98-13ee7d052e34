<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>
<head>
<title>棉花棠</title>
<meta name="keywords" content="棉花棠" />
<meta name="description" content="棉花棠" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script type="text/javascript" src="/Public/js/jquery-1.11.1.min.js"></script>

<script type="text/javascript" src="/Public/js/swiper.min.js"></script>

<link rel="stylesheet" type="text/css" href="/Public/css/css.css" />

<link rel="stylesheet" type="text/css" href="/Public/css/swiper.min.css">
<link rel="icon" type="image/x-icon" href="/Public/images/favicon.ico">
<style type="text/css">
.chaoshi_tbl th{text-align: center;}
</style>
</head>
<body class="bg_f2">
<div class="bg_f min_w">
	<div class="m_auto padt_15 clearfix ">
		<div class="fl "><a href="<?php echo U('Index/index');?>"><img src="/Public/images/logo.png" class="dis_b" style="height:78px;" /></a></div>
		<div class="fl ml_50 pos_rela"  >
			<div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box">
				<textarea style="width:400px;height: 150px;" id="pl_text" placeholder="每行输入一个批次/捆号" class="border1 dis_b pad_10"></textarea>
				<input type="button" id="pl_sou" value="搜索" class="dis_b bg_ora" style="color: #fff;padding:5px 10px;border:0" />
			</div>
			<form action="<?php echo U('Chaoshi/index');?>" id="ss_fm"  style="margin-top:9px;">
					<input type="search" name="sk" id="sousuo_sk" class="clr_9" value="<?php echo ($_GET['sk']); ?>" autocomplete="off" 
						placeholder="批号/轧花厂/仓库/产棉地区" style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;">
					<button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
						<img src="/Public/images/searth.png" style="width:20px;">
					</button>
					<span class="f_14 clr_r pointer" id="pl_click" style="position: absolute;top:30px;right:80px;">批量</span>
		
			</form>
		</div>

		<script>
		$('#pl_click').click(function(){
			$('#pl_box').toggle();
		});

			$('#pl_sou').click(function(){
				var ss = $('#pl_text').val().trim();
				var arr = ss.split(/\n/g);
				var xx = [];
				for(var i = 0; i<arr.length; i++){
					if(arr[i].trim()!=''){
						xx.push(arr[i].trim());
					}
				}
				// console.log(xx.toString());
				$('#sousuo_sk').val(xx.toString());
				$('#ss_fm').submit();
			});
		</script>


		<div class="fr al_rt f_14 login ">
			<?php if($session_user["id"] > 0): ?><div class="mt_10">
					<a href="<?php echo U('Center/info');?>" class="clr_b f_16 log_a">会员中心</a>
					<a href="<?php echo U('Passport/exits');?>" class="clr_b f_16">退出</a>
				</div>
			<?php else: ?>
				<div class="mt_10">
					<a href="<?php echo U('Passport/reg');?>" class="clr_d f_16">注册</a>
					<a href="<?php echo U('Passport/login');?>" class="clr_d f_16 log_a">登录</a>
				</div><?php endif; ?>
			
		</div>
	</div>
</div>


<div class="bg_b min_w">
	<div class="m_auto clearfix">
		<ul class="nav_ul clearfix f_16  fl">
			<li><a id="n1" href="<?php echo U('Index/index');?>" class="clr_f">每日精选</a></li>
			<li><a id="n2"  href="<?php echo U('Chaoshi/index');?>" class="clr_f">棉花商城</a></li>
			<li><a id="n3" href="<?php echo U('Center/dingzhi');?>" class="clr_f">个性需求</a></li>
 			<li><a id="n4" href="<?php echo U('Center/favor');?>" class="clr_f">我的收藏</a></li>
			<!--<li><a id="n5" href="<?php echo U('About/contact');?>" class="clr_f">联系我们</a></li> -->
		</ul>

		<div class="f_16 clr_f nav_time fr">欢迎您，今天是：<?php echo(date('Y年m月d日')); ?></div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		var h = location.href;
		if(h.indexOf('Chaoshi')>-1){
			$('#n2').addClass('nav_cur');
		}else if(h.indexOf('dingzhi')>-1){
			$('#n3').addClass('nav_cur');
		}else if(h.indexOf('favor')>-1){
			$('#n4').addClass('nav_cur');
			$('#left_3').addClass('nav_cur');
		}else if(h.indexOf('contact')>-1){
			$('#n5').addClass('nav_cur');
		}else if(h.indexOf('info')>-1){
			$('#left_1').addClass('nav_cur');
		}else {
			$('#n1').addClass('nav_cur');
		}
	});
</script>
	<div class="m_auto">
		<div class="bar">每日精选</div>
		<div style="overflow-x: auto;">
			<table class="chaoshi_tbl f_16 clr_3 w_100p mt_10 bg_f"  >
				<tr style="background: #28afe5;text-align: center;" class="clr_f">
					<th>批号</th>
					<th>类型</th>
					<th>颜色级</th>
					<th>马值</th>
					<th>长度</th>
					<th>强力</th>
					<th>含杂</th>
					<th>回潮</th>
					<th>公重</th>
					<th>整齐度</th>
					<th>加工厂</th>
					<th>仓库</th>
					<th>基差</th>
					<th>点价合约</th>
				</tr>
				<?php if(is_array($chaoshi)): foreach($chaoshi as $key=>$v): ?><tr style="text-align:center;">
						<td>
							<!--
								<div class="dis_ib mj1 f_14 ver_mid">
									<div class="h1">5星</div>
									<div class="h2">卖家</div>
								</div>
							-->
							<div class="dis_ib ver_mid"><a href="<?php echo U('Chaoshi/view', ['id'=>$v['id']]);?>" class="clr_b f_wei"><?php echo ($v["pihao_kunkao"]); ?></a></div>
						</td>
						<td><?php echo ($v["leixing"]); ?></td>
						<td ><div title="<?php echo ($v["yanseji_pinji"]); ?>"><?php echo (msubstr2($v["yanseji_pinji"],0,6)); ?></div></td>
						<td><?php echo ($v["mazhi"]); ?></td>
						<td><?php echo ($v["changdu"]); ?></td>
						<td><?php echo ($v["qiangli"]); ?></td>
						<td><?php echo ($v["hanzalv"]); ?></td>
						<td><?php echo ($v["huichaolv"]); ?></td>
						<td><?php echo ($v["gongzhong"]); ?></td>
						<td><?php echo ($v["zhengqidu"]); ?></td>
						<td ><div title="<?php echo ($v["jiagongchang"]); ?>"><?php echo (msubstr2($v["jiagongchang"],0,6)); ?></div></td>
						<td ><div title="<?php echo ($v["cangchumingcheng"]); ?>"><?php echo (msubstr2($v["cangchumingcheng"],0,6)); ?></div></td>
						<td><?php echo ($v["jicha"]); ?></td>
						<td><?php echo ($v["dianjiaheyue"]); ?></td>
					</tr><?php endforeach; endif; ?>
			</table>
		</div>
	</div>
 <div class="swiper-container pos_rela min_w"  style="    width: 1200px;
    margin: 0 auto;">
			<div class="swiper-wrapper">
				<?php if(is_array($bann)): foreach($bann as $key=>$v): ?><div class="swiper-slide" style="background: url(/Attached/<?php echo ($v["thumbs"]); ?>) no-repeat center 0;height:450px;background-size: auto 100%;">
						<?php if($v["url"] != ''): ?><a href="<?php echo ($v["url"]); ?>" target="_blank" class="dis_b" style="height:450px;"></a><?php endif; ?>
					</div><?php endforeach; endif; ?>
			</div>
			<!-- Add Pagination -->
				<div class="swiper-pagination"></div>
				<!-- Add Arrows -->
				<div class="swiper-button-prev"></div>
				<div class="swiper-button-next"></div>
	</div>
	<script>
		var swiper = new Swiper('.swiper-container', {
			nextButton: '.swiper-button-next',
			prevButton: '.swiper-button-prev',
			pagination: '.swiper-pagination',
			// paginationType: 'fraction',
			loop : true,
			autoplay : 4000,
		});
	</script>
	<div class="m_auto">
		<div class="bar ">个性需求</div>
		<!-- <a href="<?php echo U('Center/dingzhi');?>" class="mt_10 dis_b"><img src="/Public/images/xq.jpg" class="dis_b" /></a> -->
		<?php if(is_array($dingzhi)): foreach($dingzhi as $key=>$v): ?><div class="border1 bg_f pad_20 f_16 clr_3 clearfix mt_10">
			<div class="fl dz_left" style="width:960px;" >
			<!-- <?php echo (urldecode($v["url"])); ?> -->
				<a href="#" class="clr_b f_wei mr_10"><?php echo ($v["dingzhi_no"]); ?></a>
				<span class="clr_9 mr_10">类型：<span class="f_wei clr_3"> <?php echo ($v["leixing"]); ?></span></span>
				<span class="clr_9 mr_10">年度 ：<span class="clr_3"><?php echo ($v["pihao_kunkao"]); ?></span></span>
				<div class="mt_10 lh_30">
					<span class="clr_9 mr_10">长 度：<span class="clr_3"><?php echo ($v["changdu"]); ?></span></span>
					<span class="clr_9 mr_10">马值 ：<span class="clr_3"><?php echo ($v["mazhi"]); ?></span></span>
					<span class="clr_9 mr_10">强力 ：<span class="clr_3"><?php echo ($v["qiangli"]); ?></span></span>
					<span class="clr_9 mr_10">回潮 ：<span class="clr_3"><?php echo ($v["huichaolv"]); ?></span></span>
					<span class="clr_9 mr_10">整齐度：<span class="clr_3"><?php echo ($v["zhengqidu"]); ?></span></span>
					<span class="clr_9 mr_10">含杂：<span class="clr_3"><?php echo ($v["hanzalv"]); ?></span></span><br />
					<span class="clr_9 mr_10">白棉1/2/3级 ：<span class="clr_3"><?php echo ($v["mazhi"]); ?></span></span>
					<span class="clr_9 mr_10">白棉4/5级 ：<span class="clr_3"><?php echo ($v["qiangli"]); ?></span></span>
					<span class="clr_9 mr_10">淡点污1/2/3级 ：<span class="clr_3"><?php echo ($v["huichaolv"]); ?></span></span>
					<span class="clr_9 mr_10">淡黄染1/2/3级：<span class="clr_3"><?php echo ($v["zhengqidu"]); ?></span></span>
					<span class="clr_9 mr_10">黄染1/2级：<span class="clr_3"><?php echo ($v["hanzalv"]); ?></span></span><br />
					<span class="clr_9 mr_10">轧花厂： <span class="clr_3"><?php echo ($v["jiagongchang"]); ?></span></span>
					<span class="clr_9 mr_10">交货仓库：<span class="clr_3"><?php echo ($v["cangchumingcheng"]); ?></span></span><br />
				</div>
			</div>
			<div class="fr dz_right clearfix" style="width:160px;">
				<div style="text-align: right;">
					<!-- <a href="#" class="edit_btn f_14 dis_b">修改</a> -->
					<a href="<?php echo U('delDingzhi',['id'=>$v['id']]);?>" class="del_btn f_14 dis_ib mt_10" onclick="return confirm('确认删除？');">删除</a><br />
					<!-- <div class="f_16 clr_9">平台最优价格 <span class="f_wei clr_ora f_22">15700</span></div> -->
					<a href="<?php echo ($v["url"]); ?>" class="f_16 zy_box clr_ora  pos_rela mt_10 dis_ib">
						<!-- <div class="icon2">0</div> -->
						<img src="/Public/images/icon.png" class="ver_mid" /> <span class="ver_mid">查看资源</span>
					</a>
				</div>
			</div>
		</div><?php endforeach; endif; ?>
	</div>
<!--
	<div class="m_auto clearfix">
		<div class="fl" style="width:590px;">
			<div class="bar">关于我们</div>
			<div class="border1 bg_f pad_15 mt_10 f_16" style="min-height: 150px;">
				<?php echo ($our["content"]); ?>
			</div>
		</div>
		<div class="fr" style="width:590px;">
			<div class="bar">联系我们</div>
			<div class="border1 bg_f pad_15 mt_10 f_16" style="min-height: 150px;">
				<?php echo ($contact["content"]); ?>
			</div>
		</div>
	</div>
 -->
	<div class="footer mt_30 min_w">
		<div class="f_14 clr_9 al_ct padt_10 lh_28">

版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>



	  Copyright &copy; 2021 鲁ICP备2021017032号-1



 </div>
 
  
<div class="side">
	<ul>
		
		<li class="sideewm"><i class="bgs3"></i>官方微信
			<div class="ewBox son"></div>
		</li>
		<li class="sideetel"><i class="bgs4"></i>联系电话
			<div class="telBox son">
			
				<dd class="bgs2"><span>手机</span>19853227218</dd>
			</div>
		</li>
	
		<li class="sidetop" onClick="goTop()"><i class="bgs6"></i>返回顶部</li>
	</ul>
</div>

<script src="/Public/js/jquery-1.11.0.min.js" type="text/javascript" charset="utf-8"></script>
<script>
	function goTop() {
		$('html,body').animate({
			scrollTop: 0
		}, 500)
	}
</script>

 <style>
 .side{position:fixed;width:60px;right:0;top:60%;margin-top:-200px;z-index:100;border:1px solid #e0e0e0;background:#fff;border-bottom:0}
.side ul li{width:60px;height:70px;float:left;position:relative;border-bottom:1px solid #e0e0e0;color:#333;font-size:14px;line-height:38px;text-align:center;transition:all .3s;cursor:pointer}
.side ul li:hover{background:#f67524;color:#fff}
.side ul li:hover a{color:#fff}
.side ul li i{height:25px;margin-bottom:1px;display:block;overflow:hidden;background-repeat:no-repeat;background-position:center center;background-size:auto 25px;margin-top:14px;transition:all .3s}
.side ul li i.bgs1{background-image:url(/Public/images/right_pic5.png)}
.side ul li i.bgs2{background-image:url(/Public/images/right_pic7.png)}
.side ul li i.bgs3{background-image:url(/Public/images/right_pic2.png)}
.side ul li i.bgs4{background-image:url(/Public/images/right_pic1.png)}
.side ul li i.bgs5{background-image:url(/Public/images/right_pic3.png)}
.side ul li i.bgs6{background-image:url(/Public/images/right_pic6_on.png)}
.side ul li:hover i.bgs1{background-image:url(/Public/images/right_pic5_on.png)}
.side ul li:hover i.bgs2{background-image:url(/Public/images/right_pic7_on.png)}
.side ul li:hover i.bgs3{background-image:url(/Public/images/right_pic2_on.png)}
.side ul li:hover i.bgs4{background-image:url(/Public/images/right_pic1_on.png)}
.side ul li:hover i.bgs5{background-image:url(/Public/images/right_pic3_on.png)}
.side ul li .sidebox{position:absolute;width:78px;height:78px;top:0;right:0;transition:all .3s;overflow:hidden}
.side ul li.sidetop{background:#f67524;color:#fff}
.side ul li.sidetop:hover{opacity:.8;filter:Alpha(opacity=80)}
.side ul li.sideewm .ewBox.son{width:238px;display:none;color:#363636;text-align:center;padding-top:212px;position:absolute;left:-240px;top:0;background-image:url(/Public/images/leftewm.png);background-repeat:no-repeat;background-position:center center;border:1px solid #e0e0e0}
.side ul li.sideetel .telBox.son{width:240px;height:214px;display:none;color:#fff;text-align:left;position:absolute;left:-240px;top:-72px;background:#f67524}
.side ul li.sideetel .telBox dd{display:block;height:118.5px;overflow:hidden;padding-left:82px;line-height:24px;font-size:18px}
.side ul li.sideetel .telBox dd span{display:block;line-height:28px;height:28px;overflow:hidden;margin-top:32px;font-size:18px}
.side ul li.sideetel .telBox dd.bgs1{background:url(/Public/images/right_pic8.png) 28px center no-repeat;background-color:#e96410}
.side ul li.sideetel .telBox dd.bgs2{background:url(/Public/images/right_pic9.png) 28px center no-repeat}
.side ul li:hover .son{display:block!important;animation:fadein 1s}
@keyframes fadein{from{opacity:0}
to{opacity:1}
}
</style>
	</div>
</body>
</html>