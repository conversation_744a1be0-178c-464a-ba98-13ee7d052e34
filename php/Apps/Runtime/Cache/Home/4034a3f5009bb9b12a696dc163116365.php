<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html lang="en">
	<head>
		<title>在线留言</title>

<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script type="text/javascript" src="/Public/js/jquery-1.11.1.min.js"></script>

<script type="text/javascript" src="/Public/js/swiper.min.js"></script>

<link rel="stylesheet" type="text/css" href="/Public/css/css.css" />

<link rel="stylesheet" type="text/css" href="/Public/css/swiper.min.css">
<link rel="icon" type="image/x-icon" href="/Public/images/favicon.ico">
	</head>
	<body>
		
			<div class="dislpayArrow"><a class="pngfix" href="javascript:void(0);" onclick="displaynavbar(this)"></a></div>
		<div class="content">
			<div class="banner">
				<img src="/Attached/<?php echo ($bann[0]["thumbs"]); ?>" alt="">
			</div>
			<div class="w1200">
			<div class="onLine-xy col12">
					<div class="cont_title wow bounceInUp"  data-wow-duration="2s" data-wow-delay="0.5s" style="width: 450px;">
							<p>把您的需求告诉我们 <span>请在此留下您的宝贵意见</span></p>
					</div>
					<div class="of_onLine col12 wow bounceInUp"  data-wow-duration="2.5s" data-wow-delay="0.5s">
							<p>我们时刻关注客户需求，并做好随时能够与您洽谈业务的准备。
									欢迎您来电咨询，索取专业资料或者预约见面时间。</p>
						<form  id="fm1">
							<div class="col12">
								<div class="form-group col5">
									<label for=""><span class="red">*</span> 姓名：</label>
									<input type="text" name="name" id="name" class="form-control" placeholder="请输入姓名">
								</div>
								<div class="form-group col5 fr">
									<label for=""> 城市：</label>
									<input type="text" name="city"   id="city" class="form-control" placeholder="请输入城市">
								</div>
							</div>
							<div class="col12">
									<div class="form-group col5">
										<label for=""><span class="red">*</span> 邮箱：</label>
										<input type="text" name="email"  id="email"  class="form-control" placeholder="请输入您的邮箱">
									</div>
									<div class="form-group col5 fr">
										<label for=""><span class="red">*</span> 手机：</label>
										<input type="text" name="mobile"  id="mobile"  class="form-control" placeholder="请输入手机号码">
									</div>
								</div>
								<div class="col12">
									<div class="form-group col12">
										<label for="" style="position: relative;top:-70px";><span class="red">*</span> 内容：</label>
										<textarea name="content" id="content"   class="form-control" rows="3" placeholder=""></textarea>
									</div>
								</div>
							<div class="col12">
									<div class="form-group col6">
										<label for=""> 验证码：</label>
										<input type="text"  name="captcha" id="captcha"  class="form-control" style="width: 126px;" placeholder="">
										<a href="javascript:;"  class="code"><img src="<?php echo U('Tools/verify');?>" id="verify_img" alt=""></a>
									</div>
								</div>
								<div class="col12" style="text-align: center;margin-top:37px;">
									<button type="reset" class="btn btn-yl">重置</button>
									<button type="submit" class="btn btn-default">提交</button>
								</div>
						</form>	


						<script>
						$('#verify_img').click(function(){
							$(this).attr('src', "<?php echo U('Tools/verify');?>");
						});
						$('#fm1').submit(function(e){
							e.preventDefault();

							if(/^[ ]*$/.test($('#name').val())){
								alert('请输入姓名');
								return false;
							}

							if(!/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test($('#email').val())){
								alert('请输入正确的邮箱');
								return false;
							}


							if(!/^1\d{10}$/.test($('#mobile').val())){
								alert('请输入正确的手机号');
								return false;
							}


							if(/^[ ]*$/.test($('#content').val())){
								alert('请输入内容');
								return false;
							}


							if(/^[ ]*$/.test($('#captcha').val())){
								alert('请输入验证码');
								return false;
							}


							$.post('<?php echo U("runMsg");?>', $('#fm1').serialize(), function(d){
								alert(d.msg);
								if(d.code==1){
									history.go(0);
								}else{
									$('#verify_img').click();
								}
							},'json');
						});
						</script>
					</div>
					
			</div>
			
		</div>
			<div class="f_14 clr_9 al_ct padt_10 lh_28">

版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>



	  Copyright &copy; 2021 鲁ICP备2021017032号-1



 </div>
 
  
<div class="side">
	<ul>
		
		<li class="sideewm"><i class="bgs3"></i>官方微信
			<div class="ewBox son"></div>
		</li>
		<li class="sideetel"><i class="bgs4"></i>联系电话
			<div class="telBox son">
			
				<dd class="bgs2"><span>手机</span>19853227218</dd>
			</div>
		</li>
	
		<li class="sidetop" onClick="goTop()"><i class="bgs6"></i>返回顶部</li>
	</ul>
</div>

<script src="/Public/js/jquery-1.11.0.min.js" type="text/javascript" charset="utf-8"></script>
<script>
	function goTop() {
		$('html,body').animate({
			scrollTop: 0
		}, 500)
	}
</script>

 <style>
 .side{position:fixed;width:60px;right:0;top:60%;margin-top:-200px;z-index:100;border:1px solid #e0e0e0;background:#fff;border-bottom:0}
.side ul li{width:60px;height:70px;float:left;position:relative;border-bottom:1px solid #e0e0e0;color:#333;font-size:14px;line-height:38px;text-align:center;transition:all .3s;cursor:pointer}
.side ul li:hover{background:#f67524;color:#fff}
.side ul li:hover a{color:#fff}
.side ul li i{height:25px;margin-bottom:1px;display:block;overflow:hidden;background-repeat:no-repeat;background-position:center center;background-size:auto 25px;margin-top:14px;transition:all .3s}
.side ul li i.bgs1{background-image:url(/Public/images/right_pic5.png)}
.side ul li i.bgs2{background-image:url(/Public/images/right_pic7.png)}
.side ul li i.bgs3{background-image:url(/Public/images/right_pic2.png)}
.side ul li i.bgs4{background-image:url(/Public/images/right_pic1.png)}
.side ul li i.bgs5{background-image:url(/Public/images/right_pic3.png)}
.side ul li i.bgs6{background-image:url(/Public/images/right_pic6_on.png)}
.side ul li:hover i.bgs1{background-image:url(/Public/images/right_pic5_on.png)}
.side ul li:hover i.bgs2{background-image:url(/Public/images/right_pic7_on.png)}
.side ul li:hover i.bgs3{background-image:url(/Public/images/right_pic2_on.png)}
.side ul li:hover i.bgs4{background-image:url(/Public/images/right_pic1_on.png)}
.side ul li:hover i.bgs5{background-image:url(/Public/images/right_pic3_on.png)}
.side ul li .sidebox{position:absolute;width:78px;height:78px;top:0;right:0;transition:all .3s;overflow:hidden}
.side ul li.sidetop{background:#f67524;color:#fff}
.side ul li.sidetop:hover{opacity:.8;filter:Alpha(opacity=80)}
.side ul li.sideewm .ewBox.son{width:238px;display:none;color:#363636;text-align:center;padding-top:212px;position:absolute;left:-240px;top:0;background-image:url(/Public/images/leftewm.png);background-repeat:no-repeat;background-position:center center;border:1px solid #e0e0e0}
.side ul li.sideetel .telBox.son{width:240px;height:214px;display:none;color:#fff;text-align:left;position:absolute;left:-240px;top:-72px;background:#f67524}
.side ul li.sideetel .telBox dd{display:block;height:118.5px;overflow:hidden;padding-left:82px;line-height:24px;font-size:18px}
.side ul li.sideetel .telBox dd span{display:block;line-height:28px;height:28px;overflow:hidden;margin-top:32px;font-size:18px}
.side ul li.sideetel .telBox dd.bgs1{background:url(/Public/images/right_pic8.png) 28px center no-repeat;background-color:#e96410}
.side ul li.sideetel .telBox dd.bgs2{background:url(/Public/images/right_pic9.png) 28px center no-repeat}
.side ul li:hover .son{display:block!important;animation:fadein 1s}
@keyframes fadein{from{opacity:0}
to{opacity:1}
}
</style>
		</div>
	</body>
</html>