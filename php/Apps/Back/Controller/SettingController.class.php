<?php

namespace Back\Controller;

use Think\Controller;

use Vendor\Unlimit;



class SettingController extends CommController {

	public function _initialize(){

		parent::_initialize();

	}

	

	public function index(){

		$this->lists = M('Setting')->order('id asc')->select();

		$this->display();

	}

	



	

	public function runAdd(){

		foreach($_POST as $k=>$v){

			M('Setting')->where(array('id'=>$k))->save(array('value'=>$v));	

		}

		redirect($_SERVER['HTTP_REFERER']);

	}

	

	public function delImg(){

			$res = M('Setting')->find(I('get.id'));

			if($res['value']!=''){

					@unlink(realpath('.').'/Attached/'.$res['value']);

			}

			M('Setting')->where(array('id'=>$res['id']))->save(array('value'=>''));	

			redirect($_SERVER['HTTP_REFERER']);

	}

	

	

	public function runUpload(){

		$upload = new \Think\Upload();

		$upload->maxSize   =     10*1000*1000;// 2m

	    $upload->exts      =     array('jpg', 'gif', 'png', 'jpeg', 'mp4');// 设置附件上传类型

	    $upload->rootPath  =     './Attached/'; // 设置附件上传根目录

	    $upload->savePath  =     'thumbs/'; // 设置附件上传（子）目录

		

		//上传文件

		$info   =   $upload->upload();

		if(!$info) {// 上传错误提示错误信息

			$tips = $upload->getError();

		}else{

			$tips = "OK";

		}

		

		$retArr = array(

			'img_url'=> $info['thumbs_hide']['savepath'].$info['thumbs_hide']['savename'],

			'up_msg'=> $tips,

		);

		

		echo(json_encode($retArr));

	}

	

		public function password(){

		$this->display();	

	}

	

	public function runPassword(){

		$res = M('Admin')->find(session('uid_admin'));

		if(md5(I('post.old_password')) != $res['pwd']){

			die(json_encode(array('code'=>0, 'msg'=>'旧密码错误')));	

		}else{

			M('Admin')->where(array('id'=>$res['id']))->save(array('pwd'=>md5(I('post.password'))));	

			die(json_encode(array('code'=>1, 'msg'=>'密码修改成功')));	

		}

	}

	

}