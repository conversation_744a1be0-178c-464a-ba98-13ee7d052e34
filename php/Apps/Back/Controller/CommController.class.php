<?php
namespace Back\Controller;
use Think\Controller;
class CommController extends Controller{
	public function _initialize(){
		if(session('uid_admin')){
			$session_admin =  M('Admin')->find(session('uid_admin'));
			$session_admin['permission'] = unserialize($session_admin['permission']);
			if($session_admin['permission']['setting']!=''){
				$session_admin['setting'] = explode(',', $session_admin['permission']['setting']);
			}
			$this->session_admin = $session_admin;
		}
		// print_r($this->session_admin);
		if(session('?uid_admin')){
			//30分钟
			if((time()- session('login_time_admin'))> 30*60){
				session('uid_admin', null);
				session('login_time_admin', null);
				// $this->redirect('Index/index');
				echo('<script>parent.location.href="'.U('Index/index').'"</script>');
			}else{
				session('login_time_admin', time());
			}
		}else{
			// $this->redirect('Index/index');
			echo('<script>parent.location.href="'.U('Index/index').'"</script>');
		}
		// require('Unlimit.class.php');
		// 产品中心
		// $product = M('Cat')->where(array('pid'=>142))->order('order_id desc, id asc')->select();
		// foreach($product as $k=>$v){
		// 	$product[$k]['url'] = U('Xiangce/index',array('cid'=>$v['id']));
		// }
		// // 工程案例
		// $case = M('Cat')->where(array('pid'=>163))->order('order_id desc, id asc')->select();
		// foreach($case as $k=>$v){
		// 	$case[$k]['url'] = U('Art/index',array('cid'=>$v['id']));
		// }
		// $cat1 = array(
		// 	array(
		// 		'id'=>1,
		// 		'name'=>'产品中心',
		// 		'icon'=>'&#xe705;',
		// 		'child'=>$product
		// 	),
		// 	array(
		// 		'id'=>2,
		// 		'name'=>'工程案例',
		// 		'icon'=>'&#xe705;',
		// 		'child'=>$case
		// 	)
		// );
		$leftNav = array(
			array(
					'id'=>7,
						'name'=>'内容管理',
						'icon'=>'<i class="fa fa-list-alt" aria-hidden="true"></i>',
						'child'=>array(
							// array(
							// 	'id'=>707,
							// 	'name'=>'关于我们',
							// 	'url'=>U('Art/index', array('cid'=>707)),
							// ),
							// array(
							// 	'id'=>701,
							// 	'name'=>'联系我们',
							// 	'url'=>U('Art/index', array('cid'=>701)),
							// ),
							array(
								'id'=>702,
								'name'=>'用户协议',
								'url'=>U('Art/index', array('cid'=>702)),
							),
							array(
								'id'=>703,
								'name'=>'棉花商城',
								'url'=>U('Chaoshi/index', array('cid'=>703)),
							),

							array(
								'id'=>704,
								'name'=>'用户管理',
								'url'=>U('User/index', array('cid'=>704)),
							),
							array(
								'id'=>705,
								'name'=>'工厂管理',
								'url'=>U('Gongchang/index', array('cid'=>705)),
							),
							array(
								'id'=>706,
								'name'=>'仓库管理',
								'url'=>U('Cangku/index', array('cid'=>706)),
							),
							array(
								'id'=>707,
								'name'=>'会员管理',
								'url'=>U('User/index', array('cid'=>707)),
							),
							array(
								'id'=>708,
								'name'=>'首页导入',
								'url'=>U('Shouye/index', array('cid'=>708)),
							),
							array(
								'id'=>709,
								'name'=>'删除文件',
								'url'=>U('Wenjian/index', array('cid'=>709)),
							),
						)
				),
				array(
					'id'=>3,
						'name'=>'系统设置',
						'icon'=>'<i class="fa fa-cog" aria-hidden="true"></i>',
						'child'=>array(
							array(
								'id'=>301,
								'icon'=>'<i class="fa fa-cog" aria-hidden="true"></i>',
								'name'=>'参数设置',
								'url'=>U('Setting/index'),
							),
							array(
								'id'=>302,
								'icon'=>'<i class="fa fa-unlock-alt" aria-hidden="true"></i>',
								'name'=>'修改密码',
								'url'=>U('Setting/password'),
							),
							array(
								'id'=>303,
								'icon'=>'<i class="fa fa-key" aria-hidden="true"></i>',
								'name'=>'管理员分配',
								'url'=>U('Permission/index'),
							),
							array(
								'id'=>304,
								'icon'=>'<i class="fa fa-file-image-o" aria-hidden="true"></i>',
								'name'=>'广告管理',
								'url'=>U('Adv/index'),
							),
							array(
								'id'=>305,
								'icon'=>'<i class="fa fa-mars" aria-hidden="true"></i>',
								'name'=>'分类管理',
								'url'=>U('Cat/index'),
							),
							array(
								'id'=>306,
								'icon'=>'<i class="fa fa-mars" aria-hidden="true"></i>',
								'name'=>'交货地管理',
								'url'=>U('Jiaohuodi/index'),
							),
							array(
								'id'=>307,
								'icon'=>'<i class="fa fa-mars" aria-hidden="true"></i>',
								'name'=>'登录流水',
								'url'=>U('Denglu/index'),
							),
							// array(
							// 	'id'=>307,
							// 	'icon'=>'<i class="fa fa-files-o" aria-hidden="true"></i>',
							// 	'name'=>'数据备份',
							// 	'url'=>U('Bak/index'),
							// ),
						)
				),
		);
		// print_r($leftNav);
		// $this->leftNav = array_merge($cat1, $leftNav);
		$this->leftNav = array_merge($leftNav);
	}
}
?>