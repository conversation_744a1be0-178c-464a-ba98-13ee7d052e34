<?php
namespace Back\Controller;
use Think\Controller;

class OrderController extends CommController {
	
	public function _initialize(){
		parent::_initialize();
	}

	function fahuo(){
		if(M('Order_pay')->where(['id'=>I('get.id')])->getField('is_fahuo') == 0){
			M('Order_pay')->where(['id'=>I('get.id')])->save(['is_fahuo'=>1]);
		}
		redirect($_SERVER['HTTP_REFERER']);
	}


	public function toExcel(){
		$where = '`id` > 0';
		if(I('get.key')){
			$where .= ' and (`order_no` like "%'.I('get.key').'%" or `address_name` like "%'.I('get.key').'%" or `address_mobile` like "%'.I('get.key').'%")';
		}

		if(I('get.start_time')){
			$where .= ' and `add_time` >'.strtotime(I('get.start_time'));
		}

		if(I('get.end_time')){
			$where .= ' and `add_time` <'.strtotime(I('get.end_time'));
		}


		// print_r($where);

		$count =  M('Order_pay')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Order_pay')->where($where)->order('pay_time desc, add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){

			$lists[$k]['uid_text']= M('user')->where(['id'=>$v['uid']])->getField('nickName');
			$lists[$k]['coupon'] = M('Coupon')->where(['id'=>$v['coupon_id']])->find();
			$lists[$k]['xiangxi']= M('Order')->where(['order_no'=>$v['order_no']])->select();
		}

		// print_r($lists);
		// $this->lists = $lists;

		header("Content-Type: application/vnd.ms-excel;charset=UTF-8");
		header("Content-Disposition: attachment; filename=".iconv('utf-8', 'gb2312', '订单管理_'). date('Y-m-d').".xls");
		header("Pragma: no-cache");
		header("Expires: 0");
		echo("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/>");
		echo('<table border=1>');
		echo('<tr><th>订单号</th><th>价格</th><th>优惠券</th><th>用户名</th><th>收件人及地址</th><th>下单时间</th><th>支付时间</th></tr>');
		foreach ($lists as $k=>$v){
			echo('<tr>');
			echo('<td align="center">&nbsp;'.$v['order_no'].'</td>');
			echo('<td align="center">'.$v['price'].'</td>');
			if($v['coupon']['title'] != ''){
				echo('<td align="center">'.$v['coupon']['title'] .'('.$v['coupon']['price'] .')</td>');
			}else{
				echo('<td align="center">&nbsp;</td>');
			}
			
			echo('<td align="center">'.$v['uid_text'].'</td>');
			echo('<td align="center">'.$v['address_name'] .'-'.$v['address_mobile'].'<br />'.$v['address_province'].$v['address_city'].$v['address_county'].$v['address_address'].'</td>');
			echo('<td align="center">&nbsp; '.date('Y-m-d H:i', $v['add_time']).'</td>');
			if($v['pay_time']  != 0){
				echo('<td align="center">&nbsp; '.date('Y-m-d H:i', $v['pay_time']).'</td>');
			}else{
				echo('<td align="center">&nbsp;</td>');
			}
			echo('</tr>');
			echo('<tr><td></td>');
			echo('<td colspan="6">');
			foreach($v['xiangxi'] as $k2=>$v2){
				echo($v2['ke_title'].'('.$v2['ke_price'].')<br />');
			}
			echo('</td></tr>');
		}
		echo('</table>');
	}

	public function index(){
		$delist = M('Order_pay')->where(['is_pay'=>0])->select();
		foreach ($delist as $k => $v) {
			//删除30分钟未支付
			if(time()-30*60>$v['add_time']){
				M('Order_pay')->delete($v['id']);
			}
		}

		$where = '`id` > 0';
		if(I('get.key')){
			$where .= ' and (`order_no` like "%'.I('get.key').'%" or `address_name` like "%'.I('get.key').'%" or `address_mobile` like "%'.I('get.key').'%")';
		}

		if(I('get.start_time')){
			$where .= ' and `add_time` >'.strtotime(I('get.start_time'));
		}

		if(I('get.end_time')){
			$where .= ' and `add_time` <'.strtotime(I('get.end_time'));
		}


		// print_r($where);

		$count =  M('Order_pay')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Order_pay')->where($where)->order('pay_time desc, add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){

			$lists[$k]['uid_text']= M('user')->where(['id'=>$v['uid']])->getField('nickName');
			$lists[$k]['coupon'] = M('Coupon')->where(['id'=>$v['coupon_id']])->find();
			$lists[$k]['xiangxi']= M('Order')->where(['order_no'=>$v['order_no']])->select();
		}

		// print_r($lists);
		$this->lists = $lists;
		$this->display();	
	}



	
	public function del(){
		$res = M('Order_pay')->find(I('get.id'));
		
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);	
		}

		M('Order')->where(['order_no'=>$res['order_no']])->delete();
		
		M('Order_pay')->delete(I('get.id'));
		die(json_encode(['code'=>1, 'msg'=>'操作成功']));
	}
}