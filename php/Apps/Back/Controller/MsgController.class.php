<?php
namespace Back\Controller;
use Think\Controller;

class MsgController extends CommController {
	
	public function _initialize(){
		parent::_initialize();
		$this->cat_name = '在线报名';	
	}

	public function index(){

		$where = '`id` > 0';

		$count =  M('Msg')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Msg')->where($where)->order('add_time desc')->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			$lists[$k]['uid_text'] = M('User')->where(array('id'=>$v['uid']))->getField('username');
		}
		$this->lists = $lists;
		$this->display();	
	}

	function isCheck(){
		$res = M('Msg')->find(I('get.id'));
		if($res['is_check'] == 1){
			M('Msg')->where(array('id'=>$res['id']))->save(array('is_check'=>0));
		}else{
			M('Msg')->where(array('id'=>$res['id']))->save(array('is_check'=>1));
		}

		redirect($_SERVER['HTTP_REFERER']);
	}


	function del(){
		M('Msg')->delete(I('get.id'));
		// redirect($_SERVER['HTTP_REFERER']);
		die(json_encode(array('code'=>1)));
	}
	


}