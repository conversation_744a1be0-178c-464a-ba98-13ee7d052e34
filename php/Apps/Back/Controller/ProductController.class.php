<?php
namespace Back\Controller;
use Think\Controller;
use Vendor\Unlimit;
class ProductController extends CommController {
	public function _initialize(){
			parent::_initialize();
	}
	public function index(){
		$where = '`cat_id` = '.I('get.cid');
		if(I('get.small')){
			$where.=' and `cat_small` = "'.I('get.small').'"';
		}
		if(I('get.key')){
			$where.=' and `title` like "%'.I('get.key').'%"';
		}
		$count =  M('Product')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$this->lists = M('Product')->where($where)->order('order_id desc,id desc')->limit($page->firstRow.','.$page->listRows)->select();
		// print_r(M('Product')->_sql());
		$this->display();
	}
	public function add(){
		$this->link_id = time();
		$this->display();
	}
	public function runAdd(){
		$a = M('Product');
		if($a->create()){
				$a->add_time = I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
				//$a->pub_date = strtotime(I('post.pub_date'));
				$a->content = $_POST['content'];
				$a->content_en = $_POST['content_en'];
				$a->order_id = 100;
				if($a->add()){
					//$this->show($a->_sql());
					die(json_encode(array('code'=>1)));
				}
		}
	}
	function picDel(){
		 M('Xiangce_pic')->delete(I('get.id'));
		 $ret_lists = M('Xiangce_pic')->where(array('link_id'=>$_GET['linkid']))->order('id asc')->select();
		 // print_r(M('Xiangce_pic')->_sql());
		 $retArr = array(
			'code'=> 1,
			'ret_lists' => $ret_lists
		);
		 echo(json_encode($retArr));
	}
	public function edit(){
		$this->res = $res = M('Product')->find(I('get.id'));
		$this->link_id = $res['link_id'];
		$this->xiangce = M('Xiangce_pic')->where(array('link_id'=>$res['link_id']))->order('id asc')->select();
		$this->display();
	}
	public function runEdit(){
		$a = M('Product');
		if($a->create()){
			$a->add_time =I('post.pub_date') == '' ? time() :  strtotime(I('post.pub_date'));
			//$a->pub_date = strtotime(I('post.pub_date'));
			$a->content = $_POST['content'];
			$a->content_en = $_POST['content_en'];
			$a->save();
			// $this->redirect('index', array('cid'=>I('post.cid'),'type'=>I('post.type')));
			die(json_encode(array('code'=>1)));
		}
	}
	public function del(){
		$res = M('Product')->find(I('get.id'));
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);
		}
		if(M('Product')->delete(I('get.id'))){
			die(json_encode(array('code'=>1)));
		}
	}
	public function runOrder(){
		M('Product')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}
	public function runRecom(){
		$res = M('Product')->find(I('get.id'));
		if($res['is_recom']==1){
			M('Product')->where(array('id'=>$res['id']))->save(array('is_recom'=>0));
		}else{
			M('Product')->where(array('id'=>$res['id']))->save(array('is_recom'=>1));
		}
		// die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
		redirect($_SERVER['HTTP_REFERER']);
	}

	function runHot(){
		$res = M('Product')->find(I('get.id'));
		if($res['is_hot']==1){
			M('Product')->where(array('id'=>$res['id']))->save(array('is_hot'=>0));
		}else{
			M('Product')->where(array('id'=>$res['id']))->save(array('is_hot'=>1));
		}
		redirect($_SERVER['HTTP_REFERER']);
	}

	

}