<?php

namespace Back\Controller;

use Think\Controller;



class AdminController extends CommController {

	public function index(){

		$this->lists = M('Admin')->where(array('type'=>1))->order('id asc')->select();

		$this->display();	

	}

	

	

	public function runAdd(){

		$a = M('Admin');

		if($a->where(array('name'=>I('post.name')))->find()){

				$this->error('用户名已存在');

		}

		

		$a->create();

		$a->pwd = md5(I('post.pwd'));

		$a->add();	

		

		redirect($_SERVER['HTTP_REFERER']);

	}

	

	public function del(){

		M('Admin')->delete(I('get.id'));	

		redirect($_SERVER['HTTP_REFERER']);

	}

	

	

	public function runPwd(){

		

		if(M('Admin')->where(array('id'=>I('post.id')))->save(array('pwd'=>md5(I('post.pwd'))))){

			die(json_encode(array('code'=>1, 'msg'=>'操作成功')));	

		}else{

			die(json_encode(array('code'=>0, 'msg'=>'操作失败')));	

		}

		

		

		

	}







}