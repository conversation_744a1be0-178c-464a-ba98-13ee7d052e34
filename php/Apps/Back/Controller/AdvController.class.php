<?php
namespace Back\Controller;
use Think\Controller;

class AdvController extends CommController {
	public function _initialize(){
		parent::_initialize();
		$this->adv_type = C('ADV_TYPE');	
	}
	
	public function index(){
		$count =  M('Adv')->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$this->lists = M('Adv')->order('type asc , order_id desc, id asc')->limit($page->firstRow.','.$page->listRows)->select();
		$this->display();	
	}
	
	public function banner(){
		$this->lists = M('Adv')->order('type asc , order_id desc, id asc')->select();
		$this->display();	
	}
	
	public function runAdd(){
		$b = M('Adv');
		if($b->create()){
			$b->order_id  = 100;
			if($b->add()){
				die(json_encode(array('code'=>1)));
			}
		}	
	}
	
	public function edit(){
		$this->res = M('Adv')->find(I('get.id'));	
		$this->display();
	}	
	
	public function runEdit(){
		M('Adv')->create();	
		M('Adv')->summary = $_POST['summary'];
		M('Adv')->save();
		die(json_encode(array('code'=>1)));
	}
	
	public function del(){
		$res = M('Adv')->find(I('get.id'));
		
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);	
		}
		
		if(I('get.id')){
			if(M('Adv')->delete(I('get.id'))){
				die(json_encode(array('code'=>1)));
			}
		}	
	}
	
	public function runOrder(){
		M('Adv')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));	
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}

}