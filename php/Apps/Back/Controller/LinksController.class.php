<?php
namespace Back\Controller;
use Think\Controller;

class LinksController extends CommController {
	public function _initialize(){
		parent::_initialize();
		$this->adv_type = C('ADV_TYPE');	
	}
	
	public function index(){

		$where = '`cid` = '.I('get.cid');

		if(I('get.key')){
			$where.=' and `name` like "%'.I('get.key').'%"';
		}


		$count =  M('Links')->where($where)->count();
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$this->lists = M('Links')->where($where)->order('order_id desc, id asc')->limit($page->firstRow.','.$page->listRows)->select();
		$this->display();	
	}
	
	public function banner(){
		$this->lists = M('Links')->order('order_id desc, id asc')->select();
		$this->display();	
	}
	
	public function runAdd(){
		$b = M('Links');
		if($b->create()){
			$b->order_id  = 100;
			if($b->add()){
				die(json_encode(array('code'=>1)));
			}
		}	
	}
	
	public function edit(){
		$this->res = M('Links')->find(I('get.id'));	
		$this->display();
	}	
	
	public function runEdit(){
		M('Links')->create();	
		M('Links')->save();
		die(json_encode(array('code'=>1)));
	}
	
	public function del(){
		$res = M('Links')->find(I('get.id'));
		
		//删除图片
		if($res['thumbs']!=''){
			@unlink(realpath('.').'/Attached/'.$res['thumbs']);	
		}
		
		if(I('get.id')){
			if(M('Links')->delete(I('get.id'))){
				die(json_encode(array('code'=>1)));
			}
		}	
	}
	
	public function runOrder(){
		M('Links')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));	
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}

}