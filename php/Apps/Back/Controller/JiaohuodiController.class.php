<?php
namespace Back\Controller;
use Think\Controller;
require('Unlimit.class.php');
class JiaohuodiController extends CommController {
	function _initialize(){
		$this->zimu = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z');
	}

	function searchID(){
		// $a = M('Jiaohuodi')->where(['name'=>I('get.key')])->find();
		// print_r( M('Jiaohuodi')->_sql());
		die(json_encode($a));
	}


	public function index(){
		$cat = M('Jiaohuodi')->field('id, name, order_id, pid, type')->order('order_id desc, id asc')->select();
		$this->allCat = $allCat =  \Unlimit::getAllCat($cat,0);
		$this->display();
	}
	public function add(){
		$this->display();
	}
	public function runAdd(){
		$c = M('Jiaohuodi');
		if($c->create()){
			$c->order_id=100;
			$c->content = $_POST['content'];
			if($c->add()){
				die(json_encode(array('code'=>1, '')));
			}else{
				die(json_encode(array('code'=>0, '')));
			}
		}
	}
	public function edit(){
		$this->res = $res = M('Jiaohuodi')->find(I('get.id'));
		$this->display();
	}
	public function runEdit(){
		$c = M('Jiaohuodi');
		if($c->create()){
			$c->content = $_POST['content'];
			if($c->save()){
				die(json_encode(array('code'=>1, '')));
			}else{
				die(json_encode(array('code'=>0, '')));
			}
		}
	}
	public function del(){
		//检查内容
		$cat_art = M('Art')->where(array('cat_id'=>I('get.id')))->find();
		if($cat_art){
			die(json_encode(array('code'=>0, 'msg'=>'请先删除本栏目内容')));
		}
		//检查子栏目
		if(M('Jiaohuodi')->where(array('pid'=>I('get.id')))->find()){
			die(json_encode(array('code'=>0, 'msg'=>'请先删除子栏目')));
		}else{
			if(M('Jiaohuodi')->delete(I('get.id'))){
				die(json_encode(array('code'=>1, 'msg'=>'删除成功')));
			}else{
				die(json_encode(array('code'=>0, 'msg'=>'删除失败')));
			}
		}
	}
	public function runOrder(){
		M('Jiaohuodi')->where(array('id'=>I('get.id')))->save(array('order_id'=>I('get.order_id')));
		die(json_encode(array('code'=>1, 'msg'=>'操作成功')));
	}
	//数组元素形式（多维）
	public function getChildArr($pid){
		$sub = M('Jiaohuodi')->where(array('pid'=>$pid))->select();
		if(count($sub)>0){
			for($i=0;$i< count($sub);$i++){
				$arr[$i]['name']=$sub[$i]['name'];
				$arr[$i]['list']= $this->getChildArr($sub[$i]['id']);
			}
		}
		return $arr;
	}
	//字符串形式
	public function getChildStr($pid,$indent=1){
		$sub = M('Jiaohuodi')->where(array('pid'=>$pid))->select();
		if(count($sub)>0){
			for($i=0;$i< count($sub);$i++){
				$str .= str_repeat('|--', $indent);
				$str .=$sub[$i]['name'].'<br />';
				$str .= $this->getChildStr($sub[$i]['id'],$indent+1);
			}
		}
		return $str;
	}
}