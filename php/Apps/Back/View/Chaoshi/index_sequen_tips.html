<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
	<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>
	<script type="text/javascript" src="__PUBLIC__/js/ion.rangeSlider.min.js"></script>
	<link rel="stylesheet" type="text/css" href="__PUBLIC__/css/ion.rangeSlider.min.css">
</head>
<div  class="x-body">

<include file="shaixuan" />


<div class="mt_20 fl">
	<form action="{:U('index')}" id="fms">
		<input lay-verify="required"  name="pihao" id="key" value="{$_GET['pihao']}" placeholder="搜索批号/捆号" class="layui-input" style="width:200px;float: left;" />
		<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
		<div style="clear: both;"></div>
	</form>
	<script>
		$('#search_click').click(function(){
			if($('#key').val()==''){
				alert('请输入关键词');
				$('#key').focus();
				return false;
			}
		});
	</script>
</div>

<div class="mt_20 fl ml_20">
	<select id="del_sele" style="height: 35px;" layui-ignore >
		<option value="">请选择要删除的文件</option>
		<foreach name="excels" item="v">
			<option value="{$v.id}">{$v.title}({$v.add_time|date="Y/m/d",###})</option>
		</foreach>
	</select>
	<script>
		$('#del_sele').change(function(){
			if($('#del_sele').val()==''){
				alert('请选择要删除的文件');
				return false;
			}else{
				if(confirm('确认删除？')){
					$.getJSON('{:U("delRec")}', {id:$(this).val()}, function(d){
						alert(d.msg);
						if(d.code==1){
							history.go(0);
						}
					});
				}
			}
		});
	</script>
</div>
<div style="clear: both;"></div>

<div class="mt_20">
	<!-- <button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button> -->
	<button id="del_click" class="layui-btn" style="background:#ff6c60;" ><i class="fa fa-trash-o" aria-hidden="true" ></i> 删除</button>
	<input type="button" class="go_up layui-btn" value="导入Excel"  />

	<select name="zhuangtai" id="zhuangtai_all" style="height: 35px;padding:0;width:120px;" >
		<option value="">批量更新状态</option>
	
		<foreach name="xls_zhuangtai" item="v2">
			<option value="{$v2}" <if condition="$v2 eq $v['zhuangtai']">selected</if> >{$v2}</option>
		</foreach>
	</select>

	<script>

	$(function(){
		$('#add_click').click(function(){
			var aa = "{:U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']))}";
			x_admin_show('添加内容', aa);
		});

		$('#zhuangtai_all').change(function(){
			if($(this).val()!=''){
				if(!$('#fm2 input[name="id[]"]').is(':checked')){
					alert('请选择要更新的项');
					$("#zhuangtai_all").prop('selectedIndex', 0)
				}else{
					if(confirm('确认更新状态?')){
						$('#zhuangtai_text').val($(this).val());
						// console.log($('#fm2').serialize());
						$.post('{:U("changeZhuangtaiAll")}', $('#fm2').serialize(), function(d){
							if(d.code==1){
								history.go(0);
							}
						},'json');
					}
				}
			}
		});
	});

	</script>
</div>


	<div style="clear: both;"></div>
	<form id="fm2">
		<input name="zhuangtai_text" id="zhuangtai_text" type="hidden" />
		<div style="width:1500px;overflow-x: auto">
			<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79" style="width:2800px;">
				<thead>
					<tr style="text-align: left;">
						<th><label id="checkAll"><input type="checkbox" class="ver_mid" /> <span class="ver_mid">全选</span></label></th>
						<th>序号</th>
						<th>批号/捆号</th>
						<th>加工类型</th>
						<th>状态</th>
						<th>颜色级/品级</th>
						<th>平均马值</th>
						<th>长度</th>
						<th>强力</th>
						<th>含杂率</th>
						<th>回潮率</th>
						<th>公重(吨)</th>
						<th>毛重(吨)</th>
						<th>长度整齐度</th>
						<th>加工厂</th>
						<th>仓库</th>
						<th>基差</th>
						<th>备注</th>
						<th>点价合约</th>
						<th>包数</th>
						<th>基差</th>
						<!-- <th>公重</th> -->
						<th>操作</th>
					</tr>
				</thead>
				<tbody>
					<foreach name="lists" item="v">
						<tr>
							<td>
								<label>
									<input type="checkbox" name="id[]" value="{$v.id}" class="ver_mid per_check" />
								 	<!-- <span class="ver_mid">{$v.id}</span> -->
								 </label>
							</td>
							<td>{$v.xvhao}</td>
							<td>{$v.pihao_kunkao}</td>
							<td>{$v.leixing}</td>
							<td>
								<select name="zhuangtai" class="zhuangtai" style="height: 35px;padding:0;" myid="{$v.id}">
									<option value="">选择状态</option>
								
									<foreach name="xls_zhuangtai" item="v2">
										<option value="{$v2}" <if condition="$v2 eq $v['zhuangtai']">selected</if> >{$v2}</option>
									</foreach>
								</select>
							</td>
							<td>{$v.yanseji_pinji}</td>
							<td>{$v.mazhi}</td>
							<td>{$v.changdu}</td>
							<td>{$v.qiangli}</td>
							<td>{$v.hanzalv}</td>
							<td>{$v.huichaolv}</td>
							<td>{$v.gongzhong}</td>
							<td>{$v.maozhaong}</td>
							<td>{$v.zhengqidu}</td>
							<td>{$v.jiagongchang}</td>
							<td>{$v.cangchumingcheng}</td>
							<td>{$v.jicha}</td>
							<td>{$v.beizhu}</td>
							<td>{$v.dianjiaheyue}</td>
							<td>{$v.baoshu}</td>
							<td>{$v.jicha2}</td>
							<!-- <td>{$v.gongzhong2}</td> -->
							<!-- <td><input class="layui-input order_blur clr_79"  value="{$v.order_id}" myid="{$v.id}" style="width:50px;"/></td> -->
							<td>
								<!-- <a class="bg_g block_a edit_item" href="javascript:;" my_url="{:U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']))}" title="编辑" >
									<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
								</a>
								&nbsp; -->
								<a class="bg_r block_a del_item" href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" title="删除" >
									<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
								</a>
							</td>
						</tr>
					</foreach>
				</tbody>
			</table>
		</div>
	</form>


<div id="mask_bg" style="background: rgba(0,0,0,0.6);width: 100%;height: 100%;position: fixed;top:0;left:0;z-index: 99;display: none;">

	<div style="background: #fff;padding:40px;border-radius: 10px;width:600px;margin:100px auto;position: relative;">
		<div id="tips_txt"></div>
		<a href="#." id="close_mask" style="color:#333;font-size:40px;position: absolute;top:0;right:10px;">&times;</a>
		<div id="fm_box">
			<div id="file_wraper" style="max-height: 400px;overflow: auto;">
				<form class="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('runImportExcel',['id'=>1])}" style="margin-top:10px;">
					01<input type="file"  name="thumbs_hide" style="border:1px solid #eee;background: #eee;" /> <span id="txt_1"></span>
				</form>
				<include file="fm" />
			</div>
			<div style="margin-top:5px;"><a href="#." style="color: #999;" id="addFm">[+]</a></div><br />
			<input type="button" value="确认导入" id="tijiao" style="border:1px solid #eee;padding:5px 10px;" />
		</div>
		
	</div>
</div>

<script type="text/javascript">
$('.go_up').click(function(){
	$('#mask_bg').show();
});


$('#close_mask').click(function(){
	// $('#mask_bg').hide();
	history.go(0);
});


var fm_count = 1;
$('#addFm').click(function(){
	if(fm_count<5){
		$('#file_wraper form').eq(fm_count).show();
		fm_count++;
	}else{
		alert('超过最大限制数'+fm_count);
	}
	
});


var times = 0;
$('#tijiao').click(function(){
	
		tijiao(times);
});


function tijiao(times){
	if(times<fm_count){
		$('#tips_txt').append("正在导入第"+(times+1)+"个文件<br />");
		$('.uploadForm').eq(times).submit();
	}else{
		$('#tips_txt').append("导入完毕<br />");
	}
}

$('.uploadForm').ajaxForm({
	dataType: 'json',  
	// async:false,
	success: function(d){

		$('#tips_txt').append(d.msg+"<br />");
		times++;
		tijiao(times);
	},
	beforeSubmit:function(){
	}
});  

</script>



<!-- <form id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('runImportExcel')}">
	<input type="file" name="thumbs_hide" id="thumbs_hide"  />
</form> -->

	<div class="pager mt_10">
		{$page}
	</div>
</div>
<script>

	$('#del_click').click(function(){
		if(!$('#fm2 input[name="id[]"]').is(':checked')){
			alert('请选择要删除的选项');
			return false;
		}else{
			if(confirm('确认删除?')){
				$.post('{:U("delAll")}', $('#fm2').serialize(), function(d){
					if(d.code==1){
						history.go(0);
					}
				},'json');
			}
		}
	});



	$('#checkAll').click(function(){
		$('#fm2 input[type=checkbox]').prop('checked', $(this).find('input[type=checkbox]').prop('checked'));
	});
	$('.zhuangtai').change(function(){
		if($(this).val()!=''){
			$.getJSON('{:U("changeZhuangtai")}', {id:$(this).attr('myid'), zhuangtai:$(this).val()}, function(d){
				if(d.code==1){
					history.go(0);
				}
			});
		}
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.order_blur').blur(function(){
		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});
</script>
</body>
</html>