<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
</head>
	<div  class="x-body">

		 
		 <div class="fl">
			  <button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button>
			 <script>
			 	$('#add_click').click(function(){
			 		var aa = "{:U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']))}";
			 		x_admin_show('添加内容', aa);
			 	});
			 </script>
		 </div>

		 <div class="fl" style="margin-left: 20px;">
		 	<form action="{:U('index')}" >
		 		<input name="cid" value="{$_GET['cid']}" type="hidden" />
		 		<select  style="width:150px;height:40px;" name="jieduan_id"  id="jieduan_id" lay-ignore>
					<option  value="" >阶段</option>
					<foreach name="jieduan" item="v">
						<option value="{$v.id}" <if condition="$_GET['jieduan_id'] eq $v['id']">selected</if> >{$v.name}</option>
					</foreach>
				</select>

				<select  style="width:150px;height:40px;" name="nianji_id"  id="nianji_id" lay-ignore>
					<option  value="" >年级</option>
					<foreach name="all_nianji" item="v">
						<option value="{$v.id}" <if condition="$_GET['nianji_id'] eq $v['id']">selected</if> >{$v.name}</option>
					</foreach>
				</select>

				<select  style="width:150px;height:40px;" name="kemu_id"  id="kemu_id" lay-ignore>
					<option  value="" >科目</option>
					<foreach name="all_kemu" item="v">
						<option value="{$v.id}" <if condition="$_GET['kemu_id'] eq $v['id']">selected</if> >{$v.name}</option>
					</foreach>
				</select>


				<select  style="width:150px;height:40px;" name="ke_type_id"   lay-ignore>
					<option  value="" >课程类型</option>
					<foreach name="ke_type" item="v" >
						<option value="{$v.id}" <if condition="$_GET['ke_type_id'] eq $v['id']">selected</if>>{$v.name}</option>
					</foreach>
				</select>


				<input type="submit" id="shaixuan_click" value="筛选" class="layui-btn"   />
			</form>

			<script type="text/javascript">
				$('#jieduan_id').change(function(){
					$.getJSON('{:U("getChild")}', {id:$(this).val()}, function(d){
						$('#nianji_id option:gt(0)').remove();
						$('#kemu_id option:gt(0)').remove();
						$.each(d, function(k, v){
							$('#nianji_id').append('<option  value="'+v.id+'" >'+v.name+'</option>')
						})
					})
				})

				$('#nianji_id').change(function(){
					$.getJSON('{:U("getChild")}', {id:$(this).val()}, function(d){
						$('#kemu_id option:gt(0)').remove();
						$.each(d, function(k, v){
							$('#kemu_id').append('<option  value="'+v.id+'" >'+v.name+'</option>')
						})
					})
				})
				
			</script>
		 </div>
		 <div style="float:right;">
			<form action="{:U('index')}" id="fms">
				<input name="cid" value="{$_GET['cid']}" type="hidden" />
				<input lay-verify="required"  name="key" id="key" placeholder="输入关键词" class="layui-input" style="width:200px;float: left;" />
				<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
				<div style="clear: both;"></div>
			</form>
			<script>
				$('#search_click').click(function(){
					if($('#key').val()==''){
						alert('请输入关键词');
						$('#key').focus();
						return false;
					}
				});
			</script>
		</div>
		<div style="clear: both;"></div>
		<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr class="al_lt">
					<th>ID</th>
					<th>标题</th>
					<th>加入时间</th>
					<th>课程大纲</th>
					<th>排序</th>
					<th></th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<foreach name="lists" item="v">
				<tr>
					<td>{$v.id}</td>
					<td>{$v.title}</td>
					<td>{$v.add_time|date="Y-m-d H:i",###}</td>
					<td><a href="javascript:;" my_url="{:U('dagang', array('id'=>$v['id']))}"  
						class="dagang_item clr_g" >添加课程大纲</a></td>
					<td><input class="layui-input order_blur"  value="{$v.order_id}" myid="{$v.id}" style="width:50px;"/></td>
					<td>
						<if condition="$v.is_recom eq 1">
							<a href="{:U('runRecom', array('id'=>$v['id']))}" style="color:red;">荐</a>
						<else />
							<a href="{:U('runRecom', array('id'=>$v['id']))}">荐</a>
						</if>
					</td>
					<td>
						<a href="javascript:;" my_url="{:U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']))}" class="edit_item bg_g block_a " title="编辑" >
							<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
						</a>
						<a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}"  class="del_item bg_r block_a " title="删除" >
							<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
						</a>
					</td>
				</tr>
				</foreach>
			</tbody>
		</table>
		<div class="pager mt_10">
			{$page}
		</div>
	</div>
<script>
	$('.dagang_item').click(function(){
		x_admin_show('课程大纲',$(this).attr('my_url'));
	});


	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.recom_click').click(function(){
		$.getJSON('{:U("runRecom")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.recom_click2').click(function(){
		$.getJSON('{:U("runRecom2")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.order_blur').blur(function(){
		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});
</script>
</body>
</html>