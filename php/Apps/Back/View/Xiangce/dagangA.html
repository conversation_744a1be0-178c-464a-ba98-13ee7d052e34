<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
	<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />
	<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />
	<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>
	<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>
	<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>
	<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>
	<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>
	<script type="text/javascript" src="__PUBLIC__/js/jquery.validate.min.js"></script>
	<script>
		KindEditor.ready(function(K) {
			var editor1 = K.create('textarea[class="htmltext"]', {
				cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',
				uploadJson : '__PUBLIC__/kinder/upload_json.php',
				fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',
				allowFileManager : true,
				urlType:'domain',
				afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）
				afterCreate: function () {
					var self = this;
					self.sync();//把富文本编辑器的内容放到 文本域里面去。
				}
			});
			prettyPrint();
		});
		$(function(){
			//图片删除后加载
			$('#img_wrap').on('click','.pic_del', function(){
				if(confirm('确认删除？')){
					$.getJSON('{:U("picDel")}',{id:$(this).attr('myid'), linkid:"{$link_id}"}, function (d){
									// console.log(d);
									if(d.code==1){
										var strs = '';
										$.each(d.ret_lists, function(k,v){
											strs +='<img src="__ROOT__/Attached/'+v.thumbs+'" style="width:80px;height:80px;" />'
											strs +='<a href="javascript:;" class="layui-icon pic_del" myid="'+v.id+'">&#xe640;</a> &nbsp;';
										});
										$('#img_wrap').html(strs);
									}
								});
				}
			});
			//上传
			$('#uploadForm').ajaxForm({
				dataType: 'json',
				success: function(data){
							// console.log(data);
							if(data.msg=='OK'){
								var strs = '';
								$.each(data.ret_lists, function(k,v){
									strs +='<img src="__ROOT__/Attached/'+v.thumbs+'" style="width:80px;height:80px;" />'
									strs +='<a href="javascript:;" class="layui-icon pic_del" myid="'+v.id+'">&#xe640;</a> &nbsp;';
								});
								$('#img_wrap').html(strs);
							}else{
								$('.tips').text(data.msg);
							}
						},
						beforeSubmit:function(){
						}
					});
			$('.go_up').click(function(){
				$('#thumbs_hide').click();
			});
			$('#thumbs_hide').change(function(){
				$('#uploadForm').submit();
			});



			//上传
			var flag;
			$('#uploadForm2').ajaxForm({
				dataType: 'json',
				success: function(data){
					if(data.up_msg=='OK'){
						data.up_msg = '上传成功！';
						$('#img_wrap'+flag).html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');
						$('#pic'+flag).val(data.img_url);
					}
					$('.tips'+flag).text(data.up_msg);
				},
				beforeSubmit:function(){
				}
			});

			$('.go_up2').click(function(){
				flag = $(this).attr("flag");
				$('#thumbs_hide2').click();
			});
			$('#thumbs_hide2').change(function(){
				$('#uploadForm2').submit();
			});
	});
</script>
</head>



<div class="x-body">
	<form id="fm1" class="layui-form" method="post">
		<input type="hidden" name="ke_id" value="{$_GET['id']}" />
		<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79">
			<tr><td style="width:100px;">标题</td><td><input lay-verify="required"  name="title" class="layui-input"/></td></tr>
			<tr><td style="width:100px;">开课时间简介</td><td><input lay-verify="required"  name="summary" class="layui-input"/></td></tr>
			<tr><td style="width:100px;"></td><td>
				<div id="dang_box"></div>
				<a href="#." class="add_click">+添加内容</a>
			</td></tr>
			<!-- <tr><td>缩略图</td>
				<td><span id="img_wrapA"></span>
					<span>
						<input name="pic" id="picA" type="hidden"  />
						<input type="button" class="go_up2 layui-btn" flag="A"  value="上传"  />
						<span class="tipsA">（格式：2M以内，jpg/png ）</span>
					</span>
				</td>
			</tr>
			<tr>
				<td>轮播图</td>
				<td>
					<div id="img_wrap"></div>
					<div>
						<input name="thumbs" id="thumbs" type="hidden"  />
						<input type="button" class="go_up layui-btn" value="多图上传"  />
						<span class="tips">（格式：2M以内，jpg/png ）</span>
					</div>
				</td>
			</tr> -->
			<!-- <tr ><td>内容</td><td><textarea name="content" class="htmltext" style="width:800px;height:500px;visibility:hidden;" ></textarea></td></tr> -->
			<!--
			<tr><td class="al_rt">发布日期</td><td>
				<input name="pub_date" id="pub_date" style="width:200px;"  class="layui-input"/>
			</td></tr> -->
			<tr><td></td><td><input type="submit" value="添加" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
		</table>
	</form>

<script>


$('#dang_box').on('click', '.xClick' , function(){
	$(this).parent('.danggang_box').remove();
});


$('.add_click').click(function(){
	$('#dang_box').append(`<div class="danggang_box"><input name="bt[]" placeholder="大纲小标题"  class="layui-input" style="width:200px;" />
	<textarea name="summ[]"  placeholder="大纲小标题简介，每行回车即可" class="layui-textarea" style="width:500px;"></textarea>
	<a href="#." class="xClick">×</a></div>`);
});

	layui.use('laydate', function(){
		var laydate = layui.laydate;
		//执行一个laydate实例
		laydate.render({
			elem: '#pub_date' //指定元素
		});
	});

	layui.use('form', function(){
		var form = layui.form;
		//监听提交
		form.on('submit(formDemo)', function(data){
			$.post("{:U('runDagangA')}",$('#fm1').serialize(), function(d){
				if(d.code == 1){
					layer.alert("增加成功", {icon: 6},function () {
						window.parent.location.reload();
						var index = parent.layer.getFrameIndex(window.name);
										//关闭当前frame
										parent.layer.close(index);
									});
				}
			},'json');
			return false;
		});
	});
</script>
<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUploadMulti', array('linkid'=>$link_id))}">
	<input type="file" name="thumbs_hide[]" id="thumbs_hide" multiple="multiple" />
</form>
<form style="display: none;" id="uploadForm2" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">
	<input type="file" name="thumbs_hide" id="thumbs_hide2" />
</form>
</div>
</body>
</html>