<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<include file="Public:header" />
</head>
<div  class="x-body">
	<!-- <button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button> -->
	<button id="del_click" class="layui-btn" style="background:#ff6c60;" ><i class="fa fa-trash-o" aria-hidden="true" ></i> 删除</button>
	<script>
		$('#add_click').click(function(){
			var aa = "{:U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']))}";
			x_admin_show('添加内容', aa);
		});
	</script>
	<foreach name="cat_small" item="v">
		<if condition="$_GET['small'] eq $v">
			&nbsp;&nbsp; <a href="{:U('index', array('cid'=>$_GET['cid'], 'type'=>$_GET['type'], 'small'=>$v))}" style="color:red;">{$v}</a>
			<else />
			&nbsp;&nbsp; <a href="{:U('index', array('cid'=>$_GET['cid'], 'type'=>$_GET['type'], 'small'=>$v))}">{$v}</a>
		</if>
	</foreach>
	<div style="float:right;">
		<form action="{:U('index')}" id="fms">
			<input name="cid" value="{$_GET['cid']}" type="hidden" />
			<input lay-verify="required"  name="key" id="key" placeholder="输入关键词" class="layui-input" style="width:200px;float: left;" />
			<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
			<div style="clear: both;"></div>
		</form>
		<script>
			$('#search_click').click(function(){
				if($('#key').val()==''){
					alert('请输入关键词');
					$('#key').focus();
					return false;
				}
			});
		</script>
	</div>
	<div style="clear: both;"></div>
	<form id="fm1">
		<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr style="text-align: left;">
					<th><label id="checkAll"><input type="checkbox" class="ver_mid" /> <span class="ver_mid">全选</span></label></th>
					<th width="400">标题</th>
					<th>加入时间</th>
					<!-- <th>排序</th> -->
					<!-- <th>推荐</th> -->
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<foreach name="lists" item="v">
					<tr>
						<td><label><input type="checkbox" name="id[]" value="{$v.id}" class="ver_mid per_check" /> <span class="ver_mid">{$v.id}</span></label></td>
						<td>{$v.title}</td>
						<td>{$v.add_time|date="Y-m-d H:i",###}</td>
						<!-- <td><input class="layui-input order_blur clr_79"  value="{$v.order_id}" myid="{$v.id}" style="width:50px;"/></td> -->
<!-- 						<td>
							<a class="recom_click" myid="{$v.id}" href="javascript:;" title="推荐" >
								<if condition="$v.is_recom eq 1">
									<i class="fa fa-star" aria-hidden="true"  style="font-size: 14px;color:#ff6c60;"></i>
									<else />
									<i class="fa fa-star" aria-hidden="true"  style="font-size: 14px;color:#999;"></i>
								</if>
							</a>
						</td> -->
						<td>
						<!-- 	<a class="bg_g block_a edit_item" href="javascript:;" my_url="{:U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']))}" title="编辑" >
								<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
							</a> -->
							&nbsp;
							<a class="bg_r block_a del_item" href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" title="删除" >
								<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
							</a>
			<!--
						&nbsp;&nbsp;
						<if condition="$v.is_hot eq 1">
							<a href="{:U('runHot', array('id'=>$v['id']))}" style="color:red;">荐</a>
						<else />
							<a href="{:U('runHot', array('id'=>$v['id']))}">荐</a>
						</if>
					<if condition="strpos(',201,202,203,204,205,206,', ','.$v['cat_id'].',') nheq false">
						&nbsp;&nbsp;
						<if condition="$v.is_hidden eq 1">
							<a href="{:U('runHidden', array('id'=>$v['id']))}" style="color:red;">隐</a>
						<else />
							<a href="{:U('runHidden', array('id'=>$v['id']))}">隐</a>
						</if>
					</if>
						&nbsp;&nbsp;
						<if condition="$v.is_new eq 1">
							<a href="{:U('runNew', array('id'=>$v['id']))}" style="color:red;">新</a>
						<else />
							<a href="{:U('runNew', array('id'=>$v['id']))}">新</a>
						</if>
						&nbsp;&nbsp;
						<if condition="$_GET['jujiao'] eq 1">
							<if condition="$v.is_jujiao eq 1">
								<a href="{:U('runJujiao', array('id'=>$v['id']))}" style="color:red;" >聚焦</a>
							<else />
								<a href="{:U('runJujiao', array('id'=>$v['id']))}" >聚焦</a>
							</if>
						</if>
						&nbsp;&nbsp;
						<if condition="$_GET['toutiao'] eq 1">
							<if condition="$v.is_toutiao eq 1">
								<a href="{:U('runToutiao', array('id'=>$v['id']))}" style="color:red;" >头条</a>
							<else />
								<a href="{:U('runToutiao', array('id'=>$v['id']))}" >头条</a>
							</if>
						</if> -->
						<if condition="$is_jicheng eq 1">
							<select name="status" id="status" class="status_sele" myid="{$v.id}" style="padding:0;height:30px;width:80px;">
								<option value="">待审核</option>
								<option value="2" <if condition="$v.status eq 2"> selected="true" </if>>已发布</option>
								<option value="3" <if condition="$v.status eq 3"> selected="true" </if>>审核失败</option>
							</select>
						</if>
					</td>
				</tr>
			</foreach>
		</tbody>
	</table>
</form>
<div class="pager mt_10">
	{$page}
</div>
</div>
<script>
	$('#del_click').click(function(){
		if(!$('#fm1 input[name="id[]"]').is(':checked')){
			alert('请选择要删除的选项');
			return false;
		}else{
			if(confirm('确认删除?')){
				$.post('{:U("delAll")}', $('#fm1').serialize(), function(d){
					if(d.code==1){
						history.go(0);
					}
				},'json');
			}
		}
	});
	$('#checkAll').click(function(){
		$('#fm1 input[type=checkbox]').prop('checked', $(this).find('input[type=checkbox]').prop('checked'));
	});
	$('.status_sele').change(function(){
		if($(this).val()!=''){
			$.getJSON('{:U("runCheck")}', {id:$(this).attr('myid'), status:$(this).val()}, function(d){
				if(d.code==1){
					history.go(0);
				}
			});
		}
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.recom_click').click(function(){
		$.getJSON('{:U("runRecom")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.recom_click2').click(function(){
		$.getJSON('{:U("runRecom2")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.order_blur').blur(function(){
		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});
</script>
</body>
</html>