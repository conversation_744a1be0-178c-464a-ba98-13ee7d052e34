<!DOCTYPE html>

<html lang="zh-CN">

<head>

<include file="Public:header" />

</head>





	<div  class="x-body">

		<table  class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">

			<thead>

				<tr class="al_lt"><th>姓名 </th><th>城市</th><th>邮箱</th><th>手机</th><th style="width:500px;">内容</th><th>加入时间</th><th>操作</th></tr>

			</thead>

			<foreach name="lists" item="v">

			<tr>

				<td>{$v.name}</td>

				<td>{$v.city}</td>

				<td>{$v.email}</td>

				<td>{$v.mobile}</td>

				<td>{$v.content}</td>

				<td>{$v.add_time|date="Y-m-d H:i",###}</td>

<!-- 				<td>

					<if condition="$v.is_check eq 1">

						<a href="{:U('isCheck', array('id'=>$v['id']))}" style="color:red;">已审核</a>

					<else />

						<a href="{:U('isCheck', array('id'=>$v['id']))}">待审核</a>

					</if>

					

				</td> -->

				<td>

					<!-- <a href="{:U('edit', array('id'=>$v['id']))}" class="a4 clr_f">编辑</a> -->

					<!-- <a href="{:U('del', array('id'=>$v['id']))}" class="a4 clr_f" onclick="return confirm('确认删除？');"><i class="layui-icon">&#xe640;</i></a> -->



					<a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}"  class="del_item bg_r block_a" title="删除" >

							<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>

					</a>

				</td>

			</tr>

			</foreach>

		</table>

		

		<div class="pager mt_10">

			{$page}

		</div>

	</div>





<script>





	$('.del_item').click(function(){

		var aa = $(this);

		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){

			$.getJSON(aa.attr('my_url'), function(d){

				if(d.code==1){

					history.go(0);

				}

			});



			layer.close(index);

		});

	});





	$('.recom_click').click(function(){

		$.getJSON('{:U("runRecom")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});

	});	

	

	$('.order_blur').blur(function(){

		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});

	});	

</script>



</body>

</html>



