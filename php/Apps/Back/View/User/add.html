<!DOCTYPE html>

<html lang="zh-CN">

<head>

<include file="Public:header" />



<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />

<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />

<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>

<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>

<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>

<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>

<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>

<script type="text/javascript" src="__PUBLIC__/js/jquery.validate.min.js"></script>

<style>

.ke-toolbar:{background: #fff !important;}

</style>

<script>



	KindEditor.ready(function(K) {

		var editor1 = K.create('textarea[class="htmltext"]', {

			cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',

			uploadJson : '__PUBLIC__/kinder/upload_json.php',

			fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',

			allowFileManager : true,

			afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）

			afterCreate: function () {

				var self = this;

				self.sync();//把富文本编辑器的内容放到 文本域里面去。



			}

		});

		prettyPrint();

	});

	



$(function(){



	//上传

		$('#uploadForm').ajaxForm({

		  dataType: 'json',  

		    success: function(data){

		    		if(data.up_msg=='OK'){

		    			data.up_msg = '上传成功！';

		    			$('#img_wrap').html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');

		    			$('#thumbs').val(data.img_url);

		    		}

		    		

		    		$('.tips').text(data.up_msg);

		    },

		    beforeSubmit:function(){

		    }

		  });  

		  

		  $('.go_up').click(function(){

		  		$('#thumbs_hide').click();

		  });

		  

		  $('#thumbs_hide').change(function(){

				$('#uploadForm').submit();

		  });

	//上传

		$('#uploadForm2').ajaxForm({

		  dataType: 'json',  

		    success: function(data){

		    		if(data.up_msg=='OK'){

		    			data.up_msg = '上传成功！';

		    			$('#img_wrap2').html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');

		    			$('#thumbs2').val(data.img_url);

		    		}

		    		

		    		$('.tips2').text(data.up_msg);

		    },

		    beforeSubmit:function(){

		    }

		  });  

		  

		  $('.go_up2').click(function(){

		  		$('#thumbs_hide2').click();

		  });

		  

		  $('#thumbs_hide2').change(function(){

				$('#uploadForm2').submit();

		  });



});  

</script>

</head>





	<div class="x-body">

			<form id="fm1" class="layui-form" method="post">

				<input type="hidden" name="type" value="{$_GET['type']}" />

				<input type="hidden" name="cat_id" value="{$_GET['cid']}" />

				<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79">

					<tr><td style="width:100px;">公司名称</td><td><input lay-verify="required"  name="title" class="layui-input"/></td></tr>

					<tr><td style="width:100px;">地址</td><td><input lay-verify="required"  name="address" class="layui-input"/></td></tr>

					<tr><td style="width:100px;">联系人</td><td><input lay-verify="required"  name="contact" class="layui-input"/></td></tr>

					<tr><td style="width:100px;">联系电话</td><td><input lay-verify="required"  name="tel" class="layui-input"/></td></tr>

					<tr><td style="width:100px;">经度</td><td><input lay-verify="required"  name="jing" class="layui-input"/></td></tr>

					<tr><td style="width:100px;">纬度</td><td><input lay-verify="required"  name="wei" class="layui-input"/></td></tr>



					<!-- <tr><td style="width:100px;">描述</td><td><textarea name="summary" class="layui-textarea"></textarea></td></tr> -->



<!-- 

					<php>

						$array = array(303, 304);

					</php>

					<if condition="in_array($_GET['cid'], $array) eq true">

						<tr><td style="width:100px;">技师分类</td><td>

							<select name="cat"  lay-verify="required" >

								<option value="">请选择</option>

								<foreach name="cat" item="v">

									<option value="{$v.id}">{$v.title}</option>

								</foreach>

							</select>

						</td></tr>

					</if> -->



						<tr>

							<td >公司LOGO</td><td>

								<span id="img_wrap"></span>

								<span>

									<input name="thumbs" id="thumbs" type="hidden"  />

									<input type="button" class="go_up layui-btn" value="上传"  />

									<span class="tips">2M以内、jpg/png </span>

								</span>

							</td>

						</tr>

						<tr>

							<td >名片二维码</td><td>

								<span id="img_wrap2"></span>

								<span>

									<input name="thumbs2" id="thumbs2" type="hidden"  />

									<input type="button" class="go_up2 layui-btn" value="上传"  />

									<span class="tips2">2M以内、jpg/png </span>

								</span>

							</td>

						</tr>

						<!-- <tr ><td>内容</td><td><textarea class="htmltext w_100" name="content" style="height:500px;visibility:hidden;" ></textarea></td></tr>	 -->





					<tr><td></td><td><input type="submit" value="添加" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>

				</table>

			</form>



			<script>

layui.use('laydate', function(){

  var laydate = layui.laydate;

  

  //执行一个laydate实例

  laydate.render({

    elem: '#pub_date' //指定元素

  });

});





 layui.use('form', function(){

  var form = layui.form;

  

  //监听提交

  form.on('submit(formDemo)', function(data){

    // $.post("{:U('runAdd')}",data.field, function(d){

    	$.post("{:U('runAdd')}",$('#fm1').serialize(), function(d){

    	if(d.code == 1){

    		layer.alert("增加成功", {icon: 6},function () {

    			window.parent.location.reload();

    				var index = parent.layer.getFrameIndex(window.name);

	                //关闭当前frame

	                parent.layer.close(index);



            });

    	}

    },'json');



      return false;

  });





});





</script>

			

			<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">

				<input type="file" name="thumbs_hide" id="thumbs_hide" />

			</form>

			<form style="display: none;" id="uploadForm2" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">

				<input type="file" name="thumbs_hide" id="thumbs_hide2" />

			</form>



	</div>











</body>

</html>



