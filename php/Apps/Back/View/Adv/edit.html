<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>
<script>
	$(function(){
		//上传
		$('#uploadForm').ajaxForm({
		  dataType: 'json',
		    success: function(data){
		    	// console.log(data);
	    		if(data.up_msg=='OK'){
	    			data.up_msg = '上传成功！';
					$('.img_sample').attr('src','__ROOT__/Attached/'+data.img_url);
					$('#thumbs').val(data.img_url);
	    		}
	    		$('.tips').text(data.up_msg);
		    },
		    beforeSubmit:function(){
				$('.tips').text('正在上传……');
		    }
		  });
		  $('.go_up').click(function(){
		  		$('#thumbs_hide').click();
		  });
		  $('#thumbs_hide').change(function(){
				$('#uploadForm').submit();
		  });
		  //上传
		$('#uploadForm2').ajaxForm({
		  dataType: 'json',
		    success: function(data){
		    	// console.log(data);
	    		if(data.up_msg=='OK'){
	    			data.up_msg = '上传成功！';
					$('.img_sample2').attr('src','__ROOT__/Attached/'+data.img_url);
					$('#thumbs2').val(data.img_url);
	    		}
	    		$('.tips2').text(data.up_msg);
		    },
		    beforeSubmit:function(){
				$('.tips2').text('正在上传……');
		    }
		  });
		  $('.go_up2').click(function(){
		  		$('#thumbs_hide2').click();
		  });
		  $('#thumbs_hide2').change(function(){
				$('#uploadForm2').submit();
		  });
	});
</script>
</head>
<div class="x-body">
		<form  method="post" id="fm1" class="layui-form" >
			<input type="hidden" name="id" value="{$res.id}" />
			<table class="tbl3 bg_f w_100 rad_10 mt_10 clr_79 w_100"  >
				<tr  style="display: none;"><td>名称：</td><td><input type="text" name="name" class="layui-input" value="{$res.name}"/></td></tr>
				<tr><td style="width:100px;">类型：</td><td>
					<select name="type" class="layui-select" lay-verify="required">
						<option value="">请选择</option>
						<foreach name="adv_type" item="v" key="k">
							<if condition="$res.type eq $k">
								<option value="{$k}" selected="true">{$v}</option>
							<else />
								<option value="{$k}">{$v}</option>
							</if>
						</foreach>
					</select>
				</td></tr>
<!-- 				<tr ><td>文字介绍：</td>
					<td><textarea type="text" name="summary" class="layui-textarea" >{$res.summary}</textarea></td>
				</tr>
				<tr ><td>文字介绍en：</td>
					<td><textarea type="text" name="summary_en" class="layui-textarea" >{$res.summary_en}</textarea></td>
				</tr> -->
				<tr><td>图片：</td><td>
					<if condition="$res.thumbs neq ''">
						<img class="img_sample " src="__ROOT__/Attached/{$res.thumbs}" style="width:80px;height:80px;" />
					<else />
						<img class="img_sample " src="__PUBLIC__/images/back/no_pic.gif" style="width:80px;height:80px;" />
					</if>
					<input name="thumbs" id="thumbs" type="hidden"  value="{$res.thumbs}" />
					 <input type="button" class="go_up layui-btn" value="上传"  />
					 <span class="tips">2M以内</span>
				</td></tr>
<!-- 				<tr><td>图片手机：</td><td>
					<if condition="$res.thumbs2 neq ''">
						<img class="img_sample2 " src="__ROOT__/Attached/{$res.thumbs2}" style="width:80px;height:80px;" />
					<else />
						<img class="img_sample2 " src="__PUBLIC__/images/back/no_pic.gif" style="width:80px;height:80px;" />
					</if>
					<input name="thumbs2" id="thumbs2" type="hidden"  value="{$res.thumbs2}" />
					 <input type="button" class="go_up2 layui-btn" value="上传"  />
					 <span class="tips2">2M以内</span>
				</td></tr>		 -->
				<tr><td >链接：</td>
				<td><input type="text" name="url" class="layui-input"  value="{$res.url}"/>
				</td></tr>
				<tr><td></td><td><input type="submit" value="保存" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>
			</table>
		</form>
		<form style="display: none;" id="uploadForm" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">
			<input type="file" name="thumbs_hide" id="thumbs_hide" />
		</form>
		<form style="display: none;" id="uploadForm2" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUpload')}">
			<input type="file" name="thumbs_hide" id="thumbs_hide2" />
		</form>
</div>
<script>
  layui.use('form', function(){
  var form = layui.form;
  //监听提交
  form.on('submit(formDemo)', function(data){
    $.post("{:U('runEdit')}",$('#fm1').serialize(), function(d){
    	if(d.code == 1){
    		layer.alert("保存成功", {icon: 6},function () {
    			window.parent.location.reload();
    				var index = parent.layer.getFrameIndex(window.name);
	                //关闭当前frame
	                parent.layer.close(index);
            });
    	}
    },'json');
      return false;
  });
});
</script>
</body>
</html>