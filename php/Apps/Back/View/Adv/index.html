<!DOCTYPE html>

<html lang="zh-CN">

<head>

<include file="Public:header" />

<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>

<script>

	$(function(){

		//上传

		$('#uploadForm').ajaxForm({

		  	dataType: 'json',  

		    success: function(data){

		    	console.log(data);

		    		if(data.up_msg=='OK'){

		    			data.up_msg = '上传成功！';

						$('.img_sample').attr('src','__ROOT__/Attached/'+data.img_url);

						$('#thumbs').val(data.img_url);

		    		}

		    		$('.tips').text(data.up_msg);

		    },



		    beforeSubmit:function(){

		    		$('.tips').text('正在上传……');

		    }

		  });  



		  $('.go_up').click(function(){

		  		$('#thumbs_hide').click();

		  });



		  $('#thumbs_hide').change(function(){

				$('#uploadForm').submit();

		  });



		// //验证

		// $("#fm1").validate({

		// 	rules: {

		// 		name:"required",

		// 		type:"required"

		// 	  },

		//         messages: {

		// 			name:' *',

		// 			type:" *"

		// 	 },



		// 	 errorPlacement: function(error, element) {

		// 	 	error.addClass('clr_r');

		// 		if (element.is(':radio') || element.is(':checkbox')) { //如果是radio或checkbox

		// 			var eid = element.attr('name'); //获取元素的name属性

		// 			error.appendTo(element.parent()); //将错误信息添加当前元素的父结点后面

		// 		}else{

		// 			error.insertAfter(element);

		// 		}

		// 	}

		// });	

	});



</script>



</head>





<body>

	<div class="x-body">



	 <button id="add_click" class="layui-btn" >

	 <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button>



	 <script>

		 	$('#add_click').click(function(){

		 		var aa = "{:U('add')}";

		 		x_admin_show('添加内容', aa);

		 	});

		 </script>



		<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79 w_100">

			<thead>

				<tr class="al_lt"><th>缩略图</th><th>类型</th><th>排序</th><th class="al_lt">操作</th></tr>

			</thead>

			<tbody>

				<foreach name="lists" item="v">

					<tr>

						<td>

							<if condition="$v.thumbs neq ''">

								<a class="clr_3 f_14" title="查看大图" href="__ROOT__/Attached/{$v.thumbs}" target="_blank" title="{$v.name}">

								<img src="__ROOT__/Attached/{$v.thumbs}" style="width:50px;height:50px;"  /></a>	

							</if>

						</td>

				<!-- 		<td>{$v.name}</td> -->

						<td>{$adv_type[$v['type']]}</td>

						<td><input class="order_blur layui-input"  value="{$v.order_id}" myid="{$v.id}" style="width:50px;"/></td>

						<td>

							<a class="bg_g block_a edit_item" href="javascript:;" my_url="{:U('edit',array('id'=>$v['id']))}" >

								<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>

							</a>

							<a class="bg_r block_a del_item" href="javascript:;" my_url="{:U('del',array('id'=>$v['id']))}" >

								<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>

							</a>

						</td>

					</tr>	

				</foreach>

			</tbody>

		</table>

	</div>





	<div class="pager" style="padding-left:20px;">

			{$page}

		</div>



	<script>



	$('.edit_item').click(function(){

		x_admin_show('编辑',$(this).attr('my_url'));

	});



	$('.del_item').click(function(){

		var aa = $(this);

		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){

			$.getJSON(aa.attr('my_url'), function(d){

				if(d.code==1){

					history.go(0);

				}

			});



			layer.close(index);

		});

	});





		$('.order_blur').blur(function(){

			$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});

		});		

	</script>



</body>



</html>







