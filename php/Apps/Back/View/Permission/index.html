<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
</head>
<body>
<div class="x-body">
	<button class="layui-btn add_item" my_url="{:U('add')}"> <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加管理员</button>
	<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79 w_100" >
	<thead>
		<tr class="al_lt"><th >名称</th><th width="200">操作</th></tr>
	</thead>
		<foreach name="lists" item="v">
			<tr>
				<td >{$v.name}</td>
				<td>
					<a href="javascript:;" my_url="{:U('edit', array('id'=>$v['id']))}" class="edit_item bg_g block_a " title="编辑" >
						<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
					</a>
					&nbsp;
					<a href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" class="del_item bg_r block_a " title="删除" >
						<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
					</a>
				</td>
			</tr>
		</foreach>
	</table>
</div>
<script>
	$('.add_item').click(function(){
		x_admin_show('添加管理员',$(this).attr('my_url'));
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
</script>
</body>
</html>