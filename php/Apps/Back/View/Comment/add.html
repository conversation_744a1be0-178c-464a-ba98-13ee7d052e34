<!DOCTYPE html>

<html lang="zh-CN">

<head>

<include file="Public:header" />



<link rel="stylesheet" href="__PUBLIC__/kinder/themes/default/default.css" />

<link rel="stylesheet" href="__PUBLIC__/kinder/plugins/code/prettify.css" />

<script charset="utf-8" src="__PUBLIC__/kinder/kindeditor-min.js"></script>

<script charset="utf-8" src="__PUBLIC__/kinder/lang/zh_CN.js"></script>

<script charset="utf-8" src="__PUBLIC__/kinder/plugins/code/prettify.js"></script>

<script type="text/javascript" src="__PUBLIC__/js/My97DatePicker/WdatePicker.js"></script>

<script charset="utf-8" src="__PUBLIC__/js/jquery.form.js"></script>

<script type="text/javascript" src="__PUBLIC__/js/jquery.validate.min.js"></script>

<script>



	KindEditor.ready(function(K) {

		var editor1 = K.create('textarea[name="content"]', {

			cssPath : '__PUBLIC__/kinder/plugins/code/prettify.css',

			uploadJson : '__PUBLIC__/kinder/upload_json.php',

			fileManagerJson : '__PUBLIC__/kinder/file_manager_json.php',

			allowFileManager : true,

			afterBlur: function () { this.sync(); },//注意这里（异步提交时需要同步）

			afterCreate: function () {

				var self = this;

				self.sync();//把富文本编辑器的内容放到 文本域里面去。



			}

		});

		prettyPrint();

	});

	



$(function(){



	//上传

		$('#uploadForm').ajaxForm({

		  dataType: 'json',  

		    success: function(data){

		    		if(data.up_msg=='OK'){

		    			data.up_msg = '上传成功！';

		    			// $('#img_wrap').html('<img src="__ROOT__/Attached/'+data.img_url+'" style="width:80px;height:80px;" />');

		    			$('#img_wrap').html(data.img_url);

		    			$('#thumbs').val(data.img_url);

		    		}

		    		

		    		$('.tips').text(data.up_msg);

		    },

		    beforeSubmit:function(){

		    }

		  });  

		  

		  $('.go_up').click(function(){

		  		$('#thumbs_hide').click();

		  });

		  

		  $('#thumbs_hide').change(function(){

				$('#uploadForm').submit();

		  });



//上传

		$('#uploadForm2').ajaxForm({

		  dataType: 'json',  

		    success: function(data){

		    	// console.log(data);

		    		if(data.up_msg=='OK'){

		    			data.up_msg = '上传成功！';

		    			$('#img_wrap2').text(data.img_url);

		    			$('#pic').val(data.img_url);

		    			$('#ext').val(data.info.thumbs_hide.ext);

		    			$('#size').val(data.info.thumbs_hide.size);

		    		}

		    		

		    		$('.tips2').text(data.up_msg);

		    },

		    beforeSubmit:function(){

		    }

		  });  

		  

		  $('.go_up2').click(function(){

		  		$('#thumbs_hide2').click();

		  });

		  

		  $('#thumbs_hide2').change(function(){

				$('#uploadForm2').submit();

		  });

		  

		//   //验证

		// $("#fm1").validate({

		// 	rules: {

		// 		title: "required"

		// 	  },

		// 	   errorPlacement: function(error, element) {

		// 	 		error.addClass('alert_tips');

		// 			element.after('<span class="pos_rela alert_rela ver_mid"></span>');

		// 			element.siblings('.alert_rela').append(error);

		// 			error.width(error.html().length*12);

		// 	}

		//   });





});  

</script>

</head>





	<div class="x-body">

			<form id="fm1" class="layui-form" method="post">

				<input type="hidden" name="type" value="{$_GET['type']}" />

				<input type="hidden" name="cat_id" value="{$_GET['cid']}" />

				<table class="layui-table">

					<tr><td style="width:100px;">标题</td><td><input lay-verify="required"  name="name" class="layui-input"/></td></tr>

						<tr>

							<td>附件上传</td>

							<td><span id="img_wrap2"></span>

								<span>

									<input name="pic" id="pic" type="hidden"  />

									<input name="ext" id="ext" type="hidden"  />

									<input name="size" id="size" type="hidden"  />

									<input type="button" class="go_up2 layui-btn" value="上传"  />

									<span class="tips2">（格式：2M以内，rar/zip/pdf ）</span>

								</span>

							</td>

						</tr>



							<!-- <tr ><td>内容</td><td><textarea name="content" style="width:800px;height:500px;visibility:hidden;" ></textarea></td></tr> -->



<!-- 

						<tr><td class="al_rt">发布日期</td><td>

							<input name="pub_date" id="pub_date" style="width:200px;"  class="layui-input"/>

						</td></tr> -->



					<tr><td></td><td><input type="submit" value="添加" lay-submit lay-filter="formDemo" class="layui-btn" /></td></tr>

				</table>

			</form>



			<script>

layui.use('laydate', function(){

  var laydate = layui.laydate;

  

  //执行一个laydate实例

  laydate.render({

    elem: '#pub_date' //指定元素

  });

});





 layui.use('form', function(){

  var form = layui.form;

  

  //监听提交

  form.on('submit(formDemo)', function(data){

    $.post("{:U('runAdd')}",data.field, function(d){

    	if(d.code == 1){

    		layer.alert("增加成功", {icon: 6},function () {

    			window.parent.location.reload();

    				var index = parent.layer.getFrameIndex(window.name);

	                //关闭当前frame

	                parent.layer.close(index);



            });

    	}

    },'json');



      return false;

  });





});





</script>





			<form style="display: none;" id="uploadForm2" enctype="multipart/form-data"  method="post" action="{:U('Tools/runUploadPDF')}">

				<input type="file" name="thumbs_hide" id="thumbs_hide2" />

			</form>



	</div>











</body>

</html>



