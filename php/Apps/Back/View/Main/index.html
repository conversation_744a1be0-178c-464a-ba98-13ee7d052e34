<!doctype html>
<html lang="en">
<head>
	<include file="Public:header" />
</head>
<body>
	<!-- 顶部开始 -->
	<div class="container" style="background: #fff;">
		<div  style="background: #fff;z-index: 999;position: relative;">
			<div class="logo"><a href="javascript:;"  style="color:#333;">后台管理 <span class="clr_ora f_24" >系统</span></a></div>
			<div class="left_open" >
				<i  title="展开左侧栏"  class="fa fa-bars" style="color:#333;font-size: 24px;" aria-hidden="true"></i>
			</div>
			<ul class="layui-nav right" lay-filter="" style="z-index: 99;">
	<!--                   <li class="layui-nav-item to-index"><a href="{:U('Home/Index/index')}" target="_blank" style="color:#333;">
	<i class="fa fa-home"  aria-hidden="true" style="font-size: 16px;"></i>  前台首页</a></li> -->
	<li class="layui-nav-item to-index"><a href="{:U('Index/exits')}"  style="color:#333;" >
		<i class="fa fa-reply-all" aria-hidden="true" style="font-size: 14px;"></i> 退出系统</a></li>
	</ul>
</div>
<div class="left-nav">
	<div id="side-nav">
		<ul id="nav">
			<foreach name="leftNav" item="v">
				<li style="border-bottom:0;padding:0 10px;">
					<php>
						$a = 'cat1_'.$v['id'];
						//如果设置了本栏目权限，则显示,子管理员
						if($session_admin['type'] == 0){
					</php>
					<a href="javascript:;" style="color:#aeb2b7;">
						{$v.icon}
						<cite>{$v.name}</cite>
						<i class="iconfont nav_right">&#xe697;</i>
					</a>
					<ul class="sub-menu">
						<foreach name="v.child" item="v2">
							<li style="border-bottom:0;background: #35404d;">
								<a _href="{$v2.url}" style="color:#aeb2b7;">
									<if condition="$v2['icon'] neq ''">
										{$v2.icon}
										<else />
										<i class="fa fa-chevron-right" aria-hidden="true"></i>
									</if>
									<cite>{$v2.name}</cite>
								</a>
							</li>
						</foreach>
					</ul>
					<php>}else{
						if($session_admin['permission'][$a]!=''){
					</php>
					<a href="javascript:;" style="color:#aeb2b7;">
						{$v.icon}
						<cite>{$v.name}</cite>
						<i class="iconfont nav_right">&#xe697;</i>
					</a>
					<ul class="sub-menu">
						<foreach name="v.child" item="v2">
							<php>
								$cat2 = explode(',', $session_admin['permission'][$a]);
								if(in_array($v2['id'], $cat2)){
							</php>
							<li style="border-bottom:0;background: #35404d;">
								<a _href="{$v2.url}"  style="color:#aeb2b7;">
									<if condition="$v2['icon'] neq ''">
										{$v2.icon}
										<else />
										<i class="fa fa-chevron-right" aria-hidden="true"></i>
									</if>
									<cite>{$v2.name}</cite>
								</a>
							</li>
							<php>
							}
						</php>
					</foreach>
				</ul>
				<php>
				}
			}
		</php>
	</li>
</foreach>
</ul>
</div>
</div>
<script type="text/javascript">
				// $('.sub-menu').show();
			</script>
			<div class="page-content"   style="bottom:0;top:10px;">
				<div class="layui-tab-content" >
					<div class="layui-tab-item layui-show">
						<iframe src="{:U('Main/welcome')}" frameborder="0" scrolling="yes" class="x-iframe" name="myframe" id="myframe" ></iframe>
					</div>
				</div>
			</div>
			<script>
				$('.sub-menu li').click(function(){
					var a = $(this).find('a').attr('_href');
					$(this).find('a').css('color','#ff6c60');
					$(this).siblings().find('a').css('color','#aeb2b7');
					$('#myframe').attr('src',a);
				});
			</script>
			<div class="page-content-bg" ></div>
		</div>
	</body>
	</html>