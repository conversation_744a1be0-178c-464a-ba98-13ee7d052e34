<!DOCTYPE html>
<html lang="zh-CN">
<head>
<include file="Public:header" />
</head>
	<div  class="x-body">
<div class="fl">
	  <button id="add_click" class="layui-btn" > <i class="fa fa-plus-circle" aria-hidden="true"></i> 添加</button>
		   <button id="del_click" class="layui-btn" style="background:#ff6c60;" ><i class="fa fa-trash-o" aria-hidden="true" ></i> 删除</button>
			<!-- <a href="{:U('toExcel')}" class=" layui-btn" style="background: #78cd51;" > <i class="fa fa-file-excel-o" aria-hidden="true"></i> 导出Excel</a> -->
		 <script>
		 	$('#add_click').click(function(){
		 		var aa = "{:U('add', array('cid'=>$_GET['cid'], 'type'=>$_GET['type']))}";
		 		x_admin_show('添加内容', aa);
		 	});
		 </script>
</div>
<div class="fr">
		<form action="{:U('index')}" id="fms">
			<input name="cid" value="{$_GET['cid']}" type="hidden" />
			<input lay-verify="required"  name="key" id="key" placeholder="输入姓名" class="layui-input" style="width:200px;float: left;" />
			<input type="submit" id="search_click" value="搜索" class="layui-btn" style="float: left;" />
			<div style="clear: both;"></div>
		</form>
		<script>
			$('#search_click').click(function(){
				if($('#key').val()==''){
					alert('请输入关键词');
					$('#key').focus();
					return false;
				}
			});
		</script>
	</div>
	<div style="clear: both;"></div>
<form id="fm1">
		<table class="tbl2 bg_f w_100 rad_10 mt_10 clr_79">
			<thead>
				<tr style="text-align: left;">
					<th><label id="checkAll"><input type="checkbox" class="ver_mid" /> <span class="ver_mid">全选</span></label></th>
					<th>工厂名称</th>
					<th>地区</th>
					<th>交货地</th>
					<th>是否热门</th>
					<th>加入时间</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				<foreach name="lists" item="v">
				<tr>
					<td><label><input type="checkbox" name="id[]" value="{$v.id}" class="ver_mid per_check" /> <span class="ver_mid">{$v.id}</span></label></td>
					<td>{$v.title}</td>
					<td>{$v.diqu}</td>
					<td>{$jiaohuodi[$v['jiaohuodi']]}</td>
					<td>
						<if condition="$v.is_hot eq 1">
						 	<span class="clr_r">是</span>
						<else />
							否
						</if>
					</td>
					<td>{$v.add_time|date="Y-m-d H:i",###}</td>
					<td>
						<a class="bg_g block_a edit_item" href="javascript:;" my_url="{:U('edit', array('id'=>$v['id'], 'cid'=>$v['cat_id'], 'type'=>$_GET['type']))}" title="编辑" >
							<i class="fa fa-pencil-square-o" aria-hidden="true"  style="font-size: 14px;color:#fff;"></i>
						</a>
						&nbsp;
						<a class="bg_r block_a del_item" href="javascript:;" my_url="{:U('del', array('id'=>$v['id']))}" title="删除" >
							<i class="fa fa-trash-o" aria-hidden="true" style="font-size: 14px;color:#fff;"></i>
						</a>
					</td>
				</tr>
				</foreach>
			</tbody>
		</table>
</form>
		<div class="pager mt_10">
			{$page}
		</div>
	</div>
<script>
	$('#del_click').click(function(){
		if(!$('#fm1 input[name="id[]"]').is(':checked')){
			alert('请选择要删除的选项');
			return false;
		}else{
			if(confirm('确认删除?')){
				$.post('{:U("delAll")}', $('#fm1').serialize(), function(d){
					if(d.code==1){
						history.go(0);
					}
				},'json');
			}
		}
	});
	$('#checkAll').click(function(){
		$('#fm1 input[type=checkbox]').prop('checked', $(this).find('input[type=checkbox]').prop('checked'));
	});
	$('.status_sele').change(function(){
		if($(this).val()!=''){
			$.getJSON('{:U("runCheck")}', {id:$(this).attr('myid'), status:$(this).val()}, function(d){
				if(d.code==1){
					history.go(0);
				}
			});
		}
	});
	$('.edit_item').click(function(){
		x_admin_show('编辑',$(this).attr('my_url'));
	});
	$('.del_item').click(function(){
		var aa = $(this);
		layer.confirm('确认删除？', {icon: 3, title:'提示'}, function(index){
			$.getJSON(aa.attr('my_url'), function(d){
				if(d.code==1){
					history.go(0);
				}
			});
			layer.close(index);
		});
	});
	$('.recom_click').click(function(){
		$.getJSON('{:U("runRecom")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.recom_click2').click(function(){
		$.getJSON('{:U("runRecom2")}',{id:$(this).attr('myid')}, function(d){if(d.code==1)history.go(0);});
	});
	$('.order_blur').blur(function(){
		$.getJSON('{:U("runOrder")}',{id:$(this).attr('myid'), order_id:$(this).val()}, function(d){if(d.code==1)history.go(0);});
	});
</script>
</body>
</html>