<!DOCTYPE html>
<html>
<head>
<title>青岛三匹马实业</title>
<meta name="keywords" content="青岛三匹马实业" />
<meta name="description" content="青岛三匹马实业" />
<include file="Public:header" />
<script type="text/javascript" src="__PUBLIC__/js/ion.rangeSlider.min.js"></script>
<link rel="stylesheet" type="text/css" href="__PUBLIC__/css/ion.rangeSlider.min.css" />
</head>

<body>



	<div class="border1 pad_20 bg_f mt_10">
		<ul class="mian_ul clearfix al_ct f_18">
			<foreach name="chandi" item="v" key="k">
				<if condition="$def eq $v['id']">
					<li><a href="{:U('index',['def'=>$v['id']])}" class="clr_3 mian_cur">{$v.name}</a></li>
				<else />
					<li><a href="{:U('index',['def'=>$v['id']])}" class="clr_3 mian_norm">{$v.name}</a></li>
				</if>
			</foreach>
		</ul>

		<ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">
			<li  style="display: inline-block;" class="ver_top">产　地：</li>
			<li  style="display: inline-block;"  >
				<div id="chandi_click">
					<foreach name="chandi_default" item="v">
						<if condition="strpos($_GET['cd'], '|'.$v['id'].'-|') nheq false">
							<a href="#." class="clr_3 mr_10 xuan_cur" myid="{$v.id}">{$v.name}</a>
						<else />
							<a href="#." class="clr_3 mr_10 " myid="{$v.id}">{$v.name}</a>
						</if>
					</foreach>
				</div>
			</li>
		</ul>

		<ul class="chandi_ul f_14 mt_20 fl "  id="leixing_ul" style="line-height:30px; margin-left:5px;width:375px;">
			<li>类　型：</li>
			<foreach name="xls_leixing" item="v">
				<if condition="strpos($_GET['leixing'], '|'.$v.'|') nheq false">
					<li><a href="#." class="clr_3 xuan_cur">{$v}</a></li>
				<else />
					<li><a href="#." class="clr_3">{$v}</a></li>
				</if>
			</foreach>
		</ul>

		<ul class="chandi_ul f_14 mt_20 fl " id="niandu_ul" style="line-height:30px; margin-left:5px; width:330px;">
			<li>年　度：</li>
			<foreach name="xls_niandu" item="v">
				<if condition="strpos($_GET['pihao_kunkao'], '|'.$v.'|') nheq false">
					<li><a href="#." class="clr_3 xuan_cur">{$v}</a></li>
				<else />
					<li><a href="#." class="clr_3">{$v}</a></li>
				</if>
			</foreach>
		</ul>

		<div class="cls"></div>
		<div id="child_wraper">
			<foreach name="chandi_default" item="v">
				<span id="child_{$v.id}">
					<if condition="$_GET['cd2'] neq '' and $_GET['cd2'] neq '|'">
						<foreach name="cd2[$v['id']]" item="v2">
							<if condition="strpos($_GET['cd2'],  '|'.$v['id'].'-'.$v2['id'].'-|') nheq false">
								<a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="{$v.id}" myid="{$v2.id}">{$v2.name}</a>
							<else />
								<a href="#." class="clr_3 mr_10 f_14" pid="{$v.id}" myid="{$v2.id}">{$v2.name}</a>
							</if>
						</foreach>
					</if>
				</span>
			</foreach>
		</div>

		<div id="grandson_wraper" class="mt_10">
			<foreach name="xx" item="v" key="k">
				<foreach name="v" item="v2" >
					<span id="grandson_{$v2.id}" gpid="{$k}" pid="{$v2.id}">
						<if condition="$_GET['cd3'] neq '' and $_GET['cd3'] neq '|' and strpos($_GET['cd2'],  '|'.$k.'-'.$v2['id'].'-|') nheq false">
							 <foreach name="v2.children" item="v3">
								<if condition="strpos($_GET['cd3'],  '|'.$k.'-'.$v3['pid'].'-'.$v3['id'].'-|') nheq false">
									<a href="#." gpid="{$k}" pid="{$v3.pid}" myid="{$v3.id}" class="mr_10 clr_3 f_14 xuan_cur">{$v3.name}</a>
								<else />
									<a href="#." gpid="{$k}" pid="{$v3.pid}" myid="{$v3.id}" class="mr_10 clr_3 f_14">{$v3.name}</a>
								</if>
							</foreach>
						</if>
					</span>
				</foreach>
			</foreach>
		</div>




		<script type="text/javascript">

			//点击子产地
			$('#child_wraper').on('click', 'a', function(){
				var that  = $(this);

				if(!that.hasClass('xuan_cur')){
					that.addClass('xuan_cur');
				}else{
					that.removeClass('xuan_cur');
				}
			});

			//点击父产地
			$('#chandi_click').on('click', 'a', function(){
				var that  = $(this);
				if(!that.hasClass('xuan_cur')){
					$.getJSON('{:U("Tools/getCat")}', {id:that.attr('myid')}, function(d){
						// console.log(d)
						$.each(d, function(k,v){
							$('#child_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
							$('#grandson_wraper').append('<span id="grandson_'+v.id+'" gpid="'+v.pid+'"></span>')
						});
						that.addClass('xuan_cur');
					});
				}else{
					that.removeClass('xuan_cur');
				}

				$('#child_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
				$('#grandson_wraper a[gpid="'+that.attr('myid')+'"]').remove();
				//删除对应二级的占位
				$('#grandson_wraper span[gpid='+that.attr('myid')+']').remove();
			});
		</script>


		<script type="text/javascript">
			var leixing_str= '{$_GET["leixing"]}' == '' ? '|' : '{$_GET["leixing"]}' ;
			// console.log(leixing_str)
			$('#leixing_ul li a').click(function(){
				if(!$(this).hasClass('xuan_cur')){
					$(this).addClass('xuan_cur');
					if(leixing_str.indexOf('|'+$(this).text()+'|') ==-1){
						leixing_str += $(this).text()+'|';
					}
				}else{
					$(this).removeClass('xuan_cur');
					leixing_str = leixing_str.replace('|'+$(this).text()+'|', '|');
				}

				$('input[name="leixing"]').val(leixing_str);
			});
		</script>


		<script type="text/javascript">
			var niandu_str=  '{$_GET["pihao_kunkao"]}' == '' ? '|' : '{$_GET["pihao_kunkao"]}' ;
			$('#niandu_ul li a').click(function(){
				if(!$(this).hasClass('xuan_cur')){
					$(this).addClass('xuan_cur');
					if(niandu_str.indexOf('|'+$(this).text()+'|') ==-1){
						niandu_str += $(this).text()+'|';
					}
				}else{
					$(this).removeClass('xuan_cur');
					niandu_str = niandu_str.replace('|'+$(this).text()+'|', '|');
				}

				 $('input[name="pihao_kunkao"]').val(niandu_str);
			});
		</script>

		<div class="cls"></div>

		<ul class="chandi_ul f_14  fl" style="width:380px;">
			<li class="length">长　度</li>
			<li style="width:240px;"><input type="text" class="changdu_range" name="my_range" value=""   /></li>
		</ul>
		<script>
			$(".changdu_range").ionRangeSlider({
				type: "double",
				skin:"round",
					type: "double",
					min: 25,
					max: 32,
					from: '{$_GET["changdu_from"]}' == '' ? 25 : '{$_GET["changdu_from"]}',
					to: '{$_GET["changdu_to"]}' == '' ? 32 : '{$_GET["changdu_to"]}',
					step:0.1, 
					onFinish: function (data) { //拖动结束回调
						 // console.log(data)
						 $('input[name="changdu_from"]').val(data.from);
						 $('input[name="changdu_to"]').val(data.to);
					}
	 
			});
		</script>


		<ul class="chandi_ul f_14  fl"  style="width:380px;">
			<li class="length">马　值</li>
			<li style="width:240px;"><input type="text" class="mazhi_range" name="my_range" value=""   /></li>
		</ul>
		<script>
			$(".mazhi_range").ionRangeSlider({
				type: "double",
				skin:"round",
					type: "double",
					min: 2.5,
					max: 5.5,
					from: '{$_GET["mazhi_from"]}' == '' ? 2.5 : '{$_GET["mazhi_from"]}',
					to: '{$_GET["mazhi_to"]}' == '' ? 5.5 : '{$_GET["mazhi_to"]}',
					step:0.1, 
					onFinish: function (data) { //拖动结束回调
						 // console.log(data)
						 $('input[name="mazhi_from"]').val(data.from);
						 $('input[name="mazhi_to"]').val(data.to);
					}
	 
			});
		</script>

		<ul class="chandi_ul f_14 fl" style="width:380px;">
			<li  class="length">强　力</li>
			<li style="width:240px;"><input type="text" class="qiangli_range" name="my_range" value=""   /></li>
		</ul>
		<script>
			$(".qiangli_range").ionRangeSlider({
				type: "double",
				skin:"round",
					type: "double",
					min: 25,
					max: 32,
					from: '{$_GET["qiangli_from"]}' == '' ? 25 : '{$_GET["qiangli_from"]}',
					to: '{$_GET["qiangli_to"]}' == '' ? 32 : '{$_GET["qiangli_to"]}',
					step:0.1, 
					onFinish: function (data) { //拖动结束回调
						 // console.log(data)
							$('input[name="qiangli_from"]').val(data.from);
						 $('input[name="qiangli_to"]').val(data.to);
					}

	 
			});
		</script>




		<div class="cls"></div>

		<ul class="chandi_ul f_14  fl" style="width:380px;">
			<li class="length">回　潮</li>
			<li style="width:240px;"><input type="text" class="huichao_range" name="my_range" value=""   /></li>
		</ul>
		<script>
			$(".huichao_range").ionRangeSlider({
				type: "double",
				skin:"round",
					type: "double",
					min: 0,
					max: 10,
					from: '{$_GET["huichaolv_from"]}' == '' ? 0 : '{$_GET["huichaolv_from"]}',
					to: '{$_GET["huichaolv_to"]}' == '' ? 10 : '{$_GET["huichaolv_to"]}',
					step:0.1, 
					onFinish: function (data) { //拖动结束回调
						 // console.log(data)
						 $('input[name="huichaolv_from"]').val(data.from);
						 $('input[name="huichaolv_to"]').val(data.to);
					}
	 
			});
		</script>

		<ul class="chandi_ul f_14  fl"  style="width:380px;">
			<li class="length">整齐度</li>
			<li style="width:240px;"><input type="text" class="zhengqidu_range" name="my_range" value=""   /></li>
		</ul>
		<script>
			$(".zhengqidu_range").ionRangeSlider({
				type: "double",
				skin:"round",
					type: "double",
					min: 77,
					max: 90,
					from: '{$_GET["zhengqidu_from"]}' == '' ? 77 : '{$_GET["zhengqidu_from"]}',
					to: '{$_GET["zhengqidu_to"]}' == '' ? 90 : '{$_GET["zhengqidu_to"]}',
					step:0.1, 
					onFinish: function (data) { //拖动结束回调
						 // console.log(data)
						 $('input[name="zhengqidu_from"]').val(data.from);
						 $('input[name="zhengqidu_to"]').val(data.to);
					}
	 
			});
		</script>

		<ul class="chandi_ul f_14  fl" style="width:380px;">
			<li class="length">含　杂</li>
			<li style="width:240px;"><input type="text" class="hanza_range" name="my_range" value=""   /></li>
		</ul>
		<script>
			$(".hanza_range").ionRangeSlider({
				type: "double",
				skin:"round",
					type: "double",
					min: 0,
					max: 6,
					from: '{$_GET["hanzalv_from"]}' == '' ? 0 : '{$_GET["hanzalv_from"]}',
					to: '{$_GET["hanzalv_to"]}' == '' ? 6 : '{$_GET["hanzalv_to"]}',
					step:0.1, 
					onFinish: function (data) { //拖动结束回调
						 // console.log(data)
							$('input[name="hanzalv_from"]').val(data.from);
						 $('input[name="hanzalv_to"]').val(data.to);
					}
	 
			});
		</script>


		<div class="cls"></div>


		<div class="bottom">
			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="green">白棉1/2/3级</li>
				<li style="width:240px;">
					<input type="text" class="bm123_range" name="bm123_range" value=""   />
				</li>
			</ul>
			<script>
				$(".bm123_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 100,
						from: '{$_GET["bm123_from"]}' == '' ? 0 : '{$_GET["bm123_from"]}',
						to:'{$_GET["bm123_to"]}' == '' ? 100 : '{$_GET["bm123_to"]}',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="bm123_from"]').val(data.from);
							 $('input[name="bm123_to"]').val(data.to);
						}
		 
				});
			</script>
			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="green">白棉4/5级</li>
				<li style="width:240px;"><input type="text" class="bm45_range" name="bm45_range" value=""   /></li>
			</ul>
			<script>
				$(".bm45_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 100,
						from: '{$_GET["bm45_from"]}' == '' ? 0 : '{$_GET["bm45_from"]}',
						to: '{$_GET["bm45_to"]}' == '' ? 100 : '{$_GET["bm45_to"]}',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="bm45_from"]').val(data.from);
							 $('input[name="bm45_to"]').val(data.to);
						}

		 
				});
			</script>
			<ul class="chandi_ul f_14  fl"  style="width:380px;">
				<li class="green">淡点污1/2/3级</li>
				<li style="width:240px;">
					<input type="text" class="ddw123_range" name="ddw123_range" value=""   />
				</li>
			</ul>
			<script>
				$(".ddw123_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 100,
						from: '{$_GET["ddw123_from"]}' == '' ? 0 : '{$_GET["ddw123_from"]}',
						to: '{$_GET["ddw123_to"]}' == '' ? 100 : '{$_GET["ddw123_to"]}',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="ddw123_from"]').val(data.from);
							 $('input[name="ddw123_to"]').val(data.to);
						}
		 
				});
			</script>

			<div class="cls"></div>

			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="green">淡黄染1/2/3级</li>
				<li style="width:240px;">
					<input type="text" class="dhr123_range" name="dhr123_range" value=""   />
				</li>
			</ul>
			<script>
				$(".dhr123_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 100,
						from: '{$_GET["dhr123_from"]}' == '' ? 0 : '{$_GET["dhr123_from"]}',
						to: '{$_GET["dhr123_to"]}' == '' ? 100 : '{$_GET["dhr123_to"]}',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="dhr123_from"]').val(data.from);
							 $('input[name="dhr123_to"]').val(data.to);
						}
		 
				});
			</script>
			<ul class="chandi_ul f_14  fl" style="width:380px;">
				<li class="green">黄染1/2级</li>
				
				<li style="width:240px;"><input type="text" class="hr12_range" name="hr12_range" value=""   /></li>
			</ul>
			<script>
				$(".hr12_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 100,
						from: '{$_GET["hr12_from"]}' == '' ? 0 : '{$_GET["hr12_from"]}',
						to: '{$_GET["hr12_to"]}' == '' ? 100 : '{$_GET["hr12_to"]}',
						step:0.1, 
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="hr12_from"]').val(data.from);
							 $('input[name="hr12_to"]').val(data.to);
						}
		 
				});
			</script>
			
			<div class="cls"></div>
		</div>


		<ul class="chandi_ul f_14 mt_20 " id="jiaohuodi_ul" style="width:760px;">
			<li>交货地：</li>
			<foreach name="jiaohuodi" item="v" key="k">
				<if condition="$_GET['jiaohuodi'] eq $k">
					<li v="{$k}"><a href="#." class="clr_3 xuan_cur">{$v}</a></li>
				<else />
					<li v="{$k}"><a href="#." class="clr_3">{$v}</a></li>
				</if>
			</foreach>
		</ul>

		<form action="{:U('rDingzhi')}" id="fm1" method="post">
			<input id="act" name="act" value="" type="hidden" />
			<div class="dis_n">
				<!-- 交货地 -->
				<input name="jiaohuodi" value="{$_GET['jiaohuodi']}" />
				<input name="def" value="{$def}" />
				<!-- 父产地 -->
				<input name="cd" value="" />
				<!-- 子产地 -->
				<input name="cd2" value="" />
				<!-- 孙产地 -->
				<input name="cd3" value="" />
				<textarea id="url" name="url"></textarea>
				<input name="leixing" value="{$_GET['leixing']}" />
				<input name="pihao_kunkao" value="{$_GET['pihao_kunkao']}" />
				<input name="changdu_from" value="{$_GET['changdu_from'] == '' ? 25 : $_GET['changdu_from']}" />
				<input name="changdu_to" value="{$_GET['changdu_to'] == '' ? 32 : $_GET['changdu_to']}"/>
				<input name="qiangli_from" value="{$_GET['qiangli_from'] == '' ? 25 : $_GET['qiangli_from']}"/>
				<input name="qiangli_to" value="{$_GET['qiangli_to'] == '' ? 32 : $_GET['qiangli_to']}"/>
				<input name="mazhi_from" value="<php>if($_GET['mazhi_from'] == ''){echo (2.5);}else{echo ($_GET['mazhi_from']);}</php>"/>
				<input name="mazhi_to" value="<php>if($_GET['mazhi_to'] == ''){echo (5.5);}else{echo ($_GET['mazhi_to']);}</php>"/>
				<input name="huichaolv_from" value="{$_GET['huichaolv_from'] == '' ? 0 : $_GET['huichaolv_from']}"/>
				<input name="huichaolv_to" value="{$_GET['huichaolv_to'] == '' ? 10 : $_GET['huichaolv_to']}"/>
				<input name="hanzalv_from" value="{$_GET['hanzalv_from'] == '' ? 0 : $_GET['hanzalv_from']}"/>
				<input name="hanzalv_to" value="{$_GET['hanzalv_to'] == '' ? 5 : $_GET['hanzalv_to']}"/>
				<input name="zhengqidu_from" value="{$_GET['zhengqidu_from'] == '' ? 77 : $_GET['zhengqidu_from']}"/>
				<input name="zhengqidu_to" value="{$_GET['zhengqidu_to'] == '' ? 90 : $_GET['zhengqidu_to']}"/>
				<input name="bm123_from" value="{$_GET['bm123_from'] == '' ? 0 : $_GET['bm123_from']}"/>
				<input name="bm123_to" value="{$_GET['bm123_to'] == '' ? 100 : $_GET['bm123_to']}"/>
				<input name="bm45_from" value="{$_GET['bm45_from'] == '' ? 0 : $_GET['bm45_from']}"/>
				<input name="bm45_to" value="{$_GET['bm45_to'] == '' ? 100 : $_GET['bm45_to']}"/>
				<input name="ddw123_from" value="{$_GET['ddw123_from'] == '' ? 0 : $_GET['ddw123_from']}"/>
				<input name="ddw123_to" value="{$_GET['ddw123_to'] == '' ? 100 : $_GET['ddw123_to']}"/>
				<input name="dhr123_from" value="{$_GET['dhr123_from'] == '' ? 0 : $_GET['dhr123_from']}"/>
				<input name="dhr123_to" value="{$_GET['dhr123_to'] == '' ? 100 : $_GET['dhr123_to']}"/>
				<input name="hr12_from" value="{$_GET['hr12_from'] == '' ? 0 : $_GET['hr12_from']}"/>
				<input name="hr12_to" value="{$_GET['hr12_to'] == '' ? 100 : $_GET['hr12_to']}"/>
			</div>
			<ul class="chandi_ul f_14 mt_20">
				<li>
					交货仓库：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入仓库名称"  
						value="{$_GET['cangchumingcheng']}" name="cangchumingcheng" style="width:350px;" /> 
					轧花厂：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入轧花厂名称或编号" 
						value="{$_GET['jiagongchang']}" name="jiagongchang"  style="width:350px;" /> 
					<input type="button" id="tijiaoClick" value="　添加到个性需求　" class="f_14 clr_f bg_lan border0 pad_5" />
					<!-- <a href="#" class="clr_ora f_wei">+添加到需求定制</a> -->
				</li>
			</ul>

			<div class="dis_n">
				<input name="hot" value="{$_GET['hot']}" />
				<input name="zuiyou" value="{$_GET['zuiyou']}" />
				<input name="xiangtong"  value="{$_GET['xiangtong']}" />
				<input name="dan"  value="{$_GET['dan']}" />
				<input name="jicha"  value="{$_GET['jicha']}" />
			</div>
		</form>


		<script type="text/javascript">

			$('#jiaohuodi_ul li').click(function(){
				$(this).find('a').addClass('xuan_cur');
				$(this).siblings().find('a').removeClass('xuan_cur');
				$('input[name="jiaohuodi"]').val($(this).attr('v'));
			});

			$('#tijiaoClick').click(function(){
				//提交表单，而不是导出excel
				$('#act').val('');

				//所选的父产地
				var cd = '|';
				$('#chandi_click a').each(function(k, v){
					if($(this).hasClass('xuan_cur')){
						// cd += $(this).text() + '|';
						// console.log('v----'+$(this).attr('myid'));
						cd += $(this).attr('myid') + '-|';
					}
				});

				$('input[name="cd"]').val(cd);	

				
				//所选的子产地
				var cd2 = '|';
				$('#child_wraper a').each(function(k, v){
					if($(this).hasClass('xuan_cur')){
						// cd2 += $(this).text() + '|';
						// console.log('v----'+$(this).attr('myid'));
						cd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
					}
					
				});

				$('input[name="cd2"]').val(cd2);

				//所选的孙产地
				var cd3 = '|';
				$('#grandson_wraper a').each(function(k, v){
					if($(this).hasClass('xuan_cur')){
						// cd3+= $(this).text() + '|';
						// console.log('v----'+$(this).attr('myid'));
						cd3+= $(this).attr('gpid')+'-'+$(this).attr('pid')+'-'+$(this).attr('myid')+ '-|';
					}
					
				});

				$('input[name="cd3"]').val(cd3);	

				$('#url').val($('#fm1').serialize());


				$('#fm1').submit();
			});
		</script>
	</div>

</body>


</html>