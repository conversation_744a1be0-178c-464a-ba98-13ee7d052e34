<!DOCTYPE html>

<html>

<head>

	<title>棉花棠</title>

	<meta name="keywords" content="棉花棠" />

	<meta name="description" content="棉花棠" />

	<include file="Public:header" />



</head>



<body class="bg_f2">

	<include file="Public:top" />

	

	<div class="login_bg min_w">

		<div class="m_auto clearfix">

			<div class="login_box bg_f fr mt_40">

				<div class="login_box_tt f_18 padl_30">修改密码</div>	

				<div class="padl_40">

					<form id="fm1">

						<div class="border1 pad_10 mt_30">

							<img src="__PUBLIC__/images/user.png" class="ver_mid" />

							<input placeholder="手机号" name="username" id="username" value="" class="f_16 clr_9  border0 ver_mid ml_10" />

						</div>

						<div class="border1 pad_10 mt_20 pos_rela">

							<img src="__PUBLIC__/images/mobile.png" class="ver_mid" />

							<input class="f_14 clr_9 border0 ver_mid ml_10" name="mobile_code" placeholder="手机验证码" style="width:100px;" />

							<input type="button" value="获取验证码" class="bg_b clr_f border0 pad_10"  id="getcode" style="position: absolute;top:10px;right:10px;" />

						</div>

						<div class="border1 pad_10 mt_20">

							<img src="__PUBLIC__/images/password.png" class="ver_mid" />

							<input placeholder="输入新密码"  name="password" id="password" value=""  type="password" class="f_16 clr_9  border0 ver_mid ml_10" />

						</div>



						<div class=" padt_30">

							<input type="submit" value="确认修改" class="f_16 clr_f bg_b w_100p border0 padt_10" />

							<div class="f_14 al_rt mt_10">

								<a href="{:U('login')}" class="clr_b">立即登录</a>

							</div>

						</div>

					</form>

					

					<script type="text/javascript">



					$('#getcode').click(function(){

						if(!/^1\d{10}$/.test($('#username').val())){

							alert('请输入正确的手机号');	

							return false;

						}

						

						$.getJSON("{:U('Tools/getCodeAly')}", {mobile:$('#username').val()}, function(d){

							if(d.code==1){

								alert(d.msg);

								time($('#getcode'));

							}else{

								alert(d.msg);

							}

						});		

					});

								

					var wait=120;

					function time(o) {

						if (wait == 0) {

								o.attr("disabled",false);

								o.val("获取验证码");

								wait = 120;

						} else {

								o.attr("disabled", true);

								o.val(wait+"秒");

								wait--;

								setTimeout(function() {

										time(o)

								},

								1000)

						}

					}



						$('#fm1').submit(function(e){

							e.preventDefault();

							if(!/^1\d{10}$/.test($('#username').val())){

								alert('请输入正确的手机号码');

								$('#username').focus();

								return false;

							}





							if(/^[ ]*$/.test($('#password').val())){

								alert('请输入新密码');

								$('#password').focus();

								return false;

							}



							$.post('{:U("runForget")}', $('#fm1').serialize(), function(d){

								alert(d.msg);

								if(d.code== 1){

									location.href=d.url;

								}

							},'json');

						});

					</script>



				</div>

			</div>

		</div>

	</div>





<div class="min_w">

<include file="Public:footer" />

</div>





</body>





</html>