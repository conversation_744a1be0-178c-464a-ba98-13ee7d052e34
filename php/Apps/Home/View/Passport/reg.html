<!DOCTYPE html>
<html>
<head>

	<title>青岛三匹马实业</title>
	<meta name="keywords" content="青岛三匹马实业" />
	<meta name="description" content="青岛三匹马实业" />
	<include file="Public:header" />

</head>

<body>
	<include file="Public:top" />
	

		<div class="m_auto border1 reg_bg" >
			<div class="pad_20"></div>
			<div class="reg_box">
				<form id="fm1">
					<div class="border1 pad_10 mt_20">
						<img src="__PUBLIC__/images/mobile.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="username" id="username" placeholder="手机号" />
					</div>
					<div class="border1 pad_10 mt_20 pos_rela">
						<img src="__PUBLIC__/images/mobile.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="mobile_code" placeholder="手机验证码" style="width:100px;" />
						<input type="button" value="获取验证码" class="bg_b clr_f border0 pad_10"  id="getcode" style="position: absolute;top:10px;right:10px;" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="__PUBLIC__/images/user.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="nickname" id="nickname" placeholder="姓名" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="__PUBLIC__/images/gsmc.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="company" id="company" placeholder="公司名称" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="__PUBLIC__/images/password.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" name="password" id="password" type="password" placeholder="密码" placeholder="密码" />
					</div>
					<div class="border1 pad_10 mt_20">
						<img src="__PUBLIC__/images/password.png" class="ver_mid" />
						<input class="f_14 clr_9 border0 ver_mid ml_10" style="background: transparent;" 
							name="password2" id="password2" type="password" placeholder="重复密码" placeholder="密码" />
					</div>
	<!-- 				<div class="mt_20">
						<input placeholder="验证码" name="captcha" id="captcha" class="f_16 clr_9  ver_mid border1 pad_10" style="width:120px;" />
						<img src="{:U('Tools/verify')}" class="ver_mid" id="verify" />
					</div> -->
					<div class="  mt_10">
						<label>
							<input type="checkbox" class="ver_mid" id="agreement" /> 
							<span class="ver_mid f_14 clr_9 ver_mid" >已阅读并同意 <a href="{:U('agreement')}" class="clr_b" >《用户服务协议》</a></span>
						</label>
					</div>


					<div class=" padt_30">
							<input type="submit" value="注册" class="f_16 clr_f bg_b w_100p border0 padt_10" />
							<div class="f_14 al_rt mt_10">已有账号，<a href="{:U('login')}" class="clr_b">立即登录</a></div>
						</div>
				</form>

				<script type="text/javascript">

				$(function(){

$('#getcode').click(function(){
	if(!/^1\d{10}$/.test($('#username').val())){
		alert('请输入正确的手机号');	
		return false;
	}
	
	$.getJSON("{:U('Tools/getCodeAly')}", {mobile:$('#username').val()}, function(d){
		if(d.code==1){
			alert(d.msg);
			time($('#getcode'));
		}else{
			alert(d.msg);
		}
	});		
});
			
var wait=120;
function time(o) {
	if (wait == 0) {
			o.attr("disabled",false);
			o.val("获取验证码");
			wait = 120;
	} else {
			o.attr("disabled", true);
			o.val(wait+"秒");
			wait--;
			setTimeout(function() {
					time(o)
			},
			1000)
	}
}


					$('#verify').click(function(){
						$(this).attr('src', "{:U('Tools/verify')}");
					});

					$('#fm1').submit(function(e){

						e.preventDefault();
						if(!/^1\d{10}$/.test($('#username').val())){
							alert('请输入正确的手机号码');
							$('#username').focus();
							return false;
						}


						if(/^[ ]*$/.test($('#nickname').val())){
							alert('请输入姓名');
							$('#nickname').focus();
							return false;
						}

						if(/^[ ]*$/.test($('#company').val())){
							alert('请输入公司名称');
							$('#company').focus();
							return false;
						}

						if(/^[ ]*$/.test($('#password').val())){
							alert('请输入密码');
							$('#password').focus();
							return false;
						}


						if($('#password2').val() != $('#password').val()){
							alert('密码输入不一致');
							$('#password2').focus();
							return false;
						}


						if(/^[ ]*$/.test($('#captcha').val())){
							alert('请输入验证码');
							$('#captcha').focus();
							return false;
						}

						if(!$('#agreement').prop('checked')){
							alert('请阅读并同意《用户服务协议》');
							return false;
						}


						$.post('{:U("runReg")}', $('#fm1').serialize(), function(d){
							if(d.code== 1){
								alert(d.msg);
								location.href=d.url;
							}else{
								alert(d.msg);
								$('#verify').click();
							}
						},'json');
					});

				});

				
				</script>

			</div>

			<div class="pad_20"></div>
		</div>



<div class="min_w">
<include file="Public:footer" />
</div>



</body>


</html>