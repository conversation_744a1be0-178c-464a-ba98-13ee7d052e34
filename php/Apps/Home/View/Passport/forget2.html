<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<include file="Public:header" />
<title>找回密码</title>

<script>
$(function(){	

	

	})
</script>

</head>

<body>
<include file="Public:top" />

<div class="img25">
  <form id="fm1" method="post">
     <div class="zcbox">
      <h2>找回密码</h2>
       <input type="text" name="email" id="email" value="" placeholder="输入您的邮箱" class="zc_input235 fl"/>
      <input type="button" name="" value="发送验证码" id="send_email" class="zc_btn fr" style="width:110px;" />
      <div class="cb"></div>
      <input type="password" name="password"  id="password" value="" placeholder="输入新密码" class="zc_input100 mv10"/>
      <input type="password" name="password2" id="password2" value="" placeholder="确认认密码" class="zc_input100 mb10"/>
      <input type="text" name="email_code" id="email_code" value="" placeholder="输入邮箱中的验证码" class="zc_input100 mb10"/>
      <input type="text" name="captcha" id="captcha" value="" placeholder="验证码1" class="zc_input235 fl"/>
      <img src="{:U('Tools/verify')}" id="verify" style="width:120px;height:40px;" alt="" class="fr"/>
      <div class="cb"></div>
      <input type="button" id="save_click" name="" value="确认修改" class="zc_btn"/>
      <p class="tc col444 pt10 f14 lh25">已有账号，<a href="{:U('Passport/login')}" class="col444">立即登录</a></p>
      
     </div><!--zcbox end-->
  </form>
</div><!--img25 end-->
 
 <script>

 $('#save_click').click(function(){
    if($('#password').val() == ''){
      alert('请输入新密码');
      return false;
    }

    if($('#password2').val() != $('#password').val()){
      alert('两次输入密码不一致');
      return false;
    }

    if($('#email_code').val() == ''){
      alert('请输入邮箱验证码');
      return false;
    }

     if($('#captcha').val() == ''){
      alert('请输入验证码');
      return false;
    }


    $.post('{:U("runForget")}', $("#fm1").serialize(), function(d){
        alert(d.msg);
        if(d.code==1){
          history.go(0);
        }else{
          $('#verify').click();
        }
    },'json');
 });

 $('#send_email').click(function(){
  console.log(1);
  if(!/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test($('#email').val())){
    alert('请输入正确的邮箱');
    return false;
  }
      $.post('{:U("Tools/checkEmail")}',{email:$('#email').val()}, function(d){
          alert(d.msg);
          if(d.code==1){
             time($('#send_email'));
          }else{
            $('#verify').click();
          }
         
      },'json')
 });

  $('#verify').click(function(){
    $(this).attr('src', "{:U('Tools/verify')}?"+Math.random());
  });

var wait=120;
function time(o) {
        if (wait == 0) {
            o.attr("disabled",false);
            o.val("获取验证码");
            wait = 120;
        } else {
            o.attr("disabled", true);
            o.val(wait+"秒");
            wait--;
            setTimeout(function() {
                time(o)
            },
            1000)
        }
    }

 </script> 
 

  <div class="cb pv5"></div>


<include file="Public:footer" />

<include file="Public:float" />

</body>
</html>
