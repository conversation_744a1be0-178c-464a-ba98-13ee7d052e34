<div class="bg_f min_w">
	<div class="m_auto padt_15 clearfix ">
		<div class="fl "><a href="{:U('Index/index')}"><img src="__PUBLIC__/images/logo.png" class="dis_b" style="height:78px;" /></a></div>
		<div class="fl ml_50 pos_rela"  >
			<div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box">
				<textarea style="width:400px;height: 150px;" id="pl_text" placeholder="每行输入一个批次/捆号" class="border1 dis_b pad_10"></textarea>
				<input type="button" id="pl_sou" value="搜索" class="dis_b bg_ora" style="color: #fff;padding:5px 10px;border:0" />
			</div>
			<form action="{:U('Chaoshi/index')}" id="ss_fm"  style="margin-top:9px;">
					<input type="search" name="sk" id="sousuo_sk" class="clr_9" value="{$_GET['sk']}" autocomplete="off" 
						placeholder="批号/轧花厂/仓库/产棉地区" style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;">
					<button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
						<img src="__PUBLIC__/images/searth.png" style="width:20px;">
					</button>
					<span class="f_14 clr_r pointer" id="pl_click" style="position: absolute;top:30px;right:80px;">批量</span>
		
			</form>
		</div>

		<script>
		$('#pl_click').click(function(){
			$('#pl_box').toggle();
		});

			$('#pl_sou').click(function(){
				var ss = $('#pl_text').val().trim();
				var arr = ss.split(/\n/g);
				var xx = [];
				for(var i = 0; i<arr.length; i++){
					if(arr[i].trim()!=''){
						xx.push(arr[i].trim());
					}
				}
				// console.log(xx.toString());
				$('#sousuo_sk').val(xx.toString());
				$('#ss_fm').submit();
			});
		</script>


		<div class="fr al_rt f_14 login ">
			<if condition="$session_user.id gt 0">
				<div class="mt_10">
					<a href="{:U('Center/info')}" class="clr_b f_16 log_a">会员中心</a>
					<a href="{:U('Passport/exits')}" class="clr_b f_16">退出</a>
				</div>
			<else />
				<div class="mt_10">
					<a href="{:U('Passport/reg')}" class="clr_d f_16">注册</a>
					<a href="{:U('Passport/login')}" class="clr_d f_16 log_a">登录</a>
				</div>
			</if>
			
		</div>
	</div>
</div>

