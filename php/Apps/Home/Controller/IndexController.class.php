<?php

namespace Home\Controller;

use Think\Controller;

class IndexController extends CommController {

	function _initialize(){

		if(empty(session('uid'))){
			$this->redirect('Passport/login');
			die;
		}

		parent::_initialize();
	}

	function index(){
		$this->bann = M('Adv')->where(['type'=>1])->order('id asc')->select();
		$this->our = M('Art')->where(['cat_id'=>707])->find();
		$this->contact = M('Art')->where(['cat_id'=>701])->find();

		$this->chaoshi = M('Shouye')->order('id asc')->select();

		$dingzhi = M('Dingzhi')->where(['uid'=>session('uid')])->order('id desc')->limit(3)->select();

		foreach($dingzhi as $k=>$v){
			$dingzhi[$k]['leixing'] = str_replace('|', ' ', $v['leixing']);
			$dingzhi[$k]['pihao_kunkao'] = str_replace('|', ' ', $v['pihao_kunkao']);
			$dingzhi[$k]['yanseji_pinji'] = str_replace('|', ' ', $v['yanseji_pinji']);
			$dingzhi[$k]['url'] = U('Chaoshi/index').'?'.$v['url'];
			// print_r($v['url']);
		}
		$this->dingzhi = $dingzhi;

		$this->display();
	}


		function delDingzhi(){
			if(I("get.id")){
				M('Dingzhi')->delete(I('get.id'));
				redirect($_SERVER['HTTP_REFERER']);
			}
		}





}