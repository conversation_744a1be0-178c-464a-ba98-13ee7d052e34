<?php
namespace Home\Controller;
use Think\Controller;

class ChaoshiController extends CommController {



	function _initialize(){
		parent::_initialize();
		//toexcel，lists共用
		//大导航，新疆棉 地产棉 国储棉 进口棉
		$this->chandi = $chandi = M('Cat')->where(['pid'=>0])->order('id asc')->select();
		//def表示大导航
		$this->def = $def = I('get.def') == '' ? $chandi[0]['id'] : I('get.def');
		$this->chandi_default = M('Cat')->where(['pid'=>$def])->order('id asc')->select();
		// print_r($this->chandi_default);
		$this->jiaohuodi = M('Jiaohuodi')->where(['pid'=>0])->order('id asc')->select();


		//cd为父产地，查询其子产地
		if(I('get.cd') != '' && I('get.cd') !='|'){
			$cd = substr(I('get.cd') , 1, -1);
			$cd_arr = explode('|', $cd);
			$cd2 = [];
			foreach($cd_arr  as $k=>$v){
				//$v= 332-
				$c = M('Cat')->where(['id'=>substr($v, 0 , -1)])->find();
				$cd2[$c['id']] =M('Cat')->where(['pid'=>$c['id']])->order('id asc')->field('id,name')->select();
			}
			// print_r($cd2);
			$this->cd2 = $cd2;
		}

		//孙产地
		$xx = $cd2;
		foreach($xx as $k=>$v){
			foreach($v as $k2=>$v2){
				$xx[$k][$k2]['children'] = M('Cat')->where(['pid'=>$v2['id']])->order('order_id desc, id asc')->select();
			}			
		}
		// print_r($xx);
		$this->xx = $xx;


		//jhd为父交货地，查询其子产地
		if(I('get.jhd') != '' && I('get.jhd') !='|'){
			$jhd = substr(I('get.jhd') , 1, -1);
			$jhd_arr = explode('|', $jhd);
			$jhd2 = [];
			foreach($jhd_arr  as $k=>$v){
				//$v= 332-
				$c = M('Jiaohuodi')->where(['id'=>substr($v, 0 , -1)])->find();
				$jhd2[$c['id']] =M('Jiaohuodi')->where(['pid'=>$c['id']])->order('id asc')->field('id,name')->select();
			}
			// print_r($jhd2);
			$this->jhd2 = $jhd2;
		}
	}

	function addFavor(){
		if(empty(session('uid'))){
			die(json_encode(['code'=>0, 'msg'=>'请登录']));
		}

		if(I('get.id')){
			if(M('Favor')->where(['uid'=>session('uid'), 'favor_id'=>I('get.id')])->find()){
				die(json_encode(['code'=>0, 'msg'=>'已收藏过该条记录']));
			}else{
				M('Favor')->add(['uid'=>session('uid'), 'favor_id'=>I('get.id'), 'add_time'=>time()]);
				die(json_encode(['code'=>1, 'msg'=>'收藏成功']));
			}
		}
	}


function rDingzhi(){

		// print_r($_POST);
		// die;
		if(empty(session('uid'))){
			die(json_encode(['code'=>0,'msg'=>'请登录']));
		}

		$d = M('Dingzhi');
		$d->create();
		$d->url=$_POST['url'];
		$d->dingzhi_no = 'D'.date('YmdHis');
		$d->changdu = I('post.changdu_from').'-'.I('post.changdu_to');
		$d->qiangli = I('post.qiangli_from').'-'.I('post.qiangli_to');
		$d->mazhi = I('post.mazhi_from').'-'.I('post.mazhi_to');
		$d->huichaolv = I('post.huichaolv_from').'-'.I('post.huichaolv_to');
		$d->hanzalv = I('post.hanzalv_from').'-'.I('post.hanzalv_to');
		$d->zhengqidu = I('post.zhengqidu_from').'-'.I('post.zhengqidu_to');

		$d->bm123 = I('post.bm123_from').'-'.I('post.bm123_to');
		$d->bm45 = I('post.bm45_from').'-'.I('post.bm45_to');
		$d->ddw123 = I('post.ddw123_from').'-'.I('post.ddw123_to');
		$d->dhr123 = I('post.dhr123_from').'-'.I('post.dhr123_to');
		$d->hr12 = I('post.hr12_from').'-'.I('post.hr12_to');


		$d->uid=session('uid');
		$d->add_time = time();
		$d->add();
		// $this->redirect('dziframe');
		// echo('<script>window.parent.window.location.href = "'.U('dingzhi').'"</script>');
		die(json_encode(['code'=>1, 'msg'=>'操作成功']));
	}



	function test(){

		$indexKey = array('order_sn','name','business','type','total','address','tel','moble','time1','status');       
		//excel表头内容
		$header = array('order_sn'=>'订单编号','name'=>'收货人','business'=>'商户名称','type'=>'支付方式','total'=>'订单金额','address'=>'收货地址','tel'=>'手机','moble'=>'电话','time1'=>'下单时间','status'=>'订单状态');
		array_unshift($list,$header);//将查询到的订单数据和表头内容合并,构造成数组list
		//toExcel($list,'1',$indexKey,1,true);
		$this->toExcel($list,'1',$indexKey,1,true);
	}


	function index(){
		if(I('get.act')=='excel'){
			if(empty($this->session_user['id'])){
				$this->redirect('Passport/login');
				die;
			}
			$this->excel();
		}else{
			$this->lists();
		}
	}


	/** 
	 * 创建(导出)Excel数据表格 
	 * @param  array   $list        要导出的数组格式的数据 
	 * @param  string  $filename    导出的Excel表格数据表的文件名 
	 * @param  array   $indexKey    $list数组中与Excel表格表头$header中每个项目对应的字段的名字(key值) 
	 * @param  array   $startRow    第一条数据在Excel表格中起始行 
	 * @param  [bool]  $excel2007   是否生成Excel2007(.xlsx)以上兼容的数据表 
	 * 比如: $indexKey与$list数组对应关系如下: 
	 *     $indexKey = array('id','username','sex','age'); 
	 *     $list = array(array('id'=>1,'username'=>'YQJ','sex'=>'男','age'=>24)); 
	 */  

	function toExcel($list,$filename,$indexKey,$startRow=1,$excel2007=false){  
			//文件引入  
			require_once './Excel/PHPExcel.php';//包含excel入口
			require_once './Excel/PHPExcel/IOFactory.php';
			require_once './Excel/PHPExcel/Writer/Excel2007.php';

			ob_end_clean();
			if(empty($filename)) $filename = time();  
			if( !is_array($indexKey)) return false;  

			$header_arr = array('A','B','C','D','E','F','G','H','I','J','K','L','M', 'N','O','P','Q','R','S','T','U','V','W','X','Y','Z');  
			//初始化PHPExcel()  
			$objPHPExcel = new \PHPExcel();  

			//设置保存版本格式  
			if($excel2007){  
					$objWriter = new \PHPExcel_Writer_Excel2007($objPHPExcel);  
					$filename = $filename.'.xlsx';  
			}else{  
					$objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  
					$filename = $filename.'.xls';  
			}  

			//接下来就是写数据到表格里面去  
			$objActSheet = $objPHPExcel->getActiveSheet();  
			//设置加粗
			$objActSheet->getStyle("A1:S1")->getFont()->setBold(true);
			//设置自适应宽度
			$objActSheet->getColumnDimension('A')->setAutoSize(true);
			$objActSheet->getColumnDimension('B')->setAutoSize(true);
			$objActSheet->getColumnDimension('C')->setAutoSize(true);
			$objActSheet->getColumnDimension('L')->setAutoSize(true);
			$objActSheet->getColumnDimension('M')->setAutoSize(true);
			//某列颜色
			$objActSheet->getStyle('P')->getFont()->getColor()->setARGB(\PHPExcel_Style_Color::COLOR_RED);
			//设置某列文本
			$objActSheet->getStyle('A')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
			//加边框
			$styleThinBlackBorderOutline = array(
				'borders' => array(
					'allborders' => array( //设置全部边框
							'style' => \PHPExcel_Style_Border::BORDER_THIN //粗的是thick
					),

				),
			);
			$objActSheet->getStyle( 'A1:S'.count($list))->applyFromArray($styleThinBlackBorderOutline);
			//设置背景
			// $objActSheet->getStyle('N')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID);
					// $objActSheet->getStyle('N')->getFill()->getStartColor()->setARGB('FF808080');
			set_time_limit(0);
			ini_set("memory_limit",-1);
			$startRow = 1;
			foreach ($list as $row) {  
					foreach ($indexKey as $key => $value){  
							//这里是设置单元格的内容  
							$objActSheet->setCellValue($header_arr[$key].$startRow,$row[$value]);
							
					}
					$startRow++;

			} 
			// 下载这个表格，在浏览器输出  
			header("Pragma: public");  
			header("Expires: 0");  
			header("Cache-Control:must-revalidate, post-check=0, pre-check=0");  
			header("Content-Type:application/force-download");  
			header("Content-Type:application/vnd.ms-execl");  
			header("Content-Type:application/octet-stream");  
			header("Content-Type:application/download");
			header('Content-Disposition:attachment;filename='.$filename.'');  
			header("Content-Transfer-Encoding:binary");  
			// $objWriter->save('php://output'); 
			$objWriter->save(iconv('utf-8', 'gb2312',$filename));
			echo('<a href="'.C('host_url').$filename.'" target="_blank">'.C('host_url').$filename.'</a><br />');
			ob_flush();
			flush();
	}

	function excel(){
		echo('正在导出数据……<br />');
		ob_flush();
		flush();
		$where = '`id` > 0 ';

//搜索地区
$cd = substr(I('get.cd'), 1, -1);
$cd2 = substr(I('get.cd2'), 1, -1);
$cd3 = substr(I('get.cd3'), 1, -1);

$cda = explode('|', $cd);
$cda2 = explode('|', $cd2);
$cda3 = explode('|', $cd3);
// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含于第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($cda as $k=>$v){
	foreach($cda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($cda[$k]);
			break;
		}
	}
}

foreach($cda2 as $k2=>$v2){
	foreach($cda3 as $k3=>$v3){
		if(strrpos($v3, $v2) !== false){
			// array_splice($cda2, $k2, 1);
			unset($cda2[$k2]);
			break;
		}
	}
}

// print_r('------------');
// print_r($cda);
// print_r($cda2);
// print_r($cda3);

$lasta = array_merge($cda, $cda2,$cda3);
// print_r($lasta);
		
		if($lasta){
			$sql = '';
			foreach($lasta as $k=>$v){
				if($v){
					$sql .= '`path`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}
		

//交货地开始
$jhd = substr(I('get.jhd'), 1, -1);
$jhd2 = substr(I('get.jhd2'), 1, -1);


$jhda = explode('|', $jhd);
$jhda2 = explode('|', $jhd2);

// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($jhda as $k=>$v){
  foreach($jhda2 as $k2=>$v2){
    if(strrpos($v2, $v) !== false){
      // array_splice($cda, $k, 1);
      unset($jhda[$k]);
      break;
    }
  }
}


$lasta2 = array_merge($jhda, $jhda2);
// print_r($lasta2);
    
    if($lasta2){
      $sql = '';
      foreach($lasta2 as $k=>$v){
        if($v){
          $sql .= '`path_cangku`  like "%'.$v.'%"  or ';
        }
      }

      if($sql){
        //去掉最后一个" or"
        $sql = substr($sql, 0, -3);
        $sql =  ' and ('.$sql.')';
      }

      $where .= $sql;
      // print_r($where);
    }


		if(I('get.leixing') !='|'&& I('get.leixing') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.leixing'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`leixing`  like "%'.$v.'%"  or ';
				}
			}

			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			//`id` > 0 and (`leixing` like "%手摘棉%" or `leixing` like "%机采棉%" )
			// print_r($where);
		}

		if(I('get.pihao_kunkao') !='|' && I('get.pihao_kunkao') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.pihao_kunkao'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`pihao_kunkao`  like "%'.$v.'%"  or ';
				}
			}
			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.changdu_from')){
			$where .= ' and `changdu` >= '.I('get.changdu_from');
		}

		if(I('get.changdu_to')){
			$where .= ' and `changdu` <= '.I('get.changdu_to');
		}

		
		if(I('get.mazhi_from')){
			$where .= ' and `mazhi` >= '.I('get.mazhi_from');
		}

		if(I('get.mazhi_to')){
			$where .= ' and `mazhi` <= '.I('get.mazhi_to');
		}

		if(I('get.qiangli_from')){
			$where .= ' and `qiangli` >= '.I('get.qiangli_from');
		}

		if(I('get.qiangli_to')){
			$where .= ' and `qiangli` <= '.I('get.qiangli_to');
		}

		if(I('get.huichaolv_from')){
			$where .= ' and `huichaolv` >= '.I('get.huichaolv_from');
		}

		if(I('get.huichaolv_to')){
			$where .= ' and `huichaolv` <= '.I('get.huichaolv_to');
		}

		if(I('get.zhengqidu_from')){
			$where .= ' and `zhengqidu` >= '.I('get.zhengqidu_from');
		}

		if(I('get.zhengqidu_to')){
			$where .= ' and `zhengqidu` <= '.I('get.zhengqidu_to');
		}

		if(I('get.hanzalv_from')){
			$where .= ' and `hanzalv` >= '.I('get.hanzalv_from');
		}

		if(I('get.hanzalv_to')){
			$where .= ' and `hanzalv` <= '.I('get.hanzalv_to');
		}

		if(I('get.bm123_from')){
			$where .= ' and `bm123` >= '.I('get.bm123_from');
		}

		if(I('get.bm123_to')){
			$where .= ' and `bm123` <= '.I('get.bm123_to');
		}


		if(I('get.bm45_from')){
			$where .= ' and `bm45` >= '.I('get.bm45_from');
		}

		if(I('get.bm45_to')){
			$where .= ' and `bm45` <= '.I('get.bm45_to');
		}

		if(I('get.ddw123_from')){
			$where .= ' and `ddw123` >= '.I('get.ddw123_from');
		}

		if(I('get.ddw123_to')){
			$where .= ' and `ddw123` <= '.I('get.ddw123_to');
		}

		if(I('get.dhr123_from')){
			$where .= ' and `dhr123` >= '.I('get.dhr123_from');
		}

		if(I('get.dhr123_to')){
			$where .= ' and `dhr123` <= '.I('get.dhr123_to');
		}


		if(I('get.hr12_from')){
			$where .= ' and `hr12` >= '.I('get.hr12_from');
		}

		if(I('get.hr12_to')){
			$where .= ' and `hr12` <= '.I('get.hr12_to');
		}

		if(I('get.jiaohuodi')){
			$where .= ' and `jiaohuodi` = "'.I('get.jiaohuodi').'"';
		}


		if(I('get.hot') == 1){
			$where .= ' and `is_hot` = 1';
		}


		if(I('get.jiagongchang')){
			$where .= ' and `jiagongchang` like "%'.I('get.jiagongchang').'%"';
		}


		if(I('get.cangchumingcheng')){
			$where .= ' and `cangchumingcheng` like "%'.I('get.cangchumingcheng').'%"';
		}

		if(I('get.dan')){
			$where .= ' and `dan_type` = "'.I('get.dan').'"';
		}


		

		if(I('get.zuiyou')){
			$order = '`jicha` asc';
		}else if(I('get.xiangtong')){
			$order = '`'.I('get.xiangtong').'` asc';
		}else if(I('get.jicha')){
			$order = '`jicha` asc';
		}else{
			$order = ' id asc ';
		}

		// print_r($where);
		// die;
		
		$count =  M('Chaoshi')->where($where)->count();

		if(!empty(I('get.zuiyou')) && I('get.zuiyou') <= $count){
			$count = I('get.zuiyou');
		}

		$this->count = $count;

		//普通会员默认只导出100条，否则无限制
		$export_count = $this->session_user['member_type'] == 1 ? 50000 : 100;
		$page = new \Think\Page($count,$export_count);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Chaoshi')->where($where)->order($order)->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			if($v['path']){
				$path = explode('-', substr($v['path'], 0 , -1));
				$lists[$k]['diqu_text'] = M('Cat')->where(['id'=>$path[0]])->getField('name').' '	
				.M('Cat')->where(['id'=>$path[1]])->getField('name').' '
				.M('Cat')->where(['id'=>$path[2]])->getField('name');
			}

			$level2_id= M('Gongchang')->where(['title'=>$v['jiagongchang']])->getField('level2');
			$lists[$k]['chandi'] = M('Cat')->where(['id'=>$level2_id])->getField('name');
			
		}
		$this->lists = $lists;

		$indexKey = array(
			'xvhao',
			'pihao_kunkao',
			'leixing',
			'yanseji_pinji',
			'mazhi',
			'changdu',
			'qiangli',
			'hanzalv',
			'huichaolv',
			'gongzhong',
			'maozhaong',
			'zhengqidu',
			'chandi',
			'jiagongchang',
			'cangchumingcheng',
			'jicha',
			'beizhu',
			'dianjiaheyue',
			'baoshu',
		);       
		//excel表头内容
		$header = array(
			'xvhao'=>'序号',
			'pihao_kunkao'=>'批号/捆号',
			'leixing'=>'加工类型',
			'yanseji_pinji'=>'颜色级/品级',
			'mazhi'=>'平均马值',
			'changdu'=>'长度',
			'qiangli'=>'强力',
			'hanzalv'=>'含杂率',
			'huichaolv'=>'回潮率',
			'gongzhong'=>'公重(吨)',
			'maozhaong'=>'毛重(吨)',
			'zhengqidu'=>'长度整齐度',
			'chandi'=>'产地',
			'jiagongchang'=>'加工厂',
			'cangchumingcheng'=>'仓库',
			'jicha'=>'基差',
			'beizhu'=>'备注',
			'dianjiaheyue'=>'点价合约',
			'baoshu'=>'包数',
		);

		// print_r($lists);
		// die;
		
		$size = 1000;
		$arr = array_chunk($lists, $size);
		echo('请耐心等待（共'.count($arr).'个文件）……<br />');
		ob_flush();
		flush();
		foreach($arr as $k=>$v){
			array_unshift($arr[$k],$header);//将查询到的订单数据和表头内容合并,构造成数组list
			$this->toExcel($arr[$k],'导出_'.date('Y-m-d').'_'.($k+1),$indexKey,1,true);
		}

		echo('导出完毕！');
		ob_flush();
		flush();
		
	}

	function lists(){
		$where = '`id` > 0 ';

//产地开始
$cd = substr(I('get.cd'), 1, -1);
$cd2 = substr(I('get.cd2'), 1, -1);
$cd3 = substr(I('get.cd3'), 1, -1);

$cda = explode('|', $cd);
$cda2 = explode('|', $cd2);
$cda3 = explode('|', $cd3);
// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($cda as $k=>$v){
	foreach($cda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($cda[$k]);
			break;
		}
	}
}

//看三级中是否包含二级
foreach($cda2 as $k2=>$v2){
	foreach($cda3 as $k3=>$v3){
		if(strrpos($v3, $v2) !== false){
			// array_splice($cda2, $k2, 1);
			unset($cda2[$k2]);
			break;
		}
	}
}

// print_r('------------');
// print_r($cda);
// print_r($cda2);
// print_r($cda3);

$lasta = array_merge($cda, $cda2,$cda3);
// print_r($lasta);
		
		if($lasta){
			$sql = '';
			foreach($lasta as $k=>$v){
				if($v){
					$sql .= '`path`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}
		

//交货地开始
$jhd = substr(I('get.jhd'), 1, -1);
$jhd2 = substr(I('get.jhd2'), 1, -1);


$jhda = explode('|', $jhd);
$jhda2 = explode('|', $jhd2);

// Array
// (
//     [0] => 331-
//     [1] => 332-
// )
// Array
// (
//     [0] => 331-347-
//     [1] => 331-348-
//     [2] => 331-349-
//     [3] => 331-350-
// )
// Array
// (
//     [0] => 331-347-371-
//     [1] => 331-347-372-
//     [2] => 331-348-376-
// )

// print_r($cda);
// print_r($cda2);
// print_r($cda3);


//从第一级判断，看第二级是否包含第一级，有则从第一级中去掉，即有下级的话，就搜索具体的下级
foreach($jhda as $k=>$v){
	foreach($jhda2 as $k2=>$v2){
		if(strrpos($v2, $v) !== false){
			// array_splice($cda, $k, 1);
			unset($jhda[$k]);
			break;
		}
	}
}


$lasta2 = array_merge($jhda, $jhda2);
// print_r($lasta2);
		
		if($lasta2){
			$sql = '';
			foreach($lasta2 as $k=>$v){
				if($v){
					$sql .= '`path_cangku`  like "%'.$v.'%"  or ';
				}
			}

			if($sql){
				//去掉最后一个" or"
				$sql = substr($sql, 0, -3);
				$sql =  ' and ('.$sql.')';
			}

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.leixing') !='|'&& I('get.leixing') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.leixing'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`leixing`  like "%'.$v.'%"  or ';
				}
			}

			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			//`id` > 0 and (`leixing` like "%手摘棉%" or `leixing` like "%机采棉%" )
			// print_r($where);
		}

		if(I('get.pihao_kunkao') !='|' && I('get.pihao_kunkao') !=''){
			$arr = [];
			$arr2=[];
			$arr = explode('|', I('get.pihao_kunkao'));
			$sql = ' and (';
			foreach($arr as $k=>$v){
				if($v){
					$sql .= '`pihao_kunkao`  like "%'.$v.'%"  or ';
				}
			}
			//去掉最后一个" or"
			$sql = substr($sql, 0, -3);
			$sql .=')';

			$where .= $sql;
			// print_r($where);
		}


		if(I('get.sk')){
			$where .=' and (`pihao_kunkao` like "%'.I('get.sk').'%" or `jiagongchang` like "%'.I('get.sk').'%" or `cangchumingcheng` like "%'.I('get.sk').'%" ) ';
		}


		if(I('get.changdu_from')){
			$where .= ' and `changdu` >= '.I('get.changdu_from');
		}

		if(I('get.changdu_to')){
			$where .= ' and `changdu` <= '.I('get.changdu_to');
		}

		
		if(I('get.mazhi_from')){
			$where .= ' and `mazhi` >= '.I('get.mazhi_from');
		}

		if(I('get.mazhi_to')){
			$where .= ' and `mazhi` <= '.I('get.mazhi_to');
		}

		if(I('get.qiangli_from')){
			$where .= ' and `qiangli` >= '.I('get.qiangli_from');
		}

		if(I('get.qiangli_to')){
			$where .= ' and `qiangli` <= '.I('get.qiangli_to');
		}

		if(I('get.huichaolv_from')){
			$where .= ' and `huichaolv` >= '.I('get.huichaolv_from');
		}

		if(I('get.huichaolv_to')){
			$where .= ' and `huichaolv` <= '.I('get.huichaolv_to');
		}

		if(I('get.zhengqidu_from')){
			$where .= ' and `zhengqidu` >= '.I('get.zhengqidu_from');
		}

		if(I('get.zhengqidu_to')){
			$where .= ' and `zhengqidu` <= '.I('get.zhengqidu_to');
		}

		if(I('get.hanzalv_from')){
			$where .= ' and `hanzalv` >= '.I('get.hanzalv_from');
		}

		if(I('get.hanzalv_to')){
			$where .= ' and `hanzalv` <= '.I('get.hanzalv_to');
		}

		if(I('get.bm123_from')){
			$where .= ' and `bm123` >= '.I('get.bm123_from');
		}

		if(I('get.bm123_to')){
			$where .= ' and `bm123` <= '.I('get.bm123_to');
		}


		if(I('get.bm45_from')){
			$where .= ' and `bm45` >= '.I('get.bm45_from');
		}

		if(I('get.bm45_to')){
			$where .= ' and `bm45` <= '.I('get.bm45_to');
		}

		if(I('get.ddw123_from')){
			$where .= ' and `ddw123` >= '.I('get.ddw123_from');
		}

		if(I('get.ddw123_to')){
			$where .= ' and `ddw123` <= '.I('get.ddw123_to');
		}

		if(I('get.dhr123_from')){
			$where .= ' and `dhr123` >= '.I('get.dhr123_from');
		}

		if(I('get.dhr123_to')){
			$where .= ' and `dhr123` <= '.I('get.dhr123_to');
		}


		if(I('get.hr12_from')){
			$where .= ' and `hr12` >= '.I('get.hr12_from');
		}

		if(I('get.hr12_to')){
			$where .= ' and `hr12` <= '.I('get.hr12_to');
		}

		if(I('get.jiaohuodi')){
			$where .= ' and `jiaohuodi` = "'.I('get.jiaohuodi').'"';
		}


		if(I('get.hot') == 1){
			$where .= ' and `is_hot` = 1';
		}


		if(I('get.jiagongchang')){
			$where .= ' and `jiagongchang` like "%'.I('get.jiagongchang').'%"';
		}


		if(I('get.cangchumingcheng')){
			$where .= ' and `cangchumingcheng` like "%'.I('get.cangchumingcheng').'%"';
		}

		if(I('get.dan')){
			$where .= ' and `dan_type` = "'.I('get.dan').'"';
		}


		

		if(I('get.zuiyou')){
			$order = '`jicha` asc';
		}else if(I('get.xiangtong')){
			$order = '`'.I('get.xiangtong').'` asc';
		}else if(I('get.jicha')){
			$order = '`jicha` asc';
		}else{
			$order = ' id asc ';
		}

		// print_r($where);
		// die;
		
		$count =  M('Chaoshi')->where($where)->count();

		if(!empty(I('get.zuiyou')) && I('get.zuiyou') <= $count){
			$count = I('get.zuiyou');
		}

		$this->count = $count;

		// echo $this->count;
		$page = new \Think\Page($count,10);
		$page->setConfig('theme', "%UP_PAGE% %LINK_PAGE% %DOWN_PAGE% %END% <label class='f_14 clr_6'>共%TOTAL_ROW%条</label>");
		$this->page = $page->show();
		$lists = M('Chaoshi')->where($where)->order($order)->limit($page->firstRow.','.$page->listRows)->select();
		foreach($lists as $k=>$v){
			if($v['path']){
				$path = explode('-', substr($v['path'], 0 , -1));
				$lists[$k]['diqu_text'] = M('Cat')->where(['id'=>$path[0]])->getField('name').' '	
				.M('Cat')->where(['id'=>$path[1]])->getField('name').' '
				.M('Cat')->where(['id'=>$path[2]])->getField('name');
			}

			if($v['path_cangku']){
				$path = explode('-', substr($v['path_cangku'], 0 , -1));
				$lists[$k]['diqu_text_cangku'] = M('Jiaohuodi')->where(['id'=>$path[0]])->getField('name').' '	
				.M('Jiaohuodi')->where(['id'=>$path[1]])->getField('name').' '
				.M('Jiaohuodi')->where(['id'=>$path[2]])->getField('name');
			}
			
		}
		$this->lists = $lists;
		// print_r(M('Chaoshi')->_sql());
		$this->display();
		
	}

	function view(){
		if(I('get.id')){
			$this->res = $res =  M('Chaoshi')->find(I('get.id'));

			// print_r($res);


			$r = file_get_contents('http://www.oureway.com/search.shtml?keyword='.$res['pihao_kunkao']);
			$p = '/<li class=\"manufacturer_2\">(.*?)<a href=\"(.*?)\"\s+target(.*?)>(.*?)<\/a><\/li>/ism';
			preg_match_all( $p, $r, $bb);
			 // print_r($bb[2][0]);

			$url = 'http://www.oureway.com/'.$bb[2][0];

			$ret = file_get_contents($url);
			$pat = '/<div class=\"quantity_list\" style=\"background:#fff;\">(.*?)\<\/table>/ism';
			 preg_match_all( $pat, $ret, $xx);
			 // print_r($xx);
			 $this->biao = $xx[0][0];
			$this->display();	
		}
	}


}