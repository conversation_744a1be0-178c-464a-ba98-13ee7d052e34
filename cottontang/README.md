# 棉花棠管理系统

棉花棠管理系统是一个基于Spring Boot 3和Vue 3的全栈Web应用，用于管理棉花相关业务的后台管理系统和前台展示平台。该项目是对原有ThinkPHP系统的技术升级，使用现代化的技术栈进行重构。

## 主要功能

### 后台管理系统
- 用户管理：管理员和普通用户的CRUD操作，权限管理
- 仓库管理：棉花仓库信息和库存管理
- 文章管理：企业新闻、公告等文章内容管理
- 广告管理：网站轮播图、广告位管理
- 商城管理：商品分类、商品信息、订单管理
- Excel导入导出：支持批量数据处理
- 文件上传：支持图片等文件的上传和管理

### 前台展示系统
- 首页展示：轮播图、产品分类、热门产品、企业介绍、新闻动态
- 产品展示：分类浏览、详情查看
- 企业介绍：公司简介、联系方式
- 新闻动态：企业新闻、行业资讯

## 技术栈

### 后端
- Java 21
- Spring Boot 3
- MyBatis Plus
- Sa-Token（认证和授权）
- Easy Excel（Excel处理）
- MySQL（数据库）
- RESTful API设计

### 前端
- Vue 3
- TypeScript
- Element Plus（UI组件库）
- Vite（构建工具）
- Vue Router
- Pinia（状态管理）
- Axios（HTTP客户端）

## 项目结构

```
cottontang/
├── java/                  # 后端项目目录
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/cottontang/
│   │   │   │   ├── common/          # 通用类，如Result、PageData等
│   │   │   │   ├── config/          # 配置类
│   │   │   │   ├── controller/      # 控制器
│   │   │   │   ├── entity/          # 实体类
│   │   │   │   ├── exception/       # 异常处理
│   │   │   │   ├── mapper/          # MyBatis映射器接口
│   │   │   │   ├── service/         # 服务层
│   │   │   │   ├── util/            # 工具类
│   │   │   │   └── CottonTangApplication.java  # 启动类
│   │   │   └── resources/           # 配置文件、静态资源等
│   │   └── test/                    # 测试代码
│   └── pom.xml                      # Maven配置
└── vue/                   # 前端项目目录
    ├── src/
    │   ├── api/                     # API请求
    │   ├── assets/                  # 静态资源
    │   ├── components/              # 公共组件
    │   ├── router/                  # 路由配置
    │   ├── store/                   # 状态管理
    │   ├── types/                   # TypeScript类型定义
    │   ├── utils/                   # 工具函数
    │   ├── views/                   # 页面组件
    │   │   ├── article/             # 文章管理
    │   │   ├── advertisement/       # 广告管理
    │   │   ├── error/               # 错误页面
    │   │   ├── home/                # 管理首页
    │   │   ├── layout/              # 管理布局
    │   │   ├── login/               # 登录页面
    │   │   ├── public/              # 前台公共页面
    │   │   ├── store/               # 商城管理
    │   │   ├── user/                # 用户管理
    │   │   └── warehouse/           # 仓库管理
    │   ├── App.vue                  # 根组件
    │   └── main.ts                  # 入口文件
    ├── index.html                   # HTML模板
    ├── tsconfig.json                # TypeScript配置
    ├── vite.config.ts               # Vite配置
    └── package.json                 # 依赖配置
```

## 安装与使用

### 后端

1. 确保已安装Java 21和Maven
2. 进入java目录，运行：
```bash
mvn clean install
```
3. 启动应用：
```bash
mvn spring-boot:run
```
或者直接运行`CottonTangApplication.java`的main方法

### 前端

1. 确保已安装Node.js 18+和npm
2. 进入vue目录，安装依赖：
```bash
npm install
```
3. 开发环境运行：
```bash
npm run dev
```
4. 生产环境构建：
```bash
npm run build
```

## API文档

API文档通过Swagger UI提供，在启动后端服务后可访问：
```
http://localhost:8080/swagger-ui/index.html
```

## 数据库

项目使用MySQL数据库，需要提前创建名为`cottontang`的数据库。数据库表结构和初始数据可通过运行`sql/14b12b1f2a6fd67d707212393fdd6d61.sql`脚本创建。

## 配置文件

配置文件位于`java/src/main/resources/application.yml`，主要配置项包括：

- 服务器端口
- 数据库连接信息
- 文件上传路径
- 日志级别等

## 部署指南

### 后端部署
1. 使用Maven构建项目生成jar包
2. 使用以下命令启动：
```bash
java -jar cottontang.jar --spring.profiles.active=prod
```

### 前端部署
1. 构建前端项目：
```bash
npm run build
```
2. 将dist目录下的文件部署到Web服务器（如Nginx）

## 注意事项

- 确保服务器有足够的权限来访问文件系统（上传文件等功能）
- 在生产环境中，请修改默认密码和密钥
- 建议定期备份数据库

## 许可证

本项目采用MIT许可证，详情请参阅LICENSE文件。 
