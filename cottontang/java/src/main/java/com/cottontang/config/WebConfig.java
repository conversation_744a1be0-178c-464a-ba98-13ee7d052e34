package com.cottontang.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private FileProperties fileProperties;

    /**
     * 注册Sa-Token拦截器，鉴权
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 登录校验 -- 拦截所有路由，并排除登录相关的路由用于开放访问
            SaRouter.match("/**")
                    .notMatch("/auth/runLogin")          // 用户登录
                    .notMatch("/auth/runReg")            // 用户注册
                    .notMatch("/auth/runForget")         // 忘记密码
                    .notMatch("/auth/getCodeAly")        // 获取验证码
                    .notMatch("/test/**")                // 测试接口
                    .notMatch("/public/**")              // 公共资源
                    .notMatch("/static/**")              // 静态资源
                    .notMatch("/images/**")              // 图片资源
                    .notMatch("/css/**")                 // CSS资源
                    .notMatch("/js/**")                  // JS资源
                    .notMatch("/favicon.ico")            // 网站图标
                    .notMatch("/error")                  // 错误页面
                    .notMatch("/actuator/**")            // 监控端点
                    .check(r -> StpUtil.checkLogin());  // 执行登录校验
        })).addPathPatterns("/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置访问上传文件的路径
        registry.addResourceHandler("/files/**")
                .addResourceLocations("file:" + fileProperties.getPath() + "/");
    }

    /**
     * 配置跨域支持
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }
}
