package com.cottontang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "file.upload")
public class FileProperties {
    
    /**
     * 上传路径
     */
    private String path;
    
    /**
     * 允许的文件扩展名
     */
    private String allowedExtensions;
    
    /**
     * 最大文件大小（字节）
     */
    private Long maxSize;
    
    /**
     * 获取允许的扩展名列表
     * 
     * @return 允许的扩展名列表
     */
    public List<String> getAllowedExtensionsList() {
        return Arrays.asList(allowedExtensions.split(","));
    }
} 