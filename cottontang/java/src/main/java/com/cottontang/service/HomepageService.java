package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Homepage;

import java.util.List;

/**
 * 首页数据服务接口
 * 
 * <AUTHOR>
 */
public interface HomepageService extends IService<Homepage> {
    
    /**
     * 获取首页数据列表（每日精选）
     * 
     * @return 首页数据列表
     */
    List<Homepage> getHomepageList();
    
    /**
     * 获取首页数据列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 首页数据分页
     */
    Page<Homepage> getHomepageList(PageQuery pageQuery);
}
