package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.AdminDTO;
import com.cottontang.dto.LoginDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.UserInfoDTO;
import com.cottontang.entity.Admin;

/**
 * 管理员服务接口
 * 
 * <AUTHOR>
 */
public interface AdminService extends IService<Admin> {
    
    /**
     * 管理员登录
     * 
     * @param loginDTO 登录参数
     * @return 令牌字符串
     */
    String login(LoginDTO loginDTO);
    
    /**
     * 根据ID获取管理员信息
     * 
     * @param adminId 管理员ID
     * @return 管理员用户信息
     */
    UserInfoDTO getAdminInfo(Long adminId);
    
    /**
     * 登出当前管理员
     */
    void logout();
    
    /**
     * 获取管理员列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 管理员分页
     */
    Page<Admin> getAdminList(PageQuery pageQuery);
    
    /**
     * 添加管理员用户
     * 
     * @param adminDTO 管理员数据
     * @return 如果成功则为true
     */
    boolean addAdmin(AdminDTO adminDTO);
    
    /**
     * 更新管理员用户
     * 
     * @param adminDTO 管理员数据
     * @return 如果成功则为true
     */
    boolean updateAdmin(AdminDTO adminDTO);
    
    /**
     * 根据ID删除管理员用户
     * 
     * @param id 管理员ID
     * @return 如果成功则为true
     */
    boolean deleteAdmin(Long id);
    
    /**
     * 更改管理员状态
     * 
     * @param id 管理员ID
     * @param status 新状态 (0: 禁用, 1: 启用)
     * @return 如果成功则为true
     */
    boolean changeStatus(Long id, Integer status);
    
    /**
     * 更新管理员排序ID
     * 
     * @param id 管理员ID
     * @param orderId 新排序ID
     * @return 如果成功则为true
     */
    boolean updateOrderId(Long id, Integer orderId);
} 