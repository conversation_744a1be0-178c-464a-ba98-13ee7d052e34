package com.cottontang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.entity.Category;

import java.util.List;

/**
 * Category service interface
 * 
 * <AUTHOR>
 */
public interface CategoryService extends IService<Category> {
    
    /**
     * Get category list by parent ID
     * 
     * @param pid parent ID (0 for top level)
     * @return category list
     */
    List<Category> getCategoriesByParentId(Long pid);
    
    /**
     * Get category tree (hierarchical structure)
     * 
     * @return category tree
     */
    List<Category> getCategoryTree();
    
    /**
     * Add category
     * 
     * @param category category data
     * @return true if successful
     */
    boolean addCategory(Category category);
    
    /**
     * Update category
     * 
     * @param category category data
     * @return true if successful
     */
    boolean updateCategory(Category category);
    
    /**
     * Delete category
     * 
     * @param id category ID
     * @return true if successful
     */
    boolean deleteCategory(Long id);
} 