package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.LoginRecord;

/**
 * 登录记录服务接口
 * 
 * <AUTHOR>
 */
public interface LoginRecordService extends IService<LoginRecord> {
    
    /**
     * 获取登录记录列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 登录记录分页
     */
    Page<LoginRecord> getLoginRecordList(PageQuery pageQuery);
    
    /**
     * 根据用户ID获取登录记录
     * 
     * @param uid 用户ID
     * @param pageQuery 分页参数
     * @return 登录记录分页
     */
    Page<LoginRecord> getLoginRecordsByUserId(Long uid, PageQuery pageQuery);
    
    /**
     * 清理过期的登录记录
     * 
     * @param days 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredRecords(int days);
}
