package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.CustomOrder;

import java.util.List;

/**
 * 定制订单服务接口
 * 
 * <AUTHOR>
 */
public interface CustomOrderService extends IService<CustomOrder> {
    
    /**
     * 获取定制订单列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 订单分页
     */
    Page<CustomOrder> getCustomOrderList(PageQuery pageQuery);
    
    /**
     * 获取用户定制订单列表（分页）
     * 
     * @param userId 用户ID
     * @param pageQuery 分页和搜索参数
     * @return 订单分页
     */
    Page<CustomOrder> getUserCustomOrderList(Long userId, PageQuery pageQuery);
    
    /**
     * 获取用户最近的定制订单
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 订单列表
     */
    List<CustomOrder> getUserRecentCustomOrders(Long userId, Integer limit);
    
    /**
     * 创建定制订单
     * 
     * @param userId 用户ID
     * @param customOrder 订单信息
     * @return 是否成功
     */
    boolean createCustomOrder(Long userId, CustomOrder customOrder);
    
    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateOrderStatus(Long id, Integer status);
}
