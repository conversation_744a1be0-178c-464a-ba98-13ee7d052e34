package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.AdvertisementDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Advertisement;

import java.util.List;

/**
 * 广告服务接口
 * 
 * <AUTHOR>
 */
public interface AdvertisementService extends IService<Advertisement> {
    
    /**
     * 获取广告列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 广告分页
     */
    Page<Advertisement> getAdvertisementList(PageQuery pageQuery);
    
    /**
     * 根据类型获取广告
     * 
     * @param type 广告类型
     * @return 广告列表
     */
    List<Advertisement> getAdvertisementsByType(Integer type);
    
    /**
     * 添加广告
     * 
     * @param advertisementDTO 广告数据
     * @return 如果成功则为true
     */
    boolean addAdvertisement(AdvertisementDTO advertisementDTO);
    
    /**
     * 更新广告
     * 
     * @param advertisementDTO 广告数据
     * @return 如果成功则为true
     */
    boolean updateAdvertisement(AdvertisementDTO advertisementDTO);
    
    /**
     * 删除广告
     * 
     * @param id 广告ID
     * @return 如果成功则为true
     */
    boolean deleteAdvertisement(Long id);
    
    /**
     * 更新广告排序ID
     * 
     * @param id 广告ID
     * @param orderId 新排序ID
     * @return 如果成功则为true
     */
    boolean updateOrderId(Long id, Integer orderId);
} 