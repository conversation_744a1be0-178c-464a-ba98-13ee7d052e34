package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Message;

/**
 * 消息服务接口
 * 
 * <AUTHOR>
 */
public interface MessageService extends IService<Message> {
    
    /**
     * 发送消息
     * 
     * @param message 消息数据
     * @return 如果成功则为true
     */
    boolean sendMessage(Message message);
    
    /**
     * 获取用户消息列表
     * 
     * @param uid 用户ID
     * @param pageQuery 分页参数
     * @return 消息分页
     */
    Page<Message> getUserMessages(Long uid, PageQuery pageQuery);
    
    /**
     * 标记消息为已读
     * 
     * @param messageId 消息ID
     * @param uid 用户ID
     * @return 如果成功则为true
     */
    boolean markAsRead(Long messageId, Long uid);
    
    /**
     * 获取未读消息数量
     * 
     * @param uid 用户ID
     * @return 未读消息数量
     */
    long getUnreadCount(Long uid);
    
    /**
     * 删除消息
     * 
     * @param messageId 消息ID
     * @param uid 用户ID
     * @return 如果成功则为true
     */
    boolean deleteMessage(Long messageId, Long uid);
    
    /**
     * 获取系统消息列表
     * 
     * @param pageQuery 分页参数
     * @return 消息分页
     */
    Page<Message> getSystemMessages(PageQuery pageQuery);
}
