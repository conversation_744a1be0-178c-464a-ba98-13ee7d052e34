package com.cottontang.service;

/**
 * 短信服务接口
 *
 * <AUTHOR>
 */
public interface SmsService {

    /**
     * 发送验证码短信
     *
     * @param mobile 手机号
     * @param code 验证码
     * @return 发送结果
     */
    boolean sendVerificationCode(String mobile, String code);

    /**
     * 生成并发送验证码
     *
     * @param mobile 手机号
     * @return 发送结果
     */
    boolean generateAndSendCode(String mobile);

    /**
     * 验证验证码
     *
     * @param mobile 手机号
     * @param code 验证码
     * @return 验证结果
     */
    boolean validateCode(String mobile, String code);

    /**
     * 清除验证码缓存
     *
     * @param mobile 手机号
     */
    void clearCode(String mobile);
}
