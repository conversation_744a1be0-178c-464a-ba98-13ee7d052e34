package com.cottontang.service;

import com.cottontang.entity.ExcelFile;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Excel服务接口
 * 
 * <AUTHOR>
 */
public interface ExcelService {
    
    /**
     * 导入首页数据Excel
     * 
     * @param file Excel文件
     * @param adminUid 管理员ID
     * @return 导入结果
     */
    ExcelFile importHomepageData(MultipartFile file, Long adminUid);
    
    /**
     * 导出首页数据Excel
     * 
     * @param response HTTP响应
     * @param danType 单据类型（可选）
     */
    void exportHomepageData(HttpServletResponse response, String danType);
    
    /**
     * 导入定制订单Excel
     * 
     * @param file Excel文件
     * @param adminUid 管理员ID
     * @return 导入结果
     */
    ExcelFile importCustomOrderData(MultipartFile file, Long adminUid);
    
    /**
     * 导出定制订单Excel
     * 
     * @param response HTTP响应
     * @param status 订单状态（可选）
     */
    void exportCustomOrderData(HttpServletResponse response, Integer status);
    
    /**
     * 导入用户数据Excel
     * 
     * @param file Excel文件
     * @param adminUid 管理员ID
     * @return 导入结果
     */
    ExcelFile importUserData(MultipartFile file, Long adminUid);
    
    /**
     * 导出用户数据Excel
     * 
     * @param response HTTP响应
     */
    void exportUserData(HttpServletResponse response);
    
    /**
     * 获取Excel文件记录列表
     * 
     * @return Excel文件记录列表
     */
    List<ExcelFile> getExcelFileList();
    
    /**
     * 根据ID获取Excel文件记录
     * 
     * @param id 文件记录ID
     * @return Excel文件记录
     */
    ExcelFile getExcelFileById(Long id);
    
    /**
     * 删除Excel文件记录
     * 
     * @param id 文件记录ID
     * @return 如果成功则为true
     */
    boolean deleteExcelFile(Long id);
}
