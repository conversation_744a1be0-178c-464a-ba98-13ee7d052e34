package com.cottontang.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.entity.Region;

import java.util.List;

/**
 * Region service interface
 * 
 * <AUTHOR>
 */
public interface RegionService extends IService<Region> {
    
    /**
     * Get region list by parent ID
     * 
     * @param pid parent ID (0 for top level)
     * @return region list
     */
    List<Region> getRegionsByParentId(Long pid);
    
    /**
     * Get region tree (hierarchical structure)
     * 
     * @return region tree
     */
    List<Region> getRegionTree();
    
    /**
     * Add region
     * 
     * @param region region data
     * @return true if successful
     */
    boolean addRegion(Region region);
    
    /**
     * Update region
     * 
     * @param region region data
     * @return true if successful
     */
    boolean updateRegion(Region region);
    
    /**
     * Delete region
     * 
     * @param id region ID
     * @return true if successful
     */
    boolean deleteRegion(Long id);
} 