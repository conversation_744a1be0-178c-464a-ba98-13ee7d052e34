package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.ArticleDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Article;

import java.util.List;

/**
 * 文章服务接口
 * 
 * <AUTHOR>
 */
public interface ArticleService extends IService<Article> {
    
    /**
     * 获取文章列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 文章分页
     */
    Page<Article> getArticleList(PageQuery pageQuery);
    
    /**
     * 根据分类ID获取文章
     * 
     * @param catId 分类ID
     * @return 文章列表
     */
    List<Article> getArticlesByCategoryId(Long catId);
    
    /**
     * 获取推荐文章
     * 
     * @param limit 返回的最大文章数量
     * @return 推荐文章列表
     */
    List<Article> getRecommendedArticles(Integer limit);
    
    /**
     * 添加文章
     * 
     * @param articleDTO 文章数据
     * @return 如果成功则为true
     */
    boolean addArticle(ArticleDTO articleDTO);
    
    /**
     * 更新文章
     * 
     * @param articleDTO 文章数据
     * @return 如果成功则为true
     */
    boolean updateArticle(ArticleDTO articleDTO);
    
    /**
     * 删除文章
     * 
     * @param id 文章ID
     * @return 如果成功则为true
     */
    boolean deleteArticle(Long id);
    
    /**
     * 切换文章推荐状态
     * 
     * @param id 文章ID
     * @return 新的推荐状态
     */
    Integer toggleRecommendation(Long id);
    
    /**
     * 更新文章排序ID
     * 
     * @param id 文章ID
     * @param orderId 新排序ID
     * @return 如果成功则为true
     */
    boolean updateOrderId(Long id, Integer orderId);
} 