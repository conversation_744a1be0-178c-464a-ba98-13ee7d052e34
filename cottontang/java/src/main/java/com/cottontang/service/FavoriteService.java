package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Favorite;

/**
 * 收藏服务接口
 * 
 * <AUTHOR>
 */
public interface FavoriteService extends IService<Favorite> {
    
    /**
     * 添加收藏
     * 
     * @param uid 用户ID
     * @param favorId 收藏对象ID
     * @param favorType 收藏类型
     * @return 如果成功则为true
     */
    boolean addFavorite(Long uid, Long favorId, Integer favorType);
    
    /**
     * 取消收藏
     * 
     * @param uid 用户ID
     * @param favorId 收藏对象ID
     * @return 如果成功则为true
     */
    boolean removeFavorite(Long uid, Long favorId);
    
    /**
     * 检查是否已收藏
     * 
     * @param uid 用户ID
     * @param favorId 收藏对象ID
     * @return 如果已收藏则为true
     */
    boolean isFavorited(Long uid, Long favorId);
    
    /**
     * 获取用户收藏列表
     * 
     * @param uid 用户ID
     * @param favorType 收藏类型（可选）
     * @param pageQuery 分页参数
     * @return 收藏分页
     */
    Page<Favorite> getUserFavorites(Long uid, Integer favorType, PageQuery pageQuery);
    
    /**
     * 获取收藏数量
     * 
     * @param favorId 收藏对象ID
     * @return 收藏数量
     */
    long getFavoriteCount(Long favorId);
}
