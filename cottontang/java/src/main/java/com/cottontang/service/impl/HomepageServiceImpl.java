package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Homepage;
import com.cottontang.mapper.HomepageMapper;
import com.cottontang.service.HomepageService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 首页数据服务实现
 * 
 * <AUTHOR>
 */
@Service
public class HomepageServiceImpl extends ServiceImpl<HomepageMapper, Homepage> implements HomepageService {
    
    @Override
    public List<Homepage> getHomepageList() {
        LambdaQueryWrapper<Homepage> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(Homepage::getId);
        return list(wrapper);
    }
    
    @Override
    public Page<Homepage> getHomepageList(PageQuery pageQuery) {
        Page<Homepage> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());
        
        LambdaQueryWrapper<Homepage> wrapper = new LambdaQueryWrapper<>();
        
        // 关键词搜索
        if (StringUtils.hasText(pageQuery.getKeyword())) {
            wrapper.and(w -> w
                .like(Homepage::getPihaoKunkao, pageQuery.getKeyword())
                .or().like(Homepage::getLeixing, pageQuery.getKeyword())
                .or().like(Homepage::getJiagongchang, pageQuery.getKeyword())
                .or().like(Homepage::getCangchumingcheng, pageQuery.getKeyword())
            );
        }
        
        // 排序
        wrapper.orderByAsc(Homepage::getId);
        
        return page(page, wrapper);
    }
}
