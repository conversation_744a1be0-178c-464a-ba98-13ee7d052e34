package com.cottontang.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.cottontang.config.AliyunSmsConfig;
import com.cottontang.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 短信服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SmsServiceImpl implements SmsService {

    private final Client smsClient;
    private final AliyunSmsConfig smsConfig;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 验证码缓存key前缀
     */
    private static final String SMS_CODE_PREFIX = "sms:code:";

    /**
     * 验证码发送频率限制key前缀
     */
    private static final String SMS_LIMIT_PREFIX = "sms:limit:";

    /**
     * 验证码有效期（分钟）
     */
    private static final int CODE_EXPIRE_MINUTES = 5;

    /**
     * 发送频率限制（秒）
     */
    private static final int SEND_LIMIT_SECONDS = 60;

    @Override
    public boolean sendVerificationCode(String mobile, String code) {
        try {
            // 构建短信发送请求
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(mobile)
                    .setSignName(smsConfig.getSignName())
                    .setTemplateCode(smsConfig.getTemplateCode())
                    .setTemplateParam("{\"code\":\"" + code + "\"}");

            // 发送短信
            SendSmsResponse response = smsClient.sendSms(sendSmsRequest);
            
            log.info("发送短信验证码到手机号: {}, 验证码: {}, 响应: {}", mobile, code, response.getBody().getMessage());
            
            // 检查发送结果
            if ("OK".equals(response.getBody().getCode())) {
                return true;
            } else {
                log.error("短信发送失败: {}", response.getBody().getMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("发送短信验证码异常: ", e);
            return false;
        }
    }

    @Override
    public boolean generateAndSendCode(String mobile) {
        // 检查发送频率限制
        String limitKey = SMS_LIMIT_PREFIX + mobile;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(limitKey))) {
            log.warn("手机号 {} 发送验证码过于频繁", mobile);
            return false;
        }

        // 生成6位随机验证码
        String code = RandomUtil.randomNumbers(6);
        
        // 发送短信
        boolean sendResult = sendVerificationCode(mobile, code);
        
        if (sendResult) {
            // 缓存验证码到Redis
            String codeKey = SMS_CODE_PREFIX + mobile;
            redisTemplate.opsForValue().set(codeKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 设置发送频率限制
            redisTemplate.opsForValue().set(limitKey, "1", SEND_LIMIT_SECONDS, TimeUnit.SECONDS);
            
            log.info("验证码已发送并缓存: 手机号={}, 验证码={}", mobile, code);
            return true;
        }
        
        return false;
    }

    @Override
    public boolean validateCode(String mobile, String code) {
        if (StrUtil.isBlank(mobile) || StrUtil.isBlank(code)) {
            return false;
        }

        String codeKey = SMS_CODE_PREFIX + mobile;
        Object cachedCode = redisTemplate.opsForValue().get(codeKey);
        
        if (cachedCode != null && code.equals(cachedCode.toString())) {
            // 验证成功后删除缓存的验证码
            redisTemplate.delete(codeKey);
            log.info("验证码验证成功: 手机号={}", mobile);
            return true;
        }
        
        log.warn("验证码验证失败: 手机号={}, 输入验证码={}, 缓存验证码={}", mobile, code, cachedCode);
        return false;
    }

    @Override
    public void clearCode(String mobile) {
        String codeKey = SMS_CODE_PREFIX + mobile;
        redisTemplate.delete(codeKey);
        log.info("已清除验证码缓存: 手机号={}", mobile);
    }
}
