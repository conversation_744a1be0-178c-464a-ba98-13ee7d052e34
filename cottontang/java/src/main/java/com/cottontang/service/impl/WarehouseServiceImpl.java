package com.cottontang.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.WarehouseDTO;
import com.cottontang.entity.Region;
import com.cottontang.entity.Warehouse;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.WarehouseMapper;
import com.cottontang.service.RegionService;
import com.cottontang.service.WarehouseService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Warehouse service implementation
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WarehouseServiceImpl extends ServiceImpl<WarehouseMapper, Warehouse> implements WarehouseService {
    
    private final RegionService regionService;
    
    /**
     * Get warehouse list with pagination
     * 
     * @param pageQuery pagination and search parameters
     * @return warehouse page
     */
    @Override
    public Page<Warehouse> getWarehouseList(PageQuery pageQuery) {
        // Create page
        Page<Warehouse> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());
        
        // Create query wrapper
        LambdaQueryWrapper<Warehouse> wrapper = new LambdaQueryWrapper<>();
        
        // Add search condition if keyword is provided
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            wrapper.like(Warehouse::getTitle, pageQuery.getKeyword())
                   .or()
                   .like(Warehouse::getAddress, pageQuery.getKeyword());
        }
        
        // Add sort conditions
        wrapper.orderByDesc(Warehouse::getOrderId, Warehouse::getId);
        
        // Execute query
        Page<Warehouse> result = page(page, wrapper);
        
        // Fill region names for each warehouse
        for (Warehouse warehouse : result.getRecords()) {
            StringBuilder regionName = new StringBuilder();
            
            // Get level 1 region
            if (warehouse.getLevel1() != null) {
                Region level1 = regionService.getById(warehouse.getLevel1());
                if (level1 != null) {
                    regionName.append(level1.getName());
                }
            }
            
            // Get level 2 region
            if (warehouse.getLevel2() != null) {
                Region level2 = regionService.getById(warehouse.getLevel2());
                if (level2 != null) {
                    regionName.append(" ").append(level2.getName());
                }
            }
            
            // Get level 3 region
            if (warehouse.getLevel3() != null) {
                Region level3 = regionService.getById(warehouse.getLevel3());
                if (level3 != null) {
                    regionName.append(" ").append(level3.getName());
                }
            }
            
            warehouse.setRegionName(regionName.toString());
        }
        
        return result;
    }
    
    /**
     * Add warehouse
     * 
     * @param warehouseDTO warehouse data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addWarehouse(WarehouseDTO warehouseDTO) {
        // Validate regions
        validateRegions(warehouseDTO);
        
        // Create new warehouse
        Warehouse warehouse = new Warehouse();
        BeanUtil.copyProperties(warehouseDTO, warehouse);
        
        // Set default values
        if (warehouse.getOrderId() == null) {
            warehouse.setOrderId(100);
        }
        
        if (warehouse.getIsRecom() == null) {
            warehouse.setIsRecom(0);
        }
        
        // Set creation timestamp
        warehouse.setAddTime(System.currentTimeMillis() / 1000);
        
        return save(warehouse);
    }
    
    /**
     * Update warehouse
     * 
     * @param warehouseDTO warehouse data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateWarehouse(WarehouseDTO warehouseDTO) {
        // Check if warehouse exists
        Warehouse warehouse = getById(warehouseDTO.getId());
        if (warehouse == null) {
            throw new BusinessException("Warehouse not found");
        }
        
        // Validate regions
        validateRegions(warehouseDTO);
        
        // Update warehouse properties
        BeanUtil.copyProperties(warehouseDTO, warehouse);
        
        return updateById(warehouse);
    }
    
    /**
     * Validate regions in warehouse DTO
     * 
     * @param warehouseDTO warehouse DTO
     */
    private void validateRegions(WarehouseDTO warehouseDTO) {
        // Validate level 1 region
        if (warehouseDTO.getLevel1() != null) {
            Region level1 = regionService.getById(warehouseDTO.getLevel1());
            if (level1 == null) {
                throw new BusinessException("Level 1 region not found");
            }
        }
        
        // Validate level 2 region
        if (warehouseDTO.getLevel2() != null) {
            Region level2 = regionService.getById(warehouseDTO.getLevel2());
            if (level2 == null) {
                throw new BusinessException("Level 2 region not found");
            }
        }
        
        // Validate level 3 region
        if (warehouseDTO.getLevel3() != null) {
            Region level3 = regionService.getById(warehouseDTO.getLevel3());
            if (level3 == null) {
                throw new BusinessException("Level 3 region not found");
            }
        }
    }
    
    /**
     * Delete warehouse
     * 
     * @param id warehouse ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteWarehouse(Long id) {
        // Check if warehouse exists
        Warehouse warehouse = getById(id);
        if (warehouse == null) {
            throw new BusinessException("Warehouse not found");
        }
        
        // TODO: Check if warehouse is used by other entities
        
        return removeById(id);
    }
    
    /**
     * Toggle warehouse recommendation status
     * 
     * @param id warehouse ID
     * @return new recommendation status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer toggleRecommendation(Long id) {
        // Check if warehouse exists
        Warehouse warehouse = getById(id);
        if (warehouse == null) {
            throw new BusinessException("Warehouse not found");
        }
        
        // Toggle recommendation status
        Integer newStatus = warehouse.getIsRecom() != null && warehouse.getIsRecom() == 1 ? 0 : 1;
        warehouse.setIsRecom(newStatus);
        
        // Update warehouse
        updateById(warehouse);
        
        return newStatus;
    }
    
    /**
     * Update warehouse order ID
     * 
     * @param id warehouse ID
     * @param orderId new order ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderId(Long id, Integer orderId) {
        // Check if warehouse exists
        Warehouse warehouse = getById(id);
        if (warehouse == null) {
            throw new BusinessException("Warehouse not found");
        }
        
        // Update order ID
        warehouse.setOrderId(orderId);
        return updateById(warehouse);
    }
    
    /**
     * Export warehouses to Excel
     * 
     * @return Excel byte array
     */
    @Override
    public byte[] exportToExcel() {
        // Get all warehouses
        List<Warehouse> warehouses = list(new LambdaQueryWrapper<Warehouse>()
                .orderByDesc(Warehouse::getOrderId, Warehouse::getId));
                
        // Create workbook
        try (Workbook workbook = new XSSFWorkbook()) {
            // Create sheet
            Sheet sheet = workbook.createSheet("Warehouses");
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "Title", "Address", "Phone", "Region", "Capacity"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // Create data rows
            int rowNum = 1;
            for (Warehouse warehouse : warehouses) {
                Row row = sheet.createRow(rowNum++);
                
                // ID
                Cell idCell = row.createCell(0);
                idCell.setCellValue(warehouse.getId());
                
                // Title
                Cell titleCell = row.createCell(1);
                titleCell.setCellValue(warehouse.getTitle());
                
                // Address
                Cell addressCell = row.createCell(2);
                addressCell.setCellValue(warehouse.getAddress());
                
                // Phone
                Cell phoneCell = row.createCell(3);
                phoneCell.setCellValue(warehouse.getPhone());
                
                // Region
                Cell regionCell = row.createCell(4);
                StringBuilder region = new StringBuilder();
                
                // Get level 1 region
                if (warehouse.getLevel1() != null) {
                    Region level1 = regionService.getById(warehouse.getLevel1());
                    if (level1 != null) {
                        region.append(level1.getName());
                    }
                }
                
                // Get level 2 region
                if (warehouse.getLevel2() != null) {
                    Region level2 = regionService.getById(warehouse.getLevel2());
                    if (level2 != null) {
                        region.append(" ").append(level2.getName());
                    }
                }
                
                // Get level 3 region
                if (warehouse.getLevel3() != null) {
                    Region level3 = regionService.getById(warehouse.getLevel3());
                    if (level3 != null) {
                        region.append(" ").append(level3.getName());
                    }
                }
                
                regionCell.setCellValue(region.toString());
                
                // Capacity
                Cell capacityCell = row.createCell(5);
                capacityCell.setCellValue(warehouse.getCapacity());
            }
            
            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // Write to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            throw new BusinessException("Failed to export to Excel: " + e.getMessage());
        }
    }
} 