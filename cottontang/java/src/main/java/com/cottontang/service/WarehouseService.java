package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.dto.WarehouseDTO;
import com.cottontang.entity.Warehouse;

/**
 * Warehouse service interface
 * 
 * <AUTHOR>
 */
public interface WarehouseService extends IService<Warehouse> {
    
    /**
     * Get warehouse list with pagination
     * 
     * @param pageQuery pagination and search parameters
     * @return warehouse page
     */
    Page<Warehouse> getWarehouseList(PageQuery pageQuery);
    
    /**
     * Add warehouse
     * 
     * @param warehouseDTO warehouse data
     * @return true if successful
     */
    boolean addWarehouse(WarehouseDTO warehouseDTO);
    
    /**
     * Update warehouse
     * 
     * @param warehouseDTO warehouse data
     * @return true if successful
     */
    boolean updateWarehouse(WarehouseDTO warehouseDTO);
    
    /**
     * Delete warehouse
     * 
     * @param id warehouse ID
     * @return true if successful
     */
    boolean deleteWarehouse(Long id);
    
    /**
     * Toggle warehouse recommendation status
     * 
     * @param id warehouse ID
     * @return new recommendation status
     */
    Integer toggleRecommendation(Long id);
    
    /**
     * Update warehouse order ID
     * 
     * @param id warehouse ID
     * @param orderId new order ID
     * @return true if successful
     */
    boolean updateOrderId(Long id, Integer orderId);
    
    /**
     * Export warehouses to Excel
     * 
     * @return Excel byte array
     */
    byte[] exportToExcel();
} 