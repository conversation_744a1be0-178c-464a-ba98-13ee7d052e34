package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.entity.Region;
import com.cottontang.exception.BusinessException;
import com.cottontang.mapper.RegionMapper;
import com.cottontang.service.RegionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Region service implementation
 * 
 * <AUTHOR>
 */
@Service
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements RegionService {
    
    /**
     * Get region list by parent ID
     * 
     * @param pid parent ID (0 for top level)
     * @return region list
     */
    @Override
    public List<Region> getRegionsByParentId(Long pid) {
        return list(new LambdaQueryWrapper<Region>()
                .eq(Region::getPid, pid)
                .orderByDesc(Region::getOrderId, Region::getId));
    }
    
    /**
     * Get region tree (hierarchical structure)
     * 
     * @return region tree
     */
    @Override
    public List<Region> getRegionTree() {
        // Get all regions
        List<Region> allRegions = list(new LambdaQueryWrapper<Region>()
                .orderByDesc(Region::getOrderId)
                .orderByAsc(Region::getId));
                
        // Group regions by parent ID
        Map<Long, List<Region>> regionsByParentId = allRegions.stream()
                .collect(Collectors.groupingBy(Region::getPid));
                
        // Set children for each parent region
        allRegions.forEach(region -> region.setChildren(regionsByParentId.get(region.getId())));
        
        // Return only top-level regions
        return allRegions.stream()
                .filter(region -> region.getPid() == 0)
                .collect(Collectors.toList());
    }
    
    /**
     * Add region
     * 
     * @param region region data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addRegion(Region region) {
        // Validate parent region exists if not top level
        if (region.getPid() != 0) {
            Region parent = getById(region.getPid());
            if (parent == null) {
                throw new BusinessException("Parent region not found");
            }
        }
        
        // Set default order ID if not provided
        if (region.getOrderId() == null) {
            region.setOrderId(100);
        }
        
        return save(region);
    }
    
    /**
     * Update region
     * 
     * @param region region data
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRegion(Region region) {
        // Check if region exists
        Region existingRegion = getById(region.getId());
        if (existingRegion == null) {
            throw new BusinessException("Region not found");
        }
        
        // Validate parent region exists if not top level
        if (region.getPid() != 0) {
            Region parent = getById(region.getPid());
            if (parent == null) {
                throw new BusinessException("Parent region not found");
            }
            
            // Prevent circular reference (region cannot be its own descendant)
            if (region.getId().equals(region.getPid()) || isDescendant(region.getId(), region.getPid())) {
                throw new BusinessException("Cannot set parent to its own descendant");
            }
        }
        
        return updateById(region);
    }
    
    /**
     * Check if potentialAncestor is a descendant of regionId
     * 
     * @param regionId region ID
     * @param potentialDescendant potential descendant ID
     * @return true if potentialDescendant is a descendant of regionId
     */
    private boolean isDescendant(Long regionId, Long potentialDescendant) {
        List<Region> children = list(new LambdaQueryWrapper<Region>()
                .eq(Region::getPid, regionId));
                
        if (children.isEmpty()) {
            return false;
        }
        
        for (Region child : children) {
            if (child.getId().equals(potentialDescendant) || isDescendant(child.getId(), potentialDescendant)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Delete region
     * 
     * @param id region ID
     * @return true if successful
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRegion(Long id) {
        // Check if region exists
        Region region = getById(id);
        if (region == null) {
            throw new BusinessException("Region not found");
        }
        
        // Check if region has children
        long childCount = count(new LambdaQueryWrapper<Region>()
                .eq(Region::getPid, id));
                
        if (childCount > 0) {
            throw new BusinessException("Cannot delete region with children");
        }
        
        // TODO: Check if region is used in warehouses
        
        return removeById(id);
    }
} 