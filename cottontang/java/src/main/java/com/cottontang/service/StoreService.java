package com.cottontang.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Store;

import java.util.List;

/**
 * Store service interface
 * 
 * <AUTHOR>
 */
public interface StoreService extends IService<Store> {
    
    /**
     * Get store list with pagination and filtering
     * 
     * @param pageQuery pagination and search parameters
     * @return store page
     */
    Page<Store> getStoreList(PageQuery pageQuery);
    
    /**
     * Add store
     * 
     * @param store store data
     * @return true if successful
     */
    boolean addStore(Store store);
    
    /**
     * Update store
     * 
     * @param store store data
     * @return true if successful
     */
    boolean updateStore(Store store);
    
    /**
     * Delete store
     * 
     * @param id store ID
     * @return true if successful
     */
    boolean deleteStore(Long id);
    
    /**
     * Batch delete stores
     * 
     * @param ids store IDs
     * @return true if successful
     */
    boolean batchDeleteStores(List<Long> ids);
    
    /**
     * Update store status
     * 
     * @param id store ID
     * @param status new status
     * @return true if successful
     */
    boolean updateStatus(Long id, String status);
    
    /**
     * Batch update store status
     * 
     * @param ids store IDs
     * @param status new status
     * @return true if successful
     */
    boolean batchUpdateStatus(List<Long> ids, String status);
} 