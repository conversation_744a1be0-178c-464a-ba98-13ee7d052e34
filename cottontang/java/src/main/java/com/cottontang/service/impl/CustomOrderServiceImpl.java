package com.cottontang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.CustomOrder;
import com.cottontang.mapper.CustomOrderMapper;
import com.cottontang.service.CustomOrderService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 定制订单服务实现
 *
 * <AUTHOR>
 */
@Service
public class CustomOrderServiceImpl extends ServiceImpl<CustomOrderMapper, CustomOrder> implements CustomOrderService {

    @Override
    public Page<CustomOrder> getCustomOrderList(PageQuery pageQuery) {
        Page<CustomOrder> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());

        LambdaQueryWrapper<CustomOrder> wrapper = new LambdaQueryWrapper<>();

        // 关键词搜索
        if (StringUtils.hasText(pageQuery.getKeyword())) {
            wrapper.and(w -> w
                .like(CustomOrder::getDingzhiNo, pageQuery.getKeyword())
                .or().like(CustomOrder::getPihaoKunkao, pageQuery.getKeyword())
                .or().like(CustomOrder::getLeixing, pageQuery.getKeyword())
                .or().like(CustomOrder::getJiagongchang, pageQuery.getKeyword())
            );
        }

        // 排序
        wrapper.orderByDesc(CustomOrder::getId);

        return page(page, wrapper);
    }

    @Override
    public Page<CustomOrder> getUserCustomOrderList(Long userId, PageQuery pageQuery) {
        Page<CustomOrder> page = new Page<>(pageQuery.getCurrent(), pageQuery.getSize());

        LambdaQueryWrapper<CustomOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomOrder::getUid, userId);

        // 关键词搜索
        if (StringUtils.hasText(pageQuery.getKeyword())) {
            wrapper.and(w -> w
                .like(CustomOrder::getDingzhiNo, pageQuery.getKeyword())
                .or().like(CustomOrder::getPihaoKunkao, pageQuery.getKeyword())
                .or().like(CustomOrder::getLeixing, pageQuery.getKeyword())
            );
        }

        // 排序
        wrapper.orderByDesc(CustomOrder::getId);

        return page(page, wrapper);
    }

    @Override
    public List<CustomOrder> getUserRecentCustomOrders(Long userId, Integer limit) {
        LambdaQueryWrapper<CustomOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomOrder::getUid, userId)
               .orderByDesc(CustomOrder::getId)
               .last("LIMIT " + limit);

        List<CustomOrder> orders = list(wrapper);

        // 处理显示格式
        for (CustomOrder order : orders) {
            if (order.getLeixing() != null) {
                order.setLeixing(order.getLeixing().replace("|", " "));
            }
            if (order.getPihaoKunkao() != null) {
                order.setPihaoKunkao(order.getPihaoKunkao().replace("|", " "));
            }
            if (order.getYansejiPinji() != null) {
                order.setYansejiPinji(order.getYansejiPinji().replace("|", " "));
            }
        }

        return orders;
    }

    @Override
    public boolean createCustomOrder(Long userId, CustomOrder customOrder) {
        // 生成订制编号
        String orderNo = "D" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        customOrder.setDingzhiNo(orderNo);
        customOrder.setUid(userId);

        return save(customOrder);
    }

    @Override
    public boolean updateOrderStatus(Long id, Integer status) {
        CustomOrder order = new CustomOrder();
        order.setId(id);

        return updateById(order);
    }
}
