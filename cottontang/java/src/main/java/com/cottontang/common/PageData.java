package com.cottontang.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

/**
 * 分页数据包装类
 * 
 * <AUTHOR>
 */
@Data
public class PageData<T> {
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 每页记录数
     */
    private Long size;
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 默认构造函数
     */
    public PageData() {
    }
    
    /**
     * 通过MyBatis-Plus分页对象构造
     *
     * @param page MyBatis-Plus分页对象
     */
    public PageData(Page<T> page) {
        this.total = page.getTotal();
        this.size = page.getSize();
        this.current = page.getCurrent();
        this.pages = page.getPages();
        this.records = page.getRecords();
    }

    /**
     * 通过MyBatis-Plus分页对象创建PageData实例
     *
     * @param page MyBatis-Plus分页对象
     * @param <T> 数据类型
     * @return PageData实例
     */
    public static <T> PageData<T> from(Page<T> page) {
        return new PageData<>(page);
    }
}