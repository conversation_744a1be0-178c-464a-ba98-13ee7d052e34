package com.cottontang.common;

import lombok.Data;

/**
 * 通用响应结果
 * 
 * <AUTHOR>
 */
@Data
public class Result<T> {
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 数据
     */
    private T data;
    
    /**
     * 成功响应
     * 
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> success() {
        return success(null);
    }
    
    /**
     * 成功响应（带数据）
     * 
     * @param data 数据
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(ResultCode.SUCCESS.getMessage());
        result.setData(data);
        return result;
    }
    
    /**
     * 失败响应
     * 
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> error() {
        return error(ResultCode.ERROR.getMessage());
    }
    
    /**
     * 失败响应（带消息）
     *
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setCode(ResultCode.ERROR.getCode());
        result.setMessage(message);
        return result;
    }

    /**
     * 失败响应（带状态码和消息）
     *
     * @param code 状态码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    /**
     * 失败响应（带消息和数据）
     *
     * @param message 错误消息
     * @param data 数据
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> error(String message, T data) {
        Result<T> result = new Result<>();
        result.setCode(ResultCode.ERROR.getCode());
        result.setMessage(message);
        result.setData(data);
        return result;
    }
    
    /**
     * 自定义状态码和消息的响应
     * 
     * @param code 状态码
     * @param message 消息
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> result(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
    
    /**
     * 自定义状态码、消息和数据的响应
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> result(Integer code, String message, T data) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }
} 