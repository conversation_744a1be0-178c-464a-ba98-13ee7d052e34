package com.cottontang.exception;

import com.cottontang.common.ResultCode;
import lombok.Getter;

/**
 * 业务异常
 * 
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 构造方法
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.code = ResultCode.ERROR.getCode();
    }
    
    /**
     * 构造方法
     * 
     * @param resultCode 结果状态码
     */
    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
    }
    
    /**
     * 构造方法
     * 
     * @param resultCode 结果状态码
     * @param message 错误消息
     */
    public BusinessException(ResultCode resultCode, String message) {
        super(message);
        this.code = resultCode.getCode();
    }
    
    /**
     * 构造方法
     * 
     * @param code 错误码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }
} 