package com.cottontang;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * CottonTang应用程序入口
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@EnableTransactionManagement
public class CottonTangApplication {
    
    /**
     * 主方法，应用程序启动入口
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(CottonTangApplication.class, args);
    }
} 