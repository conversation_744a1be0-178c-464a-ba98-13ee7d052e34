package com.cottontang.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.cottontang.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel工具类，用于导入导出操作
 * 
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtils {
    
    /**
     * 从Excel文件导入数据
     * 
     * @param file Excel文件
     * @param headerMapping 表头到字段的映射
     * @return 数据列表（Map格式）
     */
    public static List<Map<String, Object>> importExcel(MultipartFile file, Map<String, String> headerMapping) {
        try {
            // 创建一个结果列表
            List<Map<String, Object>> resultList = new ArrayList<>();
            
            // 使用EasyExcel读取Excel文件
            EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<String, Object>>() {
                // 表头索引到字段名的映射
                private Map<Integer, String> indexToFieldMap = new HashMap<>();
                
                // 处理表头行
                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    // 将Excel表头映射到字段名
                    for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
                        String headerText = entry.getValue();
                        if (headerMapping.containsKey(headerText)) {
                            indexToFieldMap.put(entry.getKey(), headerMapping.get(headerText));
                        }
                    }
                    
                    // 验证是否找到至少一个有效的表头
                    if (indexToFieldMap.isEmpty()) {
                        throw new BusinessException("Excel文件中没有找到有效的表头");
                    }
                }
                
                // 处理每一行数据
                @Override
                public void invoke(Map<String, Object> data, AnalysisContext context) {
                    Map<String, Object> rowData = new HashMap<>();
                    boolean hasData = false;
                    
                    // 将原始数据转换为映射后的字段名和值
                    for (Map.Entry<Integer, String> entry : indexToFieldMap.entrySet()) {
                        Integer columnIndex = entry.getKey();
                        String fieldName = entry.getValue();
                        Object cellValue = data.get(columnIndex);
                        
                        if (cellValue != null) {
                            rowData.put(fieldName, cellValue);
                            hasData = true;
                        }
                    }
                    
                    if (hasData) {
                        resultList.add(rowData);
                    }
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 解析完成后的操作，这里不需要特殊处理
                }
            }).sheet().doRead();
            
            return resultList;
        } catch (Exception e) {
            log.error("导入Excel文件失败", e);
            throw new BusinessException("导入Excel文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 将数据导出为Excel并返回字节数组
     * 
     * @param dataList 要导出的数据列表
     * @param headerMapping 字段到表头的映射
     * @param sheetName 工作表名称
     * @return Excel文件的字节数组
     */
    public static byte[] exportExcel(List<?> dataList, Map<String, String> headerMapping, String sheetName) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 准备表头数据
            List<List<String>> headList = new ArrayList<>();
            List<String> fieldList = new ArrayList<>();
            
            for (Map.Entry<String, String> entry : headerMapping.entrySet()) {
                fieldList.add(entry.getKey());
                List<String> head = new ArrayList<>();
                head.add(entry.getValue());
                headList.add(head);
            }
            
            // 准备行数据
            List<List<Object>> dataRowList = new ArrayList<>();
            if (dataList != null && !dataList.isEmpty()) {
                for (Object data : dataList) {
                    List<Object> rowData = new ArrayList<>();
                    for (String field : fieldList) {
                        rowData.add(getFieldValue(data, field));
                    }
                    dataRowList.add(rowData);
                }
            }
            
            // 设置表头样式
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            
            // 设置内容样式
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            
            // 创建样式策略
            HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            
            // 导出Excel
            EasyExcel.write(outputStream)
                    .head(headList)
                    .sheet(StringUtils.hasText(sheetName) ? sheetName : "Sheet1")
                    .registerWriteHandler(styleStrategy)
                    .doWrite(dataRowList);
            
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("导出Excel文件失败", e);
            throw new BusinessException("导出Excel文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 从对象中获取字段值
     * 
     * @param obj 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private static Object getFieldValue(Object obj, String fieldName) {
        if (obj == null || fieldName == null) {
            return null;
        }
        
        try {
            if (obj instanceof Map) {
                return ((Map<?, ?>) obj).get(fieldName);
            }
            
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            log.debug("获取字段值失败: " + fieldName, e);
            return null;
        }
    }
} 