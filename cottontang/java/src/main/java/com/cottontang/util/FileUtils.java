package com.cottontang.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/28 14:16
 */
public class FileUtils {

    public static String getUrl(String path, HttpServletRequest request) {
        return ServletUriComponentsBuilder
                .fromRequestUri(request)
                .replacePath("/files/{fileName}")
                .buildAndExpand(path)
                .toUriString();
    }
}
