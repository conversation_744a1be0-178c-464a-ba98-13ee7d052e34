package com.cottontang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 广告数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class AdvertisementDTO {
    
    /**
     * 广告ID
     */
    private Long id;
    
    /**
     * 广告标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;
    
    /**
     * 广告类型
     */
    @NotNull(message = "类型不能为空")
    private Integer type;
    
    /**
     * 广告图片URL
     */
    private String thumbs;
    
    /**
     * 广告链接URL
     */
    @Size(max = 500, message = "URL长度不能超过500个字符")
    private String url;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 广告内容
     */
    private String content;
} 