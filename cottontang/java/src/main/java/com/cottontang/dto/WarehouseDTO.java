package com.cottontang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 仓库数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class WarehouseDTO {
    
    /**
     * 仓库ID
     */
    private Long id;
    
    /**
     * 仓库标题/名称
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;
    
    /**
     * 仓库地址
     */
    @Size(max = 500, message = "地址长度不能超过500个字符")
    private String address;
    
    /**
     * 一级区域ID
     */
    @NotNull(message = "一级区域不能为空")
    private Long level1;
    
    /**
     * 二级区域ID
     */
    private Long level2;
    
    /**
     * 三级区域ID
     */
    private Long level3;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 是否推荐（0: 否, 1: 是）
     */
    private Integer isRecom;
    
    /**
     * 联系电话
     */
    @Size(max = 20, message = "电话长度不能超过20个字符")
    private String phone;
    
    /**
     * 仓库缩略图
     */
    private String thumbs;
    
    /**
     * 仓库容量
     */
    @Size(max = 100, message = "容量长度不能超过100个字符")
    private String capacity;
} 