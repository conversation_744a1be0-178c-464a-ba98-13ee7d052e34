package com.cottontang.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 分页查询参数
 *
 * <AUTHOR>
 */
@Data
public class PageQuery {

    /**
     * 当前页码（从1开始）
     */
    @Min(value = 1, message = "页码不能小于1")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 500, message = "每页大小不能超过500")
    private Long size = 10L;

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：asc/desc
     */
    private String orderDirection = "desc";

    /**
     * 开始时间（时间戳）
     */
    private Long startTime;

    /**
     * 结束时间（时间戳）
     */
    private Long endTime;

    /**
     * 状态筛选
     */
    private Integer status;

    /**
     * 分类ID筛选
     */
    private Long categoryId;

    /**
     * 用户ID筛选
     */
    private Long userId;

    // 兼容性方法，用于适配现有代码
    public Integer getPageNum() {
        return current.intValue();
    }

    public void setPageNum(Integer pageNum) {
        this.current = pageNum.longValue();
    }

    public Integer getPageSize() {
        return size.intValue();
    }

    public void setPageSize(Integer pageSize) {
        this.size = pageSize.longValue();
    }

    /**
     * 获取偏移量（用于数据库查询）
     *
     * @return 偏移量
     */
    public Long getOffset() {
        return (current - 1) * size;
    }

    public Long getPage() {
        return current;
    }

    /**
     * 获取限制数量（用于数据库查询）
     *
     * @return 限制数量
     */
    public Long getLimit() {
        return size;
    }

    /**
     * 检查是否有搜索关键词
     *
     * @return 如果有关键词则返回true
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    /**
     * 检查是否有时间范围筛选
     *
     * @return 如果有时间范围则返回true
     */
    public boolean hasTimeRange() {
        return startTime != null && endTime != null;
    }

    /**
     * 检查是否有状态筛选
     *
     * @return 如果有状态筛选则返回true
     */
    public boolean hasStatus() {
        return status != null;
    }

    /**
     * 检查是否有分类筛选
     *
     * @return 如果有分类筛选则返回true
     */
    public boolean hasCategory() {
        return categoryId != null;
    }

    /**
     * 检查是否有用户筛选
     *
     * @return 如果有用户筛选则返回true
     */
    public boolean hasUser() {
        return userId != null;
    }
}
