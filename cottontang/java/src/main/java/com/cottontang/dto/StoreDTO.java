package com.cottontang.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 商品数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class StoreDTO implements Serializable {
    
    /**
     * ID（新记录为null）
     */
    private Long id;
    
    /**
     * 商品标题/名称
     */
    private String title;
    
    /**
     * 商品类型（棉花类型）
     */
    private String leixing;
    
    /**
     * 年度
     */
    private String niandu;
    
    /**
     * 状态
     */
    private String zhuangtai;
    
    /**
     * 批号/捆号
     */
    private String pihaoKunkao;
    
    /**
     * 颜色级/品级
     */
    private String yansejiPinji;
    
    /**
     * 长度
     */
    private String changdu;
    
    /**
     * 强度
     */
    private String qiangdu;
    
    /**
     * 马克隆值
     */
    private String maikeluona;
    
    /**
     * 反光度
     */
    private String fanguangdu;
    
    /**
     * 黄度
     */
    private String huangdu;
    
    /**
     * 整齐性
     */
    private String zhengqixing;
    
    /**
     * 结絮
     */
    private String jiezhu;
    
    /**
     * 短纤
     */
    private String duanxian;
    
    /**
     * 成熟度
     */
    private String chengshu;
    
    /**
     * 重量
     */
    private String weight;
    
    /**
     * 价格
     */
    private String price;
    
    /**
     * 仓库ID
     */
    private Long cangkuId;
} 