package com.cottontang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 管理员请求数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class AdminDTO {
    
    /**
     * 管理员ID
     */
    private Long id;
    
    /**
     * 管理员用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String name;
    
    /**
     * 管理员密码
     */
    private String password;
    
    /**
     * 管理员真实姓名
     */
    @Size(max = 100, message = "真实姓名长度不能超过100个字符")
    private String realName;
    
    /**
     * 管理员类型（0: 超级管理员, 1: 普通管理员）
     */
    private Integer type;
    
    /**
     * 管理员状态（0: 禁用, 1: 启用）
     */
    private Integer status;
    
    /**
     * 管理员排序ID
     */
    private Integer orderId;
} 