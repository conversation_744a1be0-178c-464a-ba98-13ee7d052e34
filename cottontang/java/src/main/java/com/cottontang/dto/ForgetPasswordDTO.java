package com.cottontang.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 忘记密码数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class ForgetPasswordDTO {

    /**
     * 用户名（手机号）
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "请输入正确的手机号")
    private String username;

    /**
     * 手机验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String mobile_code;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    private String password;
}
