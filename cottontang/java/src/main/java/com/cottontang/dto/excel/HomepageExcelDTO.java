package com.cottontang.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 首页数据Excel导入导出DTO
 * 
 * <AUTHOR>
 */
@Data
public class HomepageExcelDTO {
    
    @ExcelProperty("序号")
    private String xvhao;
    
    @ExcelProperty("批号/捆号")
    private String pihaoKunkao;
    
    @ExcelProperty("类型")
    private String leixing;
    
    @ExcelProperty("颜色级/品级")
    private String yansejiPinji;
    
    @ExcelProperty("马值")
    private String mazhi;
    
    @ExcelProperty("长度")
    private String changdu;
    
    @ExcelProperty("强力")
    private String qiangli;
    
    @ExcelProperty("含杂率")
    private String hanzalv;
    
    @ExcelProperty("回潮率")
    private String huichaolv;
    
    @ExcelProperty("公重(吨)")
    private String gongzhong;
    
    @ExcelProperty("毛重(吨)")
    private String maozhaong;
    
    @ExcelProperty("整齐度")
    private String zhengqidu;
    
    @ExcelProperty("加工厂")
    private String jiagongchang;
    
    @ExcelProperty("仓储名称")
    private String cangchumingcheng;
    
    @ExcelProperty("基差")
    private Integer jicha;
    
    @ExcelProperty("备注")
    private String beizhu;
    
    @ExcelProperty("点价合约")
    private String dianjiaheyue;
    
    @ExcelProperty("包数")
    private String baoshu;
    
    @ExcelProperty("单据类型")
    private String danType;
    
    @ExcelProperty("产地")
    private String chandi;
    
    @ExcelProperty("状态")
    private String zhuangtai;
    
    @ExcelProperty("白棉1/2/3级")
    private BigDecimal bm123;
}
