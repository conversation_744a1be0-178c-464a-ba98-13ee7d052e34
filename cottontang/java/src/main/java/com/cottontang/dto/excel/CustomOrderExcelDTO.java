package com.cottontang.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 定制订单Excel导入导出DTO
 * 
 * <AUTHOR>
 */
@Data
public class CustomOrderExcelDTO {
    
    @ExcelProperty("订制编号")
    private String dingzhiNo;
    
    @ExcelProperty("批号/捆号")
    private String pihaoKunkao;
    
    @ExcelProperty("类型")
    private String leixing;
    
    @ExcelProperty("颜色级/品级")
    private String yansejiPinji;
    
    @ExcelProperty("马值")
    private String mazhi;
    
    @ExcelProperty("长度")
    private String changdu;
    
    @ExcelProperty("强力")
    private String qiangli;
    
    @ExcelProperty("含杂率")
    private String hanzalv;
    
    @ExcelProperty("回潮率")
    private String huichaolv;
    
    @ExcelProperty("公重(吨)")
    private String gongzhong;
    
    @ExcelProperty("毛重(吨)")
    private String maozhaong;
    
    @ExcelProperty("整齐度")
    private String zhengqidu;
    
    @ExcelProperty("加工厂")
    private String jiagongchang;
    
    @ExcelProperty("仓储名称")
    private String cangchumingcheng;
    
    @ExcelProperty("基差")
    private String jicha;
    
    @ExcelProperty("备注")
    private String beizhu;
    
    @ExcelProperty("点价合约")
    private String dianjiaheyue;
    
    @ExcelProperty("件数")
    private String baoshu;
    
    @ExcelProperty("状态")
    private String status;
    
    @ExcelProperty("用户ID")
    private String uid;
    
    @ExcelProperty("创建时间")
    private String addTime;
}
