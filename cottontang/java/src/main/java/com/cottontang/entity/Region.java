package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 区域实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_jiaohuodi")
public class Region extends BaseEntity {
    
    /**
     * 区域名称
     */
    private String name;
    
    /**
     * 父ID（0表示顶层）
     */
    private Long pid;
    
    /**
     * 排序ID
     */
    private Integer orderId;
    
    /**
     * 子区域（临时字段）
     */
    @TableField(exist = false)
    private List<Region> children;
} 