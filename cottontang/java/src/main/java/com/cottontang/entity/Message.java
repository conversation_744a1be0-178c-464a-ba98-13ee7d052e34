package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_msg")
public class Message extends BaseEntity {

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 附件图片
     */
    private String thumbs;

    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;
}
