package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统设置实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_setting")
public class SystemSetting extends BaseEntity {
    
    /**
     * 设置键
     */
    private String key;

    /**
     * 设置值
     */
    private String value;

    /**
     * 设置描述
     */
    private String desc;

    /**
     * 设置类型
     */
    private String type;

    /**
     * 字段类型
     */
    private String field;
}
