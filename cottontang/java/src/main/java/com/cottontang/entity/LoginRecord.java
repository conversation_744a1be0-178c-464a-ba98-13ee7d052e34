package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录记录实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_denglu")
public class LoginRecord extends BaseEntity {

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 用户文本信息
     */
    private String uidText;


    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;
}
