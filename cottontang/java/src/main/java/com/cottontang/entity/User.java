package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_user")
public class User extends BaseEntity {

    /**
     * 用户登录名（手机号）
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户密码（加密的）
     */
    private String password;

    /**
     * 最后登录时间戳
     */
    private Long lastLogin;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 登录时间戳
     */
    private Long loginTime;

    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;


    /**
     * 会员类型（0: 普通, 1: VIP）
     */
    private Integer memberType;

    /**
     * 是否审核通过（0: 未审核, 1: 已审核）
     */
    private Integer isCheck;
}
