package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 广告实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_adv")
public class Advertisement extends BaseEntity {

    /**
     * 广告类型
     */
    private Integer type;

    /**
     * 广告图片URL
     */
    private String thumbs;

    /**
     * 广告链接URL
     */
    private String url;

    private String name;

    /**
     * 排序ID
     */
    private Integer orderId;



    /**
     * 广告摘要
     */
    private String summary;

    /**
     * 英文摘要
     */
    private String summaryEn;

    /**
     * 第二张图片
     */
    private String thumbs2;
}
