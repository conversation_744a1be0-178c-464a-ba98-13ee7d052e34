package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文章实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_art")
public class Article extends BaseEntity {

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 文章分类ID
     */
    private Long catId;

    private String summary;


    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;

    /**
     * 排序ID
     */
    private Integer orderId;

    /**
     * 文章缩略图
     */
    private String thumbs;

    /**
     * 点击次数
     */
    private Integer click;

    /**
     * 发布日期
     */
    private Long pubDate;

    /**
     * 是否推荐（0: 否, 1: 是）
     */
    private Integer isRecom;

    /**
     * 分类名称（临时字段）
     */
    @TableField(exist = false)
    private String catName;


}
