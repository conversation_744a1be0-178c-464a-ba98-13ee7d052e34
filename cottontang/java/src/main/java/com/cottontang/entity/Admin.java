package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 管理员实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_admin")
public class Admin extends BaseEntity {

    /**
     * 管理员用户名
     */
    private String name;

    /**
     * 管理员密码（加密的）
     */
    private String pwd;

    /**
     * 管理员类型（0: 超级管理员, 1: 普通管理员）
     */
    private Integer type;

    /**
     * 权限配置
     */
    private String permission;

    /**
     * 创建时间戳
     */
    @TableField(fill = FieldFill.INSERT)
    private Long addTime;
}
