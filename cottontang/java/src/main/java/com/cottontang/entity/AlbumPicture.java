package com.cottontang.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 相册图片实体
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sd_xiangce_pic")
public class AlbumPicture extends BaseEntity {
    
    /**
     * 图片缩略图
     */
    private String thumbs;

    /**
     * 关联ID
     */
    private String linkId;
    

}
