package com.cottontang.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.cottontang.common.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 测试Redis连接
     */
    @GetMapping("/redis")
    public Result<Map<String, Object>> testRedis() {
        try {
            // 测试Redis连接
            redisTemplate.opsForValue().set("test:key", "test:value");
            String value = (String) redisTemplate.opsForValue().get("test:key");
            
            Map<String, Object> result = new HashMap<>();
            result.put("redis_status", "connected");
            result.put("test_value", value);
            
            return Result.success(result);
        } catch (Exception e) {
            Map<String, Object> result = new HashMap<>();
            result.put("redis_status", "error");
            result.put("error_message", e.getMessage());
            
            return Result.error("Redis连接失败", result);
        }
    }

    /**
     * 测试Sa-Token状态
     */
    @GetMapping("/satoken")
    public Result<Map<String, Object>> testSaToken() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否登录
            boolean isLogin = StpUtil.isLogin();
            result.put("is_login", isLogin);
            
            if (isLogin) {
                result.put("login_id", StpUtil.getLoginId());
                result.put("token_value", StpUtil.getTokenValue());
                result.put("token_info", StpUtil.getTokenInfo());
            }
            
            return Result.success(result);
        } catch (Exception e) {
            result.put("error", e.getMessage());
            return Result.error("Sa-Token测试失败", result);
        }
    }
}
