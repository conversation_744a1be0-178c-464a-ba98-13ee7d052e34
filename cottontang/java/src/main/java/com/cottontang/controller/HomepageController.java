package com.cottontang.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Homepage;
import com.cottontang.service.HomepageService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 首页数据控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/homepage")
@RequiredArgsConstructor
public class HomepageController {

    private final HomepageService homepageService;

    /**
     * 获取首页数据列表（每日精选）
     *
     * @return 首页数据列表
     */
    @GetMapping("/list")
    public Result<List<Homepage>> list() {
        List<Homepage> list = homepageService.getHomepageList();
        return Result.success(list);
    }

    /**
     * 获取首页数据列表（分页）
     *
     * @param pageQuery 分页和搜索参数
     * @return 首页数据分页
     */
    @GetMapping("/page")
    public Result<PageData<Homepage>> page(PageQuery pageQuery) {
        Page<Homepage> page = homepageService.getHomepageList(pageQuery);
        return Result.success(PageData.from(page));
    }

    /**
     * 根据ID获取首页数据
     *
     * @param id 数据ID
     * @return 首页数据详情
     */
    @GetMapping("/{id}")
    public Result<Homepage> getById(@PathVariable Long id) {
        return Result.success(homepageService.getById(id));
    }

    /**
     * 添加首页数据
     *
     * @param homepage 首页数据
     * @return 添加结果
     */
    @PostMapping
    public Result<String> add(@RequestBody Homepage homepage) {
        boolean success = homepageService.save(homepage);
        return success ? Result.success("添加成功") : Result.error("添加失败");
    }

    /**
     * 更新首页数据
     *
     * @param id 数据ID
     * @param homepage 首页数据
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @RequestBody Homepage homepage) {
        homepage.setId(id);
        boolean success = homepageService.updateById(homepage);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    /**
     * 删除首页数据
     *
     * @param id 数据ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        boolean success = homepageService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }
}
