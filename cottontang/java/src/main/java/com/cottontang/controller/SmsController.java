package com.cottontang.controller;

import com.cottontang.common.Result;
import com.cottontang.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 短信验证码控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
public class SmsController {

    private final SmsService smsService;

    /**
     * 发送手机验证码 - 使用阿里云短信服务和Redis缓存
     * 对应PHP的 Tools/getCodeAly 接口
     *
     * @param mobile 手机号
     * @return 发送结果
     */
    @GetMapping("/getCodeAly")
    public Result<Map<String, Object>> getCodeAly(@RequestParam String mobile) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 验证手机号格式
            if (!mobile.matches("^1\\d{10}$")) {
                result.put("code", 0);
                result.put("msg", "请输入正确的手机号");
                return Result.success(result);
            }

            // 发送验证码
            boolean sendResult = smsService.generateAndSendCode(mobile);

            if (sendResult) {
                result.put("code", 1);
                result.put("msg", "验证码已发送，请注意查收");
                log.info("验证码发送成功: 手机号={}", mobile);
            } else {
                result.put("code", 0);
                result.put("msg", "验证码发送失败，请稍后重试");
                log.error("验证码发送失败: 手机号={}", mobile);
            }

            return Result.success(result);

        } catch (Exception e) {
            log.error("发送验证码异常: 手机号={}", mobile, e);
            result.put("code", 0);
            result.put("msg", "验证码发送失败，请稍后重试");
            return Result.success(result);
        }
    }
}
