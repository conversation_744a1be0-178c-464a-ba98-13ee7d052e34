package com.cottontang.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.cottontang.common.Result;
import com.cottontang.dto.LoginDTO;
import com.cottontang.dto.RegisterDTO;
import com.cottontang.dto.ForgetPasswordDTO;
import com.cottontang.dto.UserInfoDTO;
import com.cottontang.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户认证控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;

    /**
     * 用户登录 - 完全按照PHP逻辑
     *
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    @PostMapping("/runLogin")
    public Result<Map<String, Object>> runLogin(@Valid @RequestBody LoginDTO loginDTO) {
        try {
            // 执行用户登录
            Map<String, Object> loginResult = userService.login(loginDTO);

            return Result.success(loginResult);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/info")
    public Result<UserInfoDTO> getUserInfo() {
        try {
            // 检查用户是否已登录
            StpUtil.checkLogin();

            // 获取用户信息
            UserInfoDTO userInfo = userService.getUserInfo(StpUtil.getLoginIdAsLong());

            return Result.success(userInfo);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @return 成功结果
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        try {
            userService.logout();
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户注册 - 完全按照PHP逻辑
     *
     * @param registerDTO 注册参数
     * @return 注册结果
     */
    @PostMapping("/runReg")
    public Result<Map<String, Object>> runReg(@Valid @RequestBody RegisterDTO registerDTO) {
        try {
            userService.register(registerDTO);
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 忘记密码 - 完全按照PHP逻辑
     *
     * @param forgetPasswordDTO 忘记密码参数
     * @return 修改结果
     */
    @PostMapping("/runForget")
    public Result<Map<String, Object>> runForget(@Valid @RequestBody ForgetPasswordDTO forgetPasswordDTO) {
        try {
            userService.forgetPassword(forgetPasswordDTO);
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
