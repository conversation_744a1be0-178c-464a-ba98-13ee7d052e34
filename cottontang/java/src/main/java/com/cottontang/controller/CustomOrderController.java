package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.CustomOrder;
import com.cottontang.service.CustomOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 定制订单控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/custom-orders")
@RequiredArgsConstructor
public class CustomOrderController {

    private final CustomOrderService customOrderService;

    /**
     * 获取管理员订单列表（分页）
     *
     * @param pageQuery 分页和搜索参数
     * @return 订单分页
     */
    @GetMapping("/admin/list")
    @SaCheckRole("admin")
    public Result<PageData<CustomOrder>> adminList(PageQuery pageQuery) {
        Page<CustomOrder> page = customOrderService.getCustomOrderList(pageQuery);
        return Result.success(PageData.from(page));
    }

    /**
     * 获取用户订单列表（分页）
     *
     * @param pageQuery 分页和搜索参数
     * @return 订单分页
     */
    @GetMapping("/user/list")
    @SaCheckLogin
    public Result<PageData<CustomOrder>> userList(PageQuery pageQuery) {
        Long userId = StpUtil.getLoginIdAsLong();
        Page<CustomOrder> page = customOrderService.getUserCustomOrderList(userId, pageQuery);
        return Result.success(PageData.from(page));
    }

    /**
     * 获取当前用户的个性需求列表（用于首页显示）
     *
     * @return 个性需求列表
     */
    @GetMapping("/my-list")
    @SaCheckLogin
    public Result<List<CustomOrder>> myList() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<CustomOrder> list = customOrderService.getUserRecentCustomOrders(userId, 3);
        return Result.success(list);
    }

    /**
     * 根据ID获取订单
     *
     * @param id 订单ID
     * @return 订单详情
     */
    @GetMapping("/{id}")
    @SaCheckLogin
    public Result<CustomOrder> getById(@PathVariable Long id) {
        return Result.success(customOrderService.getById(id));
    }

    /**
     * 创建订单
     *
     * @param customOrder 订单信息
     * @return 创建结果
     */
    @PostMapping
    @SaCheckLogin
    public Result<String> create(@Valid @RequestBody CustomOrder customOrder) {
        Long userId = StpUtil.getLoginIdAsLong();
        boolean success = customOrderService.createCustomOrder(userId, customOrder);
        return success ? Result.success("创建成功") : Result.error("创建失败");
    }

    /**
     * 更新订单
     *
     * @param id 订单ID
     * @param customOrder 订单信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @SaCheckLogin
    public Result<String> update(@PathVariable Long id, @Valid @RequestBody CustomOrder customOrder) {
        customOrder.setId(id);
        boolean success = customOrderService.updateById(customOrder);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckLogin
    public Result<String> delete(@PathVariable Long id) {
        boolean success = customOrderService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 更新订单状态
     *
     * @param id 订单ID
     * @param status 状态
     * @return 更新结果
     */
    @PutMapping("/{id}/status")
    @SaCheckRole("admin")
    public Result<String> updateStatus(@PathVariable Long id, @RequestParam Integer status) {
        boolean success = customOrderService.updateOrderStatus(id, status);
        return success ? Result.success("状态更新成功") : Result.error("状态更新失败");
    }
}
