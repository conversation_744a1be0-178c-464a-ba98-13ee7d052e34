package com.cottontang.controller;

import com.cottontang.common.Result;
import com.cottontang.entity.Article;
import com.cottontang.entity.Store;
import com.cottontang.entity.User;
import com.cottontang.exception.BusinessException;
import com.cottontang.service.ArticleService;
import com.cottontang.service.StoreService;
import com.cottontang.service.UserService;
import com.cottontang.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel控制器，处理导入导出操作
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/excel")
@Slf4j
public class ExcelController {

    @Autowired
    private ArticleService articleService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreService storeService;

    /**
     * 将文章导出为Excel文件
     *
     * @param catId 可选的分类ID过滤
     * @return Excel文件
     */
    @GetMapping("/export/articles")
    public ResponseEntity<byte[]> exportArticles(@RequestParam(required = false) Long catId) {
        List<Article> articles;
        if (catId != null) {
            articles = articleService.getArticlesByCategoryId(catId);
        } else {
            articles = articleService.list();
        }

        // 定义字段到表头的映射
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("id", "ID");
        headerMapping.put("title", "标题");
        headerMapping.put("summary", "摘要");
        headerMapping.put("catId", "分类ID");
        headerMapping.put("author", "作者");
        headerMapping.put("pubDate", "发布日期");
        headerMapping.put("viewCount", "浏览次数");
        headerMapping.put("status", "状态");
        headerMapping.put("orderId", "排序ID");
        headerMapping.put("isRecom", "是否推荐");

        // 生成Excel字节数组
        byte[] excelBytes = ExcelUtils.exportExcel(articles, headerMapping, "文章列表");

        // 生成文件名
        String fileName = "articles_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HHmmss")) + ".xlsx";

        // 返回Excel文件
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodeFileName(fileName))
                .body(excelBytes);
    }

    /**
     * 将用户导出为Excel文件
     *
     * @return Excel文件
     */
    @GetMapping("/export/users")
    public ResponseEntity<byte[]> exportUsers() {
        List<User> users = userService.list();

        // 定义字段到表头的映射
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("id", "ID");
        headerMapping.put("username", "用户名");
        headerMapping.put("nickname", "昵称");
        headerMapping.put("email", "邮箱");
        headerMapping.put("mobile", "手机号");
        headerMapping.put("status", "状态");
        headerMapping.put("createTime", "创建时间");
        headerMapping.put("lastLoginTime", "最后登录时间");

        // 生成Excel字节数组
        byte[] excelBytes = ExcelUtils.exportExcel(users, headerMapping, "用户列表");

        // 生成文件名
        String fileName = "users_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HHmmss")) + ".xlsx";

        // 返回Excel文件
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodeFileName(fileName))
                .body(excelBytes);
    }

    /**
     * 将店铺导出为Excel文件
     *
     * @return Excel文件
     */
    @GetMapping("/export/stores")
    public ResponseEntity<byte[]> exportStores() {
        List<Store> stores = storeService.list();

        // 定义字段到表头的映射
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("id", "ID");
        headerMapping.put("title", "店铺名称");
        headerMapping.put("leixing", "类型");
        headerMapping.put("niandu", "年度");
        headerMapping.put("zhuangtai", "状态");
        headerMapping.put("pihaoKunkao", "批号/捆号");
        headerMapping.put("yansejiPinji", "颜色级/品级");
        headerMapping.put("price", "价格");
        headerMapping.put("weight", "重量");
        headerMapping.put("createTime", "创建时间");

        // 生成Excel字节数组
        byte[] excelBytes = ExcelUtils.exportExcel(stores, headerMapping, "店铺列表");

        // 生成文件名
        String fileName = "stores_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HHmmss")) + ".xlsx";

        // 返回Excel文件
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + encodeFileName(fileName))
                .body(excelBytes);
    }

    /**
     * 从Excel导入用户
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import/users")
    public Result<Map<String, Object>> importUsers(@RequestParam("file") MultipartFile file) {
        // 定义表头到字段的映射
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("用户名", "username");
        headerMapping.put("昵称", "nickname");
        headerMapping.put("邮箱", "email");
        headerMapping.put("手机号", "mobile");

        // 从Excel导入数据
        List<Map<String, Object>> dataList = ExcelUtils.importExcel(file, headerMapping);

        // 处理导入的数据
        int successCount = 0;
        int failCount = 0;

        for (Map<String, Object> data : dataList) {
            try {
                User user = new User();

                // 从Excel数据设置用户属性
                if (data.containsKey("username")) {
                    user.setUsername(data.get("username").toString());
                }
                if (data.containsKey("nickname")) {
                    user.setNickname(data.get("nickname").toString());
                }

                // 设置默认密码
                user.setPassword("123456");
                // 保存用户
                userService.save(user);

                successCount++;
            } catch (Exception e) {
                log.error("导入用户失败", e);
                failCount++;
            }
        }

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", dataList.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);

        return Result.success(result);
    }

    /**
     * 从Excel导入店铺
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import/stores")
    public Result<Map<String, Object>> importStores(@RequestParam("file") MultipartFile file) {
        // 定义表头到字段的映射
        Map<String, String> headerMapping = new HashMap<>();
        headerMapping.put("店铺名称", "title");
        headerMapping.put("类型", "leixing");
        headerMapping.put("年度", "niandu");
        headerMapping.put("批号/捆号", "pihaoKunkao");
        headerMapping.put("颜色级/品级", "yansejiPinji");
        headerMapping.put("价格", "price");
        headerMapping.put("重量", "weight");

        // 从Excel导入数据
        List<Map<String, Object>> dataList = ExcelUtils.importExcel(file, headerMapping);

        // 处理导入的数据
        int successCount = 0;
        int failCount = 0;

        for (Map<String, Object> data : dataList) {
            try {
                Store store = new Store();

//                // 从Excel数据设置店铺属性
//                if (data.containsKey("title")) {
//                    store.setTitle(data.get("title").toString());
//                } else {
//                    throw new BusinessException("店铺名称是必填项");
//                }

                if (data.containsKey("leixing")) {
                    store.setLeixing(data.get("leixing").toString());
                }

//                if (data.containsKey("niandu")) {
//                    store.setNiandu(data.get("niandu").toString());
//                }

                if (data.containsKey("pihaoKunkao")) {
                    store.setPihaoKunkao(data.get("pihaoKunkao").toString());
                }

                if (data.containsKey("yansejiPinji")) {
                    store.setYansejiPinji(data.get("yansejiPinji").toString());
                }

//                if (data.containsKey("price")) {
//                    store.setPrice(data.get("price").toString());
//                }
//
//                if (data.containsKey("weight")) {
//                    store.setWeight(data.get("weight").toString());
//                }

                // 设置默认状态
                store.setZhuangtai("正常");

                // 保存店铺
                storeService.addStore(store);

                successCount++;
            } catch (Exception e) {
                log.error("导入店铺失败", e);
                failCount++;
            }
        }

        // 返回导入结果
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", dataList.size());
        result.put("successCount", successCount);
        result.put("failCount", failCount);

        return Result.success(result);
    }

    /**
     * 对文件名进行编码处理，解决特殊字符问题
     *
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    private String encodeFileName(String fileName) {
        try {
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.error("文件名编码失败", e);
            return fileName;
        }
    }
}
