package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.AdminDTO;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Admin;
import com.cottontang.service.AdminService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理员用户控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {
    
    private final AdminService adminService;
    
    /**
     * 获取管理员列表（分页）
     * 
     * @param pageQuery 分页和搜索参数
     * @return 管理员分页
     */
    @GetMapping("/list")
    @SaCheckRole("admin")
    public Result<PageData<Admin>> list(PageQuery pageQuery) {
        Page<Admin> page = adminService.getAdminList(pageQuery);
        return Result.success(PageData.from(page));
    }
    
    /**
     * 根据ID获取管理员
     * 
     * @param id 管理员ID
     * @return 管理员详情
     */
    @GetMapping("/{id}")
    @SaCheckRole("admin")
    public Result<Admin> getById(@PathVariable Long id) {
        return Result.success(adminService.getById(id));
    }
    
    /**
     * 添加管理员
     * 
     * @param adminDTO 管理员数据
     * @return 成功或错误
     */
    @PostMapping
    @SaCheckRole("super_admin")
    public Result<Boolean> add(@Valid @RequestBody AdminDTO adminDTO) {
        return Result.success(adminService.addAdmin(adminDTO));
    }
    
    /**
     * 更新管理员
     * 
     * @param adminDTO 管理员数据
     * @return 成功或错误
     */
    @PutMapping
    @SaCheckRole("super_admin")
    public Result<Boolean> update(@Valid @RequestBody AdminDTO adminDTO) {
        return Result.success(adminService.updateAdmin(adminDTO));
    }
    
    /**
     * 删除管理员
     * 
     * @param id 管理员ID
     * @return 成功或错误
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("super_admin")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(adminService.deleteAdmin(id));
    }
    
    /**
     * 更改管理员状态
     * 
     * @param id 管理员ID
     * @param status 新状态 (0: 禁用, 1: 启用)
     * @return 成功或错误
     */
    @PutMapping("/{id}/status")
    @SaCheckRole("super_admin")
    public Result<Boolean> changeStatus(
            @PathVariable Long id,
            @RequestParam Integer status) {
        return Result.success(adminService.changeStatus(id, status));
    }
    
    /**
     * 更新管理员排序ID
     * 
     * @param id 管理员ID
     * @param orderId 新排序ID
     * @return 成功或错误
     */
    @PutMapping("/{id}/order")
    @SaCheckRole("super_admin")
    public Result<Boolean> updateOrderId(
            @PathVariable Long id,
            @RequestParam Integer orderId) {
        return Result.success(adminService.updateOrderId(id, orderId));
    }
} 
