package com.cottontang.controller;

import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.Article;
import com.cottontang.entity.Store;
import com.cottontang.service.ArticleService;
import com.cottontang.service.StoreService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 公开API控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/public")
@RequiredArgsConstructor
public class PublicController {

    private final StoreService storeService;
    private final ArticleService articleService;

    /**
     * 获取热门产品
     *
     * @param limit 限制数量
     * @return 热门产品列表
     */
    @GetMapping("/hot-products")
    public Result<List<Store>> getHotProducts(@RequestParam(defaultValue = "8") Integer limit) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setSize((long) limit);
        pageQuery.setCurrent(1L);

        List<Store> products = storeService.getStoreList(pageQuery).getRecords();
        return Result.success(products);
    }

    /**
     * 获取新闻列表
     *
     * @param limit 限制数量
     * @param category 分类
     * @return 新闻列表
     */
    @GetMapping("/news")
    public Result<Object> getNewsList(@RequestParam(defaultValue = "6") Integer limit,
                                     @RequestParam(required = false) String category,
                                     @RequestParam(required = false) String keyword,
                                     @RequestParam(defaultValue = "1") Integer pageNum,
                                     @RequestParam(defaultValue = "10") Integer pageSize) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setSize((long) pageSize);
        pageQuery.setCurrent((long) pageNum);
        pageQuery.setKeyword(keyword);
        pageQuery.setCategoryId(category != null ? Long.parseLong(category) : null);

        return Result.success(articleService.getArticleList(pageQuery));
    }

    /**
     * 根据ID获取新闻详情
     *
     * @param id 新闻ID
     * @return 新闻详情
     */
    @GetMapping("/news/{id}")
    public Result<Article> getNewsById(@PathVariable Long id) {
        return Result.success(articleService.getById(id));
    }

    /**
     * 获取产品分类
     *
     * @return 分类列表
     */
    @GetMapping("/categories")
    public Result<List<Map<String, Object>>> getProductCategories() {
        // 这里应该从分类服务获取数据
        // 暂时返回空列表
        return Result.success(List.of());
    }

    /**
     * 获取公司信息
     *
     * @return 公司信息
     */
    @GetMapping("/company-info")
    public Result<Map<String, Object>> getCompanyInfo() {
        Map<String, Object> companyInfo = Map.of(
            "name", "棉花棠",
            "description", "专业的棉花贸易平台",
            "address", "新疆乌鲁木齐市经济技术开发区",
            "phone", "************",
            "email", "<EMAIL>",
            "website", "www.cottontang.com"
        );
        return Result.success(companyInfo);
    }

    /**
     * 获取轮播图
     *
     * @return 轮播图列表
     */
    @GetMapping("/banners")
    public Result<List<Map<String, Object>>> getBanners() {
        // 这里应该从广告服务获取轮播图数据
        // 暂时返回空列表
        return Result.success(List.of());
    }

    /**
     * 提交联系表单
     *
     * @param contactData 联系表单数据
     * @return 提交结果
     */
    @PostMapping("/contact")
    public Result<String> submitContactForm(@RequestBody Map<String, Object> contactData) {
        // 这里应该保存联系表单数据或发送邮件
        // 暂时直接返回成功
        return Result.success("联系表单提交成功，我们会尽快与您联系");
    }
}
