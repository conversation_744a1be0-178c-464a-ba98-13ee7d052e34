package com.cottontang.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cottontang.common.PageData;
import com.cottontang.common.Result;
import com.cottontang.dto.PageQuery;
import com.cottontang.entity.User;
import com.cottontang.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 获取用户列表（分页）
     *
     * @param pageQuery 分页和搜索参数
     * @return 用户分页
     */
    @GetMapping("/list")
    @SaCheckRole("admin")
    public Result<PageData<User>> list(PageQuery pageQuery) {
        Page<User> page = userService.getUserList(pageQuery);
        return Result.success(PageData.from(page));
    }

    /**
     * 根据ID获取用户
     *
     * @param id 用户ID
     * @return 用户详情
     */
    @GetMapping("/{id}")
    @SaCheckRole("admin")
    public Result<User> getById(@PathVariable Long id) {
        return Result.success(userService.getById(id));
    }

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @return 创建结果
     */
    @PostMapping
    @SaCheckRole("admin")
    public Result<String> create(@Valid @RequestBody User user) {
        boolean success = userService.createUser(user);
        return success ? Result.success("创建成功") : Result.error("创建失败");
    }

    /**
     * 更新用户
     *
     * @param id 用户ID
     * @param user 用户信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @SaCheckRole("admin")
    public Result<String> update(@PathVariable Long id, @Valid @RequestBody User user) {
        user.setId(id);
        boolean success = userService.updateUser(user);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("admin")
    public Result<String> delete(@PathVariable Long id) {
        boolean success = userService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 审核用户
     *
     * @param id 用户ID
     * @param isCheck 审核状态（0：未审核，1：已审核）
     * @return 审核结果
     */
    @PutMapping("/{id}/check")
    @SaCheckRole("admin")
    public Result<String> checkUser(@PathVariable Long id, @RequestParam Integer isCheck) {
        boolean success = userService.checkUser(id, isCheck);
        return success ? Result.success("审核成功") : Result.error("审核失败");
    }

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @param password 新密码
     * @return 重置结果
     */
    @PutMapping("/{id}/reset-password")
    @SaCheckRole("admin")
    public Result<String> resetPassword(@PathVariable Long id, @RequestParam String password) {
        boolean success = userService.resetPassword(id, password);
        return success ? Result.success("密码重置成功") : Result.error("密码重置失败");
    }

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @SaCheckRole("admin")
    public Result<String> batchDelete(@RequestBody Long[] ids) {
        boolean success = userService.batchDelete(ids);
        return success ? Result.success("批量删除成功") : Result.error("批量删除失败");
    }
}
