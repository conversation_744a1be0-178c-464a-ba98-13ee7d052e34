import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 如果有令牌，添加到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = token
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    
    // 检查响应是否成功
    if (res.code !== 200) {
      // 显示错误信息
      ElMessage.error(res.message || '错误')
      
      // 处理认证错误
      if (res.code === 401) {
        // 清除本地存储并重定向到登录页
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        router.push('/login')
      }
      
      return Promise.reject(new Error(res.message || '错误'))
    } else {
      return res
    }
  },
  (error) => {
    console.error('响应错误:', error)
    ElMessage.error(error.message || '请求失败')
    return Promise.reject(error)
  }
)

// 通用请求方法
const request = <T = any>(config: AxiosRequestConfig): Promise<T> => {
  return service.request(config).then((res: any) => res.data)
}

// HTTP方法封装
export const get = <T = any>(url: string, params?: any): Promise<T> =>
  request({ url, method: 'get', params })

export const post = <T = any>(url: string, data?: any): Promise<T> =>
  request({ url, method: 'post', data })

export const put = <T = any>(url: string, data?: any): Promise<T> =>
  request({ url, method: 'put', data })

export const del = <T = any>(url: string, params?: any): Promise<T> =>
  request({ url, method: 'delete', params })

export default service 