<template>
  <div class="forget-page bg_f2">
    <div class="bg_f min_w">
      <div class="m_auto padt_15 clearfix">
        <div class="fl">
          <router-link to="/public/home">
            <img src="/images/logo.png" class="dis_b" style="height:78px;" />
          </router-link>
        </div>
        <div class="fl ml_50 pos_rela">
          <div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box" ref="plBox">
            <textarea
              style="width:400px;height: 150px;"
              id="pl_text"
              v-model="batchText"
              placeholder="每行输入一个批次/捆号"
              class="border1 dis_b pad_10"
            ></textarea>
            <input
              type="button"
              id="pl_sou"
              value="搜索"
              class="dis_b bg_ora"
              style="color: #fff;padding:5px 10px;border:0"
              @click="handleBatchSearch"
            />
          </div>
          <form action="#" id="ss_fm" style="margin-top:9px;" @submit.prevent="handleSearch">
            <input
              type="search"
              name="sk"
              id="sousuo_sk"
              v-model="searchKeyword"
              class="clr_9"
              autocomplete="off"
              placeholder="批号/轧花厂/仓库/产棉地区"
              style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;"
            >
            <button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
              <img src="/images/searth.png" style="width:20px;">
            </button>
            <span
              class="f_14 clr_r pointer"
              id="pl_click"
              style="position: absolute;top:30px;right:80px;"
              @click="toggleBatchBox"
            >批量</span>
          </form>
        </div>
        <div class="fr al_rt f_14 login">
          <div class="mt_10">
            <router-link to="/register" class="clr_d f_16">注册</router-link>
            <router-link to="/login" class="clr_d f_16 log_a">登录</router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码区域 - 完全按照PHP原版 -->
    <div class="login_bg min_w">
      <div class="m_auto clearfix">
        <div class="login_box bg_f fr mt_40">
          <div class="login_box_tt f_18 padl_30">修改密码</div>
          <div class="padl_40">
            <form id="fm1" @submit.prevent="handleForgetPassword">
              <div class="border1 pad_10 mt_30">
                <img src="/images/user.png" class="ver_mid" />
                <input
                  placeholder="手机号"
                  name="username"
                  id="username"
                  v-model="forgetForm.username"
                  class="f_16 clr_9 border0 ver_mid ml_10"
                />
              </div>
              <div class="border1 pad_10 mt_20 pos_rela">
                <img src="/images/mobile.png" class="ver_mid" />
                <input
                  class="f_14 clr_9 border0 ver_mid ml_10"
                  name="mobile_code"
                  v-model="forgetForm.mobileCode"
                  placeholder="手机验证码"
                  style="width:100px;"
                />
                <input
                  type="button"
                  :value="codeCountdown > 0 ? `${codeCountdown}秒` : '获取验证码'"
                  class="bg_b clr_f border0 pad_10"
                  id="getcode"
                  :disabled="codeCountdown > 0"
                  style="position: absolute;top:10px;right:10px;"
                  @click="sendCode"
                />
              </div>
              <div class="border1 pad_10 mt_20">
                <img src="/images/password.png" class="ver_mid" />
                <input
                  placeholder="输入新密码"
                  name="password"
                  id="password"
                  v-model="forgetForm.password"
                  type="password"
                  class="f_16 clr_9 border0 ver_mid ml_10"
                />
              </div>
              <div class="padt_30">
                <input
                  type="submit"
                  value="确认修改"
                  :disabled="loading"
                  class="f_16 clr_f bg_b w_100p border0 padt_10"
                />
                <div class="f_14 al_rt mt_10">
                  <router-link to="/login" class="clr_b">立即登录</router-link>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <div class="min_w">
      <div class="f_14 clr_9 al_ct padt_10 lh_28 footer-content">
        版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>
        Copyright &copy; 2021 鲁ICP备2021017032号-1
      </div>
    </div>

    <!-- 侧边栏 -->
    <div class="side">
      <ul>
        <li class="sideewm">
          <i class="bgs3"></i>官方微信
          <div class="ewBox son"></div>
        </li>
        <li class="sideetel">
          <i class="bgs4"></i>联系电话
          <div class="telBox son">
            <dd class="bgs2"><span>手机</span>19853227218</dd>
          </div>
        </li>
        <li class="sidetop" @click="goTop">
          <i class="bgs6"></i>返回顶部
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { forgetPassword, sendSmsCode } from '@/api/auth'

const router = useRouter()

// 搜索相关数据
const searchKeyword = ref('')
const batchText = ref('')
const plBox = ref<HTMLElement>()

// 忘记密码表单数据
const loading = ref(false)
const codeCountdown = ref(0)

const forgetForm = reactive({
  username: '',
  mobileCode: '',
  password: ''
})

// 搜索相关方法 - 完全按照PHP的jQuery逻辑
const toggleBatchBox = () => {
  if (plBox.value) {
    const currentDisplay = plBox.value.style.display
    if (currentDisplay === 'none' || !currentDisplay) {
      plBox.value.style.display = 'block'
      plBox.value.classList.remove('dis_n')
      plBox.value.classList.add('dis_b')
    } else {
      plBox.value.style.display = 'none'
      plBox.value.classList.remove('dis_b')
      plBox.value.classList.add('dis_n')
    }
  }
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    console.log('搜索关键词:', searchKeyword.value)
  }
}

const handleBatchSearch = () => {
  const ss = batchText.value.trim()
  const arr = ss.split(/\n/g)
  const xx: string[] = []
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].trim() !== '') {
      xx.push(arr[i].trim())
    }
  }
  searchKeyword.value = xx.toString()
  handleSearch()
}

// 发送验证码 - 完全按照PHP的jQuery逻辑
const sendCode = async () => {
  // 验证手机号
  if (!/^1\d{10}$/.test(forgetForm.username)) {
    alert('请输入正确的手机号')
    return false
  }

  try {
    await sendSmsCode(forgetForm.username)
    alert('验证码已发送')
    time()
  } catch (error: any) {
    alert(error.message || '验证码发送失败')
  }
}

// 倒计时函数 - 完全按照PHP的JavaScript逻辑
let wait = 120
function time() {
  if (wait == 0) {
    codeCountdown.value = 0
    wait = 120
  } else {
    codeCountdown.value = wait
    wait--
    setTimeout(function() {
      time()
    }, 1000)
  }
}

// 处理忘记密码 - 完全按照PHP的jQuery逻辑
const handleForgetPassword = async (e: Event) => {
  e.preventDefault()

  // 验证手机号
  if (!/^1\d{10}$/.test(forgetForm.username)) {
    alert('请输入正确的手机号码')
    const usernameEl = document.getElementById('username') as HTMLInputElement
    usernameEl?.focus()
    return false
  }

  // 验证新密码
  if (/^[ ]*$/.test(forgetForm.password)) {
    alert('请输入新密码')
    const passwordEl = document.getElementById('password') as HTMLInputElement
    passwordEl?.focus()
    return false
  }

  try {
    loading.value = true

    const forgetData = {
      username: forgetForm.username,
      password: forgetForm.password,
      mobile_code: forgetForm.mobileCode
    }

    const response = await forgetPassword(forgetData)
    alert(response.msg || '密码修改成功')
    if (response.url) {
      router.push(response.url)
    } else {
      router.push('/login')
    }
  } catch (error: any) {
    alert(error.message || '密码修改失败')
  } finally {
    loading.value = false
  }
}

// 返回顶部
const goTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 页面加载时的初始化
onMounted(() => {
  // 初始化代码
})
</script>

<style scoped>

</style>
