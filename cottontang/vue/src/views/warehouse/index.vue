<template>
  <div class="warehouse-container">
    <div class="top-actions">
      <el-button type="primary" @click="handleAddWarehouse">
        <el-icon><Plus /></el-icon>添加仓库
      </el-button>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索仓库名称/地址"
          clearable
          @clear="fetchWarehouseList"
          @input="handleSearchInput"
        >
          <template #append>
            <el-button @click="fetchWarehouseList">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    
    <el-table v-loading="loading" :data="warehouseList" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="name" label="仓库名称" width="150" />
      <el-table-column prop="address" label="地址" min-width="200" />
      <el-table-column prop="capacity" label="容量(吨)" width="100" />
      <el-table-column prop="usedCapacity" label="已使用(吨)" width="100" />
      <el-table-column prop="managerName" label="负责人" width="100" />
      <el-table-column prop="phone" label="联系电话" width="130" />
      <el-table-column label="使用状态" width="100">
        <template #default="scope">
          <el-progress
            :percentage="calculateUsage(scope.row)"
            :status="getUsageStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="status" label="仓库状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template #default="scope">
          <el-button size="small" @click="handleEditWarehouse(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleViewInventory(scope.row)"
          >查看库存</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDeleteWarehouse(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 仓库编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑仓库' : '添加仓库'" width="500px">
      <el-form
        ref="warehouseFormRef"
        :model="warehouseForm"
        :rules="warehouseRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="仓库名称" prop="name">
          <el-input v-model="warehouseForm.name" placeholder="请输入仓库名称" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="warehouseForm.address" placeholder="请输入仓库地址" />
        </el-form-item>
        <el-form-item label="容量(吨)" prop="capacity">
          <el-input-number v-model="warehouseForm.capacity" :min="0" :step="10" />
        </el-form-item>
        <el-form-item label="已使用(吨)" prop="usedCapacity" v-if="isEdit">
          <el-input-number v-model="warehouseForm.usedCapacity" :min="0" :max="warehouseForm.capacity" :step="1" />
        </el-form-item>
        <el-form-item label="负责人" prop="managerName">
          <el-input v-model="warehouseForm.managerName" placeholder="请输入负责人姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="warehouseForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="仓库状态" prop="status">
          <el-radio-group v-model="warehouseForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 库存详情对话框 -->
    <el-dialog v-model="inventoryVisible" title="库存详情" width="800px">
      <template #header>
        <div class="inventory-header">
          <h3>{{ currentWarehouse.name }} - 库存详情</h3>
          <div class="capacity-info">
            总容量: {{ currentWarehouse.capacity }}吨 / 已使用: {{ currentWarehouse.usedCapacity }}吨 / 
            剩余: {{ currentWarehouse.capacity - currentWarehouse.usedCapacity }}吨
          </div>
        </div>
      </template>
      
      <el-table :data="inventoryList" border>
        <el-table-column prop="productName" label="商品名称" />
        <el-table-column prop="batch" label="批次" width="120" />
        <el-table-column prop="quantity" label="数量(吨)" width="100" />
        <el-table-column prop="inboundTime" label="入库时间" width="180" />
        <el-table-column prop="shelfLife" label="保质期" width="100" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import {
  getWarehouseList,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  getWarehouseInventory
} from '@/api/warehouse'

// 表格数据
const loading = ref(false)
const warehouseList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')

// 对话框控制
const dialogVisible = ref(false)
const inventoryVisible = ref(false)
const isEdit = ref(false)
const warehouseFormRef = ref<FormInstance>()
const currentWarehouse = ref({
  id: 0,
  name: '',
  capacity: 0,
  usedCapacity: 0
})

// 库存列表数据
const inventoryList = ref([])

// 表单数据
const warehouseForm = reactive({
  id: 0,
  name: '',
  address: '',
  capacity: 1000,
  usedCapacity: 0,
  managerName: '',
  phone: '',
  status: 1
})

// 表单验证规则
const warehouseRules = reactive({
  name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
  address: [{ required: true, message: '请输入仓库地址', trigger: 'blur' }],
  capacity: [{ required: true, message: '请输入容量', trigger: 'blur' }],
  managerName: [{ required: true, message: '请输入负责人姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ]
})

// 计算使用率百分比
const calculateUsage = (row: any) => {
  if (row.capacity <= 0) return 0
  return Math.round((row.usedCapacity / row.capacity) * 100)
}

// 获取使用状态
const getUsageStatus = (row: any) => {
  const usage = calculateUsage(row)
  if (usage >= 90) return 'exception'
  if (usage >= 70) return 'warning'
  return 'success'
}

// 获取仓库列表
const fetchWarehouseList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchQuery.value
    }

    const response = await getWarehouseList(params)
    warehouseList.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取仓库列表失败')
    warehouseList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索输入
const handleSearchInput = () => {
  // 防抖处理，实际项目中建议使用lodash的debounce
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchWarehouseList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchWarehouseList()
}

// 添加仓库
const handleAddWarehouse = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑仓库
const handleEditWarehouse = (row: any) => {
  isEdit.value = true
  Object.assign(warehouseForm, row)
  dialogVisible.value = true
}

// 删除仓库
const handleDeleteWarehouse = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除仓库 ${row.name} 吗? 删除后无法恢复。`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteWarehouse(row.id)
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchWarehouseList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 查看库存
const handleViewInventory = async (row: any) => {
  currentWarehouse.value = row
  try {
    const response = await getWarehouseInventory(row.id)
    inventoryList.value = response.data || []
    inventoryVisible.value = true
  } catch (error) {
    ElMessage.error('获取库存数据失败')
  }
}

// 重置表单
const resetForm = () => {
  warehouseForm.id = 0
  warehouseForm.name = ''
  warehouseForm.address = ''
  warehouseForm.capacity = 1000
  warehouseForm.usedCapacity = 0
  warehouseForm.managerName = ''
  warehouseForm.phone = ''
  warehouseForm.status = 1
}

// 提交表单
const submitForm = async () => {
  if (!warehouseFormRef.value) return

  await warehouseFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 提交数据
        if (isEdit.value) {
          await updateWarehouse(warehouseForm.id.toString(), warehouseForm)
        } else {
          await createWarehouse(warehouseForm)
        }

        ElMessage({
          type: 'success',
          message: isEdit.value ? '修改成功' : '添加成功'
        })
        dialogVisible.value = false
        fetchWarehouseList()
      } catch (error) {
        ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
      }
    }
  })
}

// 页面加载时获取仓库列表
onMounted(() => {
  fetchWarehouseList()
})
</script>

<style scoped>
.warehouse-container {
  padding: 20px 0;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 320px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.inventory-header {
  display: flex;
  flex-direction: column;
}

.inventory-header h3 {
  margin: 0 0 10px 0;
}

.capacity-info {
  font-size: 14px;
  color: #606266;
}
</style> 