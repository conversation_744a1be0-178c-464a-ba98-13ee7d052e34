<template>
  <div class="dashboard-container">
    <div class="dashboard-title">
      <h1>系统概览</h1>
      <span class="date">{{ currentDate }}</span>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>用户总数</span>
              <el-icon><User /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.userCount }}</div>
            <div class="trend">
              <span class="label">较上周</span>
              <span class="value" :class="stats.userGrowth >= 0 ? 'up' : 'down'">
                {{ stats.userGrowth >= 0 ? '+' : '' }}{{ stats.userGrowth }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>仓库总数</span>
              <el-icon><House /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.warehouseCount }}</div>
            <div class="trend">
              <span class="label">较上周</span>
              <span class="value" :class="stats.warehouseGrowth >= 0 ? 'up' : 'down'">
                {{ stats.warehouseGrowth >= 0 ? '+' : '' }}{{ stats.warehouseGrowth }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>文章总数</span>
              <el-icon><Document /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.articleCount }}</div>
            <div class="trend">
              <span class="label">较上周</span>
              <span class="value" :class="stats.articleGrowth >= 0 ? 'up' : 'down'">
                {{ stats.articleGrowth >= 0 ? '+' : '' }}{{ stats.articleGrowth }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>商品总数</span>
              <el-icon><ShoppingCart /></el-icon>
            </div>
          </template>
          <div class="card-body">
            <div class="number">{{ stats.productCount }}</div>
            <div class="trend">
              <span class="label">较上周</span>
              <span class="value" :class="stats.productGrowth >= 0 ? 'up' : 'down'">
                {{ stats.productGrowth >= 0 ? '+' : '' }}{{ stats.productGrowth }}%
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <el-row :gutter="20" class="recent-section">
      <el-col :span="16">
        <el-card shadow="hover" class="recent-card">
          <template #header>
            <div class="card-header">
              <span>最近更新文章</span>
              <el-button type="primary" size="small" text @click="goToArticles">查看所有</el-button>
            </div>
          </template>
          <el-table :data="recentArticles" stripe style="width: 100%">
            <el-table-column prop="title" label="标题" min-width="250" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column prop="createTime" label="创建时间" width="180" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="recent-card">
          <template #header>
            <div class="card-header">
              <span>系统消息</span>
              <el-button type="primary" size="small" text>全部已读</el-button>
            </div>
          </template>
          <div class="notification-list">
            <div v-for="(notice, index) in notifications" :key="index" class="notification-item">
              <div class="notice-title">{{ notice.title }}</div>
              <div class="notice-time">{{ notice.time }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { User, House, Document, ShoppingCart } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getDashboardStats, getRecentArticles, getNotifications } from '@/api/dashboard'

const router = useRouter()

// 响应式数据
const stats = ref({
  userCount: 0,
  userGrowth: 0,
  warehouseCount: 0,
  warehouseGrowth: 0,
  articleCount: 0,
  articleGrowth: 0,
  productCount: 0,
  productGrowth: 0
})

const recentArticles = ref([])
const notifications = ref([])

// 获取当前日期
const currentDate = ref(new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
}))

// 跳转到文章列表
const goToArticles = () => {
  router.push('/dashboard/article')
}

// 获取仪表板统计数据
const fetchDashboardStats = async () => {
  try {
    const response = await getDashboardStats()
    stats.value = response.data
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

// 获取最近文章
const fetchRecentArticles = async () => {
  try {
    const response = await getRecentArticles()
    recentArticles.value = response.data
  } catch (error) {
    ElMessage.error('获取最近文章失败')
  }
}

// 获取通知
const fetchNotifications = async () => {
  try {
    const response = await getNotifications()
    notifications.value = response.data
  } catch (error) {
    ElMessage.error('获取通知失败')
  }
}

onMounted(() => {
  fetchDashboardStats()
  fetchRecentArticles()
  fetchNotifications()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px 0;
}

.dashboard-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-title h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.date {
  color: #909399;
  font-size: 14px;
}

.statistics {
  margin-bottom: 20px;
}

.stat-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 10px 0;
}

.number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.trend {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.label {
  color: #909399;
  font-size: 12px;
  margin-right: 8px;
}

.value {
  font-size: 14px;
  font-weight: 500;
}

.value.up {
  color: #67c23a;
}

.value.down {
  color: #f56c6c;
}

.recent-section {
  margin-bottom: 20px;
}

.recent-card {
  height: 100%;
}

.notification-list {
  max-height: 290px;
  overflow-y: auto;
}

.notification-item {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.notification-item:last-child {
  border-bottom: none;
}

.notice-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}

.notice-time {
  font-size: 12px;
  color: #909399;
}
</style> 