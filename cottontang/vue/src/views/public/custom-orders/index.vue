<template>
  <div class="custom-orders-page">
    <div class="container">
      <div class="page-header">
        <h1>个性需求</h1>
        <el-button type="primary" @click="showCreateDialog = true">创建需求</el-button>
      </div>

      <!-- 需求列表 -->
      <div class="orders-list">
        <div v-for="(order, index) in orders" :key="index" class="order-item">
          <div class="order-content">
            <div class="order-header">
              <span class="order-no">{{ order.dingzhiNo }}</span>
              <span class="order-status" :class="getStatusClass(order.status)">
                {{ getStatusText(order.status) }}
              </span>
            </div>
            <div class="order-details">
              <div class="detail-row">
                <span class="label">类型：</span>
                <span class="value">{{ order.leixing }}</span>
              </div>
              <div class="detail-row">
                <span class="label">批号：</span>
                <span class="value">{{ order.pihaoKunkao }}</span>
              </div>
              <div class="detail-row">
                <span class="label">颜色级：</span>
                <span class="value">{{ order.yansejiPinji }}</span>
              </div>
              <div class="detail-row">
                <span class="label">马值：</span>
                <span class="value">{{ order.mazhi }}</span>
              </div>
              <div class="detail-row">
                <span class="label">长度：</span>
                <span class="value">{{ order.changdu }}</span>
              </div>
              <div class="detail-row">
                <span class="label">强力：</span>
                <span class="value">{{ order.qiangli }}</span>
              </div>
            </div>
          </div>
          <div class="order-actions">
            <el-button size="small" @click="editOrder(order)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteOrder(order.id)">删除</el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingOrder ? '编辑需求' : '创建需求'"
      width="600px"
    >
      <el-form :model="orderForm" label-width="100px">
        <el-form-item label="类型">
          <el-input v-model="orderForm.leixing" />
        </el-form-item>
        <el-form-item label="批号">
          <el-input v-model="orderForm.pihaoKunkao" />
        </el-form-item>
        <el-form-item label="颜色级">
          <el-input v-model="orderForm.yansejiPinji" />
        </el-form-item>
        <el-form-item label="马值">
          <el-input v-model="orderForm.mazhi" />
        </el-form-item>
        <el-form-item label="长度">
          <el-input v-model="orderForm.changdu" />
        </el-form-item>
        <el-form-item label="强力">
          <el-input v-model="orderForm.qiangli" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveOrder">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCustomRequirements, deleteCustomRequirement } from '@/api/public'

// 响应式数据
const orders = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showCreateDialog = ref(false)
const editingOrder = ref<any>(null)
const orderForm = ref({
  leixing: '',
  pihaoKunkao: '',
  yansejiPinji: '',
  mazhi: '',
  changdu: '',
  qiangli: ''
})

// 获取订单列表
const fetchOrders = async () => {
  try {
    const response = await getCustomRequirements()
    orders.value = response.data || []
    total.value = orders.value.length
  } catch (error) {
    ElMessage.error('获取需求列表失败')
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: { [key: number]: string } = {
    0: '待处理',
    1: '处理中',
    2: '已完成',
    3: '已取消'
  }
  return statusMap[status] || '未知'
}

// 获取状态样式
const getStatusClass = (status: number) => {
  const classMap: { [key: number]: string } = {
    0: 'status-pending',
    1: 'status-processing',
    2: 'status-completed',
    3: 'status-cancelled'
  }
  return classMap[status] || ''
}

// 编辑订单
const editOrder = (order: any) => {
  editingOrder.value = order
  orderForm.value = { ...order }
  showCreateDialog.value = true
}

// 删除订单
const deleteOrder = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认删除这个需求吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteCustomRequirement(id)
    ElMessage.success('删除成功')
    fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 保存订单
const saveOrder = () => {
  // TODO: 实现保存逻辑
  ElMessage.success('保存成功')
  showCreateDialog.value = false
  fetchOrders()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchOrders()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchOrders()
}

// 页面加载时获取数据
onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.custom-orders-page {
  padding: 20px 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.orders-list {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.order-content {
  flex: 1;
}

.order-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.order-no {
  font-weight: bold;
  color: #409EFF;
  margin-right: 15px;
}

.order-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background-color: #f0f9ff;
  color: #1890ff;
}

.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-cancelled {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.order-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.detail-row {
  display: flex;
}

.label {
  color: #666;
  margin-right: 5px;
}

.value {
  color: #333;
  font-weight: 500;
}

.order-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .order-item {
    flex-direction: column;
  }
  
  .order-actions {
    flex-direction: row;
    margin-top: 15px;
  }
  
  .order-details {
    grid-template-columns: 1fr;
  }
}
</style>
