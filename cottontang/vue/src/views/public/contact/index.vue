<template>
  <div class="contact-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <h1>联系我们</h1>
        <p>我们期待与您的合作，随时为您提供专业服务</p>
      </div>
    </div>

    <!-- 联系方式 -->
    <div class="contact-info-section">
      <div class="container">
        <div class="contact-grid">
          <div class="contact-card">
            <div class="contact-icon">
              <i class="el-icon-location-outline"></i>
            </div>
            <h3>公司地址</h3>
            <p>新疆乌鲁木齐市经济技术开发区</p>
            <p>棉花贸易大厦18楼</p>
          </div>

          <div class="contact-card">
            <div class="contact-icon">
              <i class="el-icon-phone-outline"></i>
            </div>
            <h3>联系电话</h3>
            <p>总机：************</p>
            <p>销售：0991-1234567</p>
          </div>

          <div class="contact-card">
            <div class="contact-icon">
              <i class="el-icon-message"></i>
            </div>
            <h3>邮箱地址</h3>
            <p>商务合作：<EMAIL></p>
            <p>客户服务：<EMAIL></p>
          </div>

          <div class="contact-card">
            <div class="contact-icon">
              <i class="el-icon-time"></i>
            </div>
            <h3>工作时间</h3>
            <p>周一至周五：9:00-18:00</p>
            <p>周六：9:00-12:00</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系表单 -->
    <div class="contact-form-section">
      <div class="container">
        <div class="form-container">
          <div class="form-header">
            <h2>在线留言</h2>
            <p>请填写以下信息，我们会尽快与您联系</p>
          </div>

          <el-form
            ref="contactFormRef"
            :model="contactForm"
            :rules="formRules"
            label-width="100px"
            class="contact-form"
          >
            <div class="form-row">
              <el-form-item label="姓名" prop="name" class="form-item-half">
                <el-input v-model="contactForm.name" placeholder="请输入您的姓名" />
              </el-form-item>

              <el-form-item label="公司" prop="company" class="form-item-half">
                <el-input v-model="contactForm.company" placeholder="请输入公司名称" />
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="电话" prop="phone" class="form-item-half">
                <el-input v-model="contactForm.phone" placeholder="请输入联系电话" />
              </el-form-item>

              <el-form-item label="邮箱" prop="email" class="form-item-half">
                <el-input v-model="contactForm.email" placeholder="请输入邮箱地址" />
              </el-form-item>
            </div>

            <el-form-item label="咨询类型" prop="type">
              <el-select v-model="contactForm.type" placeholder="请选择咨询类型" style="width: 100%">
                <el-option label="产品咨询" value="product" />
                <el-option label="价格咨询" value="price" />
                <el-option label="合作洽谈" value="cooperation" />
                <el-option label="售后服务" value="service" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>

            <el-form-item label="留言内容" prop="message">
              <el-input
                v-model="contactForm.message"
                type="textarea"
                :rows="5"
                placeholder="请详细描述您的需求或问题"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                @click="submitForm"
                :loading="submitting"
                class="submit-btn"
              >
                提交留言
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-section">
      <div class="container">
        <h2>公司位置</h2>
        <div class="map-container">
          <div class="map-placeholder">
            <p>地图加载中...</p>
            <p>地址：新疆乌鲁木齐市经济技术开发区棉花贸易大厦18楼</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { submitContactForm } from '@/api/public'

// 页面元数据
document.title = '联系我们 - 棉花棠'

// 表单引用
const contactFormRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const contactForm = reactive({
  name: '',
  company: '',
  phone: '',
  email: '',
  type: '',
  message: ''
})

// 表单验证规则
const formRules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择咨询类型', trigger: 'change' }
  ],
  message: [
    { required: true, message: '请输入留言内容', trigger: 'blur' },
    { min: 10, message: '留言内容至少10个字符', trigger: 'blur' }
  ]
})

// 提交表单
const submitForm = async () => {
  if (!contactFormRef.value) return

  await contactFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      try {
        await submitContactForm(contactForm)
        ElMessage({
          type: 'success',
          message: '留言提交成功，我们会尽快与您联系！'
        })

        // 重置表单
        contactFormRef.value?.resetFields()
      } catch (error) {
        ElMessage.error('留言提交失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }
  })
}
</script>

<style scoped>
.contact-page {
  min-height: 100vh;
}

.container {
  width: 1200px;
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.page-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.contact-info-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.contact-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-5px);
}

.contact-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.contact-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #333;
}

.contact-card p {
  color: #666;
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.contact-form-section {
  padding: 80px 0;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 3rem;
}

.form-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.form-header p {
  font-size: 1.1rem;
  color: #666;
}

.contact-form {
  background: white;
  padding: 3rem;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.form-item-half {
  margin-bottom: 1.5rem;
}

.submit-btn {
  width: 200px;
  height: 50px;
  font-size: 1.1rem;
}

.map-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.map-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.map-container {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.map-placeholder {
  height: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #666;
}

.map-placeholder p {
  margin: 0.5rem 0;
}

@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .contact-form {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .form-header h2 {
    font-size: 2rem;
  }
}
</style>
