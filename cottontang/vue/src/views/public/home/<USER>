<template>
  <div class="bg_f2" style="padding-bottom: 30px">
    <!-- 每日精选 - 完全按照PHP原版 -->
    <div class="m_auto">
      <div class="bar">每日精选</div>
      <div style="overflow-x: auto;">
        <table class="chaoshi_tbl f_16 clr_3 w_100p mt_10 bg_f">
          <thead>
          <tr style="background: #28afe5;text-align: center;" class="clr_f">
            <th>批号</th>
            <th>类型</th>
            <th>颜色级</th>
            <th>马值</th>
            <th>长度</th>
            <th>强力</th>
            <th>含杂</th>
            <th>回潮</th>
            <th>公重</th>
            <th>整齐度</th>
            <th>加工厂</th>
            <th>仓库</th>
            <th>基差</th>
            <th>点价合约</th>
          </tr>
          </thead> 
          <tr v-for="v in chaoshi" :key="v.id" style="text-align:center;">
            <td>
              <!--
                <div class="dis_ib mj1 f_14 ver_mid">
                  <div class="h1">5星</div>
                  <div class="h2">卖家</div>
                </div>
              -->
              <div class="dis_ib ver_mid">
                <a @click="viewProduct(v.id)" class="clr_b f_wei">{{ v.pihaoKunkao }}</a>
              </div>
            </td>
            <td>{{ v.leixing }}</td>
            <td>
              <div :title="v.yanseji_pinji">{{ msubstr2(v.yansejiPinji, 0, 6) }}</div>
            </td>
            <td>{{ v.mazhi }}</td>
            <td>{{ v.changdu }}</td>
            <td>{{ v.qiangli }}</td>
            <td>{{ v.hanzalv }}</td>
            <td>{{ v.huichaolv }}</td>
            <td>{{ v.gongzhong }}</td>
            <td>{{ v.zhengqidu }}</td>
            <td>
              <div :title="v.jiagongchang">{{ msubstr2(v.jiagongchang, 0, 6) }}</div>
            </td>
            <td>
              <div :title="v.cangchumingcheng">{{ msubstr2(v.cangchumingcheng, 0, 6) }}</div>
            </td>
            <td>{{ v.jicha }}</td>
            <td>{{ v.dianjiaheyue }}</td>
          </tr>
        </table>
      </div>
    </div>

    <!-- 轮播图 - 使用Vue实现的轮播 -->
    <div class="banner-container" v-if="bann.length > 0">
      <div class="banner-list" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
        <div
          v-for="(v, index) in bann"
          :key="v.id || index"
          class="banner-item"
          :style="{ backgroundImage: `url(${getImageUrl(v.thumbs)})` }"
        >
          <a v-if="v.url" :href="v.url" target="_blank" class="banner-link"></a>
        </div>
      </div>

      <!-- 指示点 -->
      <div class="banner-dots" v-if="bann.length > 1">
        <span
          v-for="(item, index) in bann"
          :key="index"
          :class="['banner-dot', { active: index === currentSlide }]"
          @click="goToSlide(index)"
        ></span>
      </div>

      <!-- 左右箭头 -->
      <button class="banner-prev" @click="prevSlide" v-if="bann.length > 1">‹</button>
      <button class="banner-next" @click="nextSlide" v-if="bann.length > 1">›</button>
    </div>

    <!-- 个性需求 - 完全按照PHP原版 -->
    <div class="m_auto">
      <div class="bar">个性需求</div>
      <!-- <a href="{:U('Center/dingzhi')}" class="mt_10 dis_b"><img src="__PUBLIC__/images/xq.jpg" class="dis_b" /></a> -->
      <div v-for="v in dingzhi" :key="v.id" class="border1 bg_f pad_20 f_16 clr_3 clearfix mt_10">
        <div class="fl dz_left" style="width:960px;">
          <!-- {$v.url|urldecode=###} -->
          <a href="#" class="clr_b f_wei mr_10">{{ v.dingzhiNo }}</a>
          <span class="clr_9 mr_10">类型：<span class="f_wei clr_3">{{ v.leixing }}</span></span>
          <span class="clr_9 mr_10">年度 ：<span class="clr_3">{{ v.pihaoKunkao }}</span></span>
          <div class="mt_10 lh_30">
            <span class="clr_9 mr_10">长 度：<span class="clr_3">{{ v.changdu }}</span></span>
            <span class="clr_9 mr_10">马值 ：<span class="clr_3">{{ v.mazhi }}</span></span>
            <span class="clr_9 mr_10">强力 ：<span class="clr_3">{{ v.qiangli }}</span></span>
            <span class="clr_9 mr_10">回潮 ：<span class="clr_3">{{ v.huichaolv }}</span></span>
            <span class="clr_9 mr_10">整齐度：<span class="clr_3">{{ v.zhengqidu }}</span></span>
            <span class="clr_9 mr_10">含杂：<span class="clr_3">{{ v.hanzalv }}</span></span><br />
            <span class="clr_9 mr_10">白棉1/2/3级 ：<span class="clr_3">{{ v.mazhi }}</span></span>
            <span class="clr_9 mr_10">白棉4/5级 ：<span class="clr_3">{{ v.qiangli }}</span></span>
            <span class="clr_9 mr_10">淡点污1/2/3级 ：<span class="clr_3">{{ v.huichaolv }}</span></span>
            <span class="clr_9 mr_10">淡黄染1/2/3级：<span class="clr_3">{{ v.zhengqidu }}</span></span>
            <span class="clr_9 mr_10">黄染1/2级：<span class="clr_3">{{ v.hanzalv }}</span></span><br />
            <span class="clr_9 mr_10">轧花厂： <span class="clr_3">{{ v.jiagongchang }}</span></span>
            <span class="clr_9 mr_10">交货仓库：<span class="clr_3">{{ v.cangchumingcheng }}</span></span><br />
          </div>
        </div>
        <div class="fr dz_right clearfix" style="width:160px;">
          <div style="text-align: right;">
            <!-- <a href="#" class="edit_btn f_14 dis_b">修改</a> -->
            <a @click="delDingzhi(v.id)" class="del_btn f_14 dis_ib mt_10" onclick="return confirm('确认删除？');">删除</a><br />
            <!-- <div class="f_16 clr_9">平台最优价格 <span class="f_wei clr_ora f_22">15700</span></div> -->
            <a :href="v.url" class="f_16 zy_box clr_ora pos_rela mt_10 dis_ib">
              <!-- <div class="icon2">0</div> -->
              <img src="/images/icon.png" class="ver_mid" /> <span class="ver_mid">查看资源</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

import { getHomepageData, getBanners, getCustomRequirements, deleteCustomRequirement } from '@/api/public'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据 - 完全按照PHP原版变量名
const chaoshi = ref<any[]>([])  // 每日精选数据
const bann = ref<any[]>([])     // 轮播图数据 (PHP原版用的是bann)
const dingzhi = ref<any[]>([])  // 个性需求数据

// 轮播图相关
const currentSlide = ref(0)
let slideTimer: NodeJS.Timeout | null = null

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 字符串截取函数 - 完全按照PHP原版
const msubstr2 = (str: string, start: number, length: number) => {
  if (!str) return ''
  return str.length > length ? str.substring(start, length) + '...' : str
}

// 获取图片URL
const getImageUrl = (thumbs: string) => {
  if (!thumbs) return '/images/default-banner.jpg'
  return thumbs.startsWith('http') ? thumbs : `/api/files/${thumbs}`
}

// 轮播图相关方法
const goToSlide = (index: number) => {
  currentSlide.value = index
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % bann.value.length
}

const prevSlide = () => {
  currentSlide.value = currentSlide.value === 0 ? bann.value.length - 1 : currentSlide.value - 1
}

const startAutoSlide = () => {
  if (bann.value.length > 1) {
    slideTimer = setInterval(() => {
      nextSlide()
    }, 5000)
  }
}

const stopAutoSlide = () => {
  if (slideTimer) {
    clearInterval(slideTimer)
    slideTimer = null
  }
}

// 获取首页数据 - 完全按照PHP原版
const fetchHomepageData = async () => {
  try {
    const response = await getHomepageData()
    chaoshi.value = response.data || []
  } catch (error) {
    ElMessage.error('获取每日精选数据失败')
  }
}

// 获取轮播图数据
const fetchBanners = async () => {
  try {
    const response = await getBanners()
    bann.value = response.data?.records || []

    // 启动自动轮播
    if (bann.value.length > 0) {
      startAutoSlide()
    }
  } catch (error) {
    ElMessage.error('获取轮播图数据失败')
  }
}

// 获取个性需求数据
const fetchCustomRequirements = async () => {
  if (!isLoggedIn.value) return

  try {
    const response = await getCustomRequirements()
    dingzhi.value = response.data || []
  } catch (error) {
    ElMessage.error('获取个性需求数据失败')
  }
}

// 查看产品详情 - 完全按照PHP原版
const viewProduct = (id: number) => {
  router.push(`/public/products/${id}`)
}

// 删除个性需求 - 完全按照PHP原版方法名
const delDingzhi = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认删除？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteCustomRequirement(id)
    ElMessage.success('删除成功')
    fetchCustomRequirements() // 重新获取数据
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchHomepageData()
  fetchBanners()
  if (isLoggedIn.value) {
    fetchCustomRequirements()
  }
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
/* 完全按照PHP原版样式 */

/* 表格样式 - 补充全局CSS中可能缺失的样式 */
.chaoshi_tbl {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ddd;
}

.chaoshi_tbl th {
  background: #28afe5 !important;
  color: #fff !important;
  font-weight: bold;
}

.chaoshi_tbl tr:nth-child(even) {
  background-color: #f9f9f9;
}

.chaoshi_tbl tr:hover {
  background-color: #f5f5f5;
}

/* 轮播图样式 - 使用简单的轮播而不是Swiper */
.banner-container {
  width: 100%;
  max-width: 1200px;
  margin: 20px auto;
  position: relative;
  height: 450px;
  overflow: hidden;
}

.banner-list {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.banner-item {
  min-width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.banner-link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
}

.banner-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 3;
}

.banner-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s;
}

.banner-dot.active {
  background: #28afe5;
}

.banner-prev,
.banner-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
  transition: background 0.3s;
}

.banner-prev:hover,
.banner-next:hover {
  background: rgba(0, 0, 0, 0.7);
}

.banner-prev {
  left: 20px;
}

.banner-next {
  right: 20px;
}

/* 个性需求样式 */
.dz_left {
  float: left;
}

.dz_right {
  float: right;
}

.del_btn {
  color: #ff0000;
  cursor: pointer;
  text-decoration: none;
}

.del_btn:hover {
  text-decoration: underline;
}

.zy_box {
  display: inline-block;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border-radius: 3px;
  text-decoration: none;
}

.zy_box:hover {
  background-color: #e0e0e0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chaoshi_tbl {
    font-size: 12px;
  }

  .chaoshi_tbl th,
  .chaoshi_tbl td {
    padding: 6px 4px;
  }

  .banner-container {
    height: 250px;
    margin: 10px;
  }

  .banner-prev,
  .banner-next {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .dz_left,
  .dz_right {
    float: none !important;
    width: 100% !important;
  }

  .dz_right {
    margin-top: 15px;
    text-align: left !important;
  }
}
</style>
