<template>
  <div class="bg_f2">
    <!-- 棉花商城 - 完全按照PHP原版 -->
    <div class="m_auto">
      <div class="bar">棉花商城</div>
      <div class="border1 pad_20 bg_f mt_10">
        <!-- 主分类导航 - 完全按照PHP原版 -->
        <ul class="mian_ul clearfix al_ct f_18">
          <li v-for="item in chandi" :key="item.id">
            <a 
              href="#" 
              @click.prevent="selectMainCategory(item.id)" 
              :class="currentDef === item.id ? 'clr_3 mian_cur' : 'clr_3 mian_norm'"
            >
              {{ item.name }}
            </a>
          </li>
        </ul>

        <!-- 产地筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">
          <li style="display: inline-block;" class="ver_top">产　地：</li>
          <li style="display: inline-block;">
            <div id="chandi_click">
              <a 
                v-for="item in chandiDefault" 
                :key="item.id"
                href="#."
                @click.prevent="toggleParentChandi(item)"
                :class="['clr_3', 'mr_10', selectedParentChandi.includes(item.id) ? 'xuan_cur' : '']"
                :myid="item.id"
              >
                {{ item.name }}
              </a>
            </div>
          </li>
        </ul>

        <!-- 类型筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px; margin-left:5px;width:375px;">
          <li>类　型：</li>
          <li v-for="item in xlsLeixing" :key="item">
            <a 
              href="#."
              @click.prevent="toggleLeixing(item)"
              :class="['clr_3', selectedLeixing.includes(item) ? 'xuan_cur' : '']"
            >
              {{ item }}
            </a>
          </li>
        </ul>

        <!-- 年度筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px; margin-left:5px; width:330px;">
          <li>年　度：</li>
          <li v-for="item in xlsNiandu" :key="item">
            <a 
              href="#."
              @click.prevent="toggleNiandu(item)"
              :class="['clr_3', selectedNiandu.includes(item) ? 'xuan_cur' : '']"
            >
              {{ item }}
            </a>
          </li>
        </ul>

        <div class="cls"></div>

        <!-- 子产地容器 - 完全按照PHP原版 -->
        <div id="child_wraper">
          <span v-for="parent in selectedParentChandiData" :key="parent.id" :id="`child_${parent.id}`">
            <a 
              v-for="child in parent.children" 
              :key="child.id"
              href="#."
              @click.prevent="toggleChildChandi(parent.id, child)"
              :class="['clr_3', 'mr_10', 'f_14', isChildSelected(parent.id, child.id) ? 'xuan_cur' : '']"
              :pid="parent.id"
              :myid="child.id"
            >
              {{ child.name }}
            </a>
          </span>
        </div>

        <!-- 范围滑块筛选 - 完全按照PHP原版 -->
        <div class="cls"></div>

        <!-- 长度筛选 -->
        <ul class="chandi_ul f_14 fl hang1" style="width:380px;">
          <li class="length">长　度</li>
          <li style="width:240px;">
            <div class="vue-slider-container">
              <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
              <div class="vue-slider-min" :class="{ show: shouldShowMinMax('changdu', 25, 32).showMin }">25</div>
              <div class="vue-slider-max" :class="{ show: shouldShowMinMax('changdu', 25, 32).showMax }">32</div>
              
              <!-- 轨道 -->
              <div class="vue-slider-track" @click="handleSliderClick($event, 'changdu', 25, 32)">
                <div
                  class="vue-slider-bar"
                  :style="getSliderBarStyle('changdu', 25, 32)"
                ></div>
                
                <!-- 左手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-left"
                  :style="getSliderHandleStyle('changdu', 25, 32, 'left')"
                  @mousedown="startDrag($event, 'changdu', 'left', 25, 32)"
                  @touchstart="startDrag($event, 'changdu', 'left', 25, 32)"
                >
                  <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.changdu[0]) }}</div>
                </div>
                
                <!-- 右手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-right"
                  :style="getSliderHandleStyle('changdu', 25, 32, 'right')"
                  @mousedown="startDrag($event, 'changdu', 'right', 25, 32)"
                  @touchstart="startDrag($event, 'changdu', 'right', 25, 32)"
                >
                  <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.changdu[1]) }}</div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!-- 强力筛选 -->
        <ul class="chandi_ul f_14 fl hang1" style="width:380px;">
          <li class="length">强　力</li>
          <li style="width:240px;">
            <div class="vue-slider-container">
              <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
              <div class="vue-slider-min" :class="{ show: shouldShowMinMax('qiangli', 25, 32).showMin }">25</div>
              <div class="vue-slider-max" :class="{ show: shouldShowMinMax('qiangli', 25, 32).showMax }">32</div>
              
              <!-- 轨道 -->
              <div class="vue-slider-track" @click="handleSliderClick($event, 'qiangli', 25, 32)">
                <div
                  class="vue-slider-bar"
                  :style="getSliderBarStyle('qiangli', 25, 32)"
                ></div>
                
                <!-- 左手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-left"
                  :style="getSliderHandleStyle('qiangli', 25, 32, 'left')"
                  @mousedown="startDrag($event, 'qiangli', 'left', 25, 32)"
                  @touchstart="startDrag($event, 'qiangli', 'left', 25, 32)"
                >
                  <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.qiangli[0]) }}</div>
                </div>
                
                <!-- 右手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-right"
                  :style="getSliderHandleStyle('qiangli', 25, 32, 'right')"
                  @mousedown="startDrag($event, 'qiangli', 'right', 25, 32)"
                  @touchstart="startDrag($event, 'qiangli', 'right', 25, 32)"
                >
                  <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.qiangli[1]) }}</div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!-- 马值筛选 -->
        <ul class="chandi_ul f_14 fl hang1" style="width:380px;">
          <li class="length">马　值</li>
          <li style="width:240px;">
            <div class="vue-slider-container">
              <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
              <div class="vue-slider-min" :class="{ show: shouldShowMinMax('mazhi', 2.5, 5.5).showMin }">2.5</div>
              <div class="vue-slider-max" :class="{ show: shouldShowMinMax('mazhi', 2.5, 5.5).showMax }">5.5</div>
              
              <!-- 轨道 -->
              <div class="vue-slider-track" @click="handleSliderClick($event, 'mazhi', 2.5, 5.5)">
                <div
                  class="vue-slider-bar"
                  :style="getSliderBarStyle('mazhi', 2.5, 5.5)"
                ></div>
                
                <!-- 左手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-left"
                  :style="getSliderHandleStyle('mazhi', 2.5, 5.5, 'left')"
                  @mousedown="startDrag($event, 'mazhi', 'left', 2.5, 5.5)"
                  @touchstart="startDrag($event, 'mazhi', 'left', 2.5, 5.5)"
                >
                  <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.mazhi[0]) }}</div>
                </div>
                
                <!-- 右手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-right"
                  :style="getSliderHandleStyle('mazhi', 2.5, 5.5, 'right')"
                  @mousedown="startDrag($event, 'mazhi', 'right', 2.5, 5.5)"
                  @touchstart="startDrag($event, 'mazhi', 'right', 2.5, 5.5)"
                >
                  <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.mazhi[1]) }}</div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <div class="cls"></div>

        <!-- 回潮筛选 -->
        <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
          <li class="length">回　潮</li>
          <li style="width:240px;">
            <div class="vue-slider-container">
              <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
              <div class="vue-slider-min" :class="{ show: shouldShowMinMax('huichao', 0, 10).showMin }">0</div>
              <div class="vue-slider-max" :class="{ show: shouldShowMinMax('huichao', 0, 10).showMax }">10</div>
              
              <!-- 轨道 -->
              <div class="vue-slider-track" @click="handleSliderClick($event, 'huichao', 0, 10)">
                <div
                  class="vue-slider-bar"
                  :style="getSliderBarStyle('huichao', 0, 10)"
                ></div>
                
                <!-- 左手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-left"
                  :style="getSliderHandleStyle('huichao', 0, 10, 'left')"
                  @mousedown="startDrag($event, 'huichao', 'left', 0, 10)"
                  @touchstart="startDrag($event, 'huichao', 'left', 0, 10)"
                >
                  <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.huichao[0]) }}</div>
                </div>
                
                <!-- 右手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-right"
                  :style="getSliderHandleStyle('huichao', 0, 10, 'right')"
                  @mousedown="startDrag($event, 'huichao', 'right', 0, 10)"
                  @touchstart="startDrag($event, 'huichao', 'right', 0, 10)"
                >
                  <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.huichao[1]) }}</div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!-- 整齐度筛选 -->
        <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
          <li class="length">整齐度</li>
          <li style="width:240px;">
            <div class="vue-slider-container">
              <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
              <div class="vue-slider-min" :class="{ show: shouldShowMinMax('zhengqidu', 77, 90).showMin }">77</div>
              <div class="vue-slider-max" :class="{ show: shouldShowMinMax('zhengqidu', 77, 90).showMax }">90</div>
              
              <!-- 轨道 -->
              <div class="vue-slider-track" @click="handleSliderClick($event, 'zhengqidu', 77, 90)">
                <div
                  class="vue-slider-bar"
                  :style="getSliderBarStyle('zhengqidu', 77, 90)"
                ></div>
                
                <!-- 左手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-left"
                  :style="getSliderHandleStyle('zhengqidu', 77, 90, 'left')"
                  @mousedown="startDrag($event, 'zhengqidu', 'left', 77, 90)"
                  @touchstart="startDrag($event, 'zhengqidu', 'left', 77, 90)"
                >
                  <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.zhengqidu[0]) }}</div>
                </div>
                
                <!-- 右手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-right"
                  :style="getSliderHandleStyle('zhengqidu', 77, 90, 'right')"
                  @mousedown="startDrag($event, 'zhengqidu', 'right', 77, 90)"
                  @touchstart="startDrag($event, 'zhengqidu', 'right', 77, 90)"
                >
                  <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.zhengqidu[1]) }}</div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <!-- 含杂筛选 -->
        <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
          <li class="length">含　杂</li>
          <li style="width:240px;">
            <div class="vue-slider-container">
              <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
              <div class="vue-slider-min" :class="{ show: shouldShowMinMax('hanza', 0, 6).showMin }">0</div>
              <div class="vue-slider-max" :class="{ show: shouldShowMinMax('hanza', 0, 6).showMax }">6</div>
              
              <!-- 轨道 -->
              <div class="vue-slider-track" @click="handleSliderClick($event, 'hanza', 0, 6)">
                <div
                  class="vue-slider-bar"
                  :style="getSliderBarStyle('hanza', 0, 6)"
                ></div>
                
                <!-- 左手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-left"
                  :style="getSliderHandleStyle('hanza', 0, 6, 'left')"
                  @mousedown="startDrag($event, 'hanza', 'left', 0, 6)"
                  @touchstart="startDrag($event, 'hanza', 'left', 0, 6)"
                >
                  <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.hanza[0]) }}</div>
                </div>
                
                <!-- 右手柄和其选中值 -->
                <div
                  class="vue-slider-handle vue-slider-handle-right"
                  :style="getSliderHandleStyle('hanza', 0, 6, 'right')"
                  @mousedown="startDrag($event, 'hanza', 'right', 0, 6)"
                  @touchstart="startDrag($event, 'hanza', 'right', 0, 6)"
                >
                  <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.hanza[1]) }}</div>
                </div>
              </div>
            </div>
          </li>
        </ul>

        <div class="cls"></div>

        <!-- 颜色级筛选区域 - 完全按照PHP原版 -->
        <div class="bottom">
          <!-- 白棉1/2/3级筛选 -->
          <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
            <li class="green length">白棉1/2/3级</li>
            <li style="width:240px;">
              <div class="vue-slider-container">
                <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
                <div class="vue-slider-min" :class="{ show: shouldShowMinMax('bm123', 0, 100).showMin }">0</div>
                <div class="vue-slider-max" :class="{ show: shouldShowMinMax('bm123', 0, 100).showMax }">100</div>
                
                <!-- 轨道 -->
                <div class="vue-slider-track" @click="handleSliderClick($event, 'bm123', 0, 100)">
                  <div
                    class="vue-slider-bar"
                    :style="getSliderBarStyle('bm123', 0, 100)"
                  ></div>
                  
                  <!-- 左手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-left"
                    :style="getSliderHandleStyle('bm123', 0, 100, 'left')"
                    @mousedown="startDrag($event, 'bm123', 'left', 0, 100)"
                    @touchstart="startDrag($event, 'bm123', 'left', 0, 100)"
                  >
                    <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.bm123[0]) }}</div>
                  </div>
                  
                  <!-- 右手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-right"
                    :style="getSliderHandleStyle('bm123', 0, 100, 'right')"
                    @mousedown="startDrag($event, 'bm123', 'right', 0, 100)"
                    @touchstart="startDrag($event, 'bm123', 'right', 0, 100)"
                  >
                    <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.bm123[1]) }}</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <!-- 白棉4/5级筛选 -->
          <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
            <li class="green length">白棉4/5级</li>
            <li style="width:240px;">
              <div class="vue-slider-container">
                <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
                <div class="vue-slider-min" :class="{ show: shouldShowMinMax('bm45', 0, 100).showMin }">0</div>
                <div class="vue-slider-max" :class="{ show: shouldShowMinMax('bm45', 0, 100).showMax }">100</div>
                
                <!-- 轨道 -->
                <div class="vue-slider-track" @click="handleSliderClick($event, 'bm45', 0, 100)">
                  <div
                    class="vue-slider-bar"
                    :style="getSliderBarStyle('bm45', 0, 100)"
                  ></div>
                  
                  <!-- 左手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-left"
                    :style="getSliderHandleStyle('bm45', 0, 100, 'left')"
                    @mousedown="startDrag($event, 'bm45', 'left', 0, 100)"
                    @touchstart="startDrag($event, 'bm45', 'left', 0, 100)"
                  >
                    <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.bm45[0]) }}</div>
                  </div>
                  
                  <!-- 右手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-right"
                    :style="getSliderHandleStyle('bm45', 0, 100, 'right')"
                    @mousedown="startDrag($event, 'bm45', 'right', 0, 100)"
                    @touchstart="startDrag($event, 'bm45', 'right', 0, 100)"
                  >
                    <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.bm45[1]) }}</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <!-- 淡点污1/2/3级筛选 -->
          <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
            <li class="green length">淡点污1/2/3级</li>
            <li style="width:240px;">
              <div class="vue-slider-container">
                <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
                <div class="vue-slider-min" :class="{ show: shouldShowMinMax('ddw123', 0, 100).showMin }">0</div>
                <div class="vue-slider-max" :class="{ show: shouldShowMinMax('ddw123', 0, 100).showMax }">100</div>
                
                <!-- 轨道 -->
                <div class="vue-slider-track" @click="handleSliderClick($event, 'ddw123', 0, 100)">
                  <div
                    class="vue-slider-bar"
                    :style="getSliderBarStyle('ddw123', 0, 100)"
                  ></div>
                  
                  <!-- 左手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-left"
                    :style="getSliderHandleStyle('ddw123', 0, 100, 'left')"
                    @mousedown="startDrag($event, 'ddw123', 'left', 0, 100)"
                    @touchstart="startDrag($event, 'ddw123', 'left', 0, 100)"
                  >
                    <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.ddw123[0]) }}</div>
                  </div>
                  
                  <!-- 右手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-right"
                    :style="getSliderHandleStyle('ddw123', 0, 100, 'right')"
                    @mousedown="startDrag($event, 'ddw123', 'right', 0, 100)"
                    @touchstart="startDrag($event, 'ddw123', 'right', 0, 100)"
                  >
                    <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.ddw123[1]) }}</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <div class="cls"></div>

          <!-- 淡黄染1/2/3级筛选 -->
          <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
            <li class="green length">淡黄染1/2/3级</li>
            <li style="width:240px;">
              <div class="vue-slider-container">
                <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
                <div class="vue-slider-min" :class="{ show: shouldShowMinMax('dhr123', 0, 100).showMin }">0</div>
                <div class="vue-slider-max" :class="{ show: shouldShowMinMax('dhr123', 0, 100).showMax }">100</div>
                
                <!-- 轨道 -->
                <div class="vue-slider-track" @click="handleSliderClick($event, 'dhr123', 0, 100)">
                  <div
                    class="vue-slider-bar"
                    :style="getSliderBarStyle('dhr123', 0, 100)"
                  ></div>
                  
                  <!-- 左手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-left"
                    :style="getSliderHandleStyle('dhr123', 0, 100, 'left')"
                    @mousedown="startDrag($event, 'dhr123', 'left', 0, 100)"
                    @touchstart="startDrag($event, 'dhr123', 'left', 0, 100)"
                  >
                    <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.dhr123[0]) }}</div>
                  </div>
                  
                  <!-- 右手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-right"
                    :style="getSliderHandleStyle('dhr123', 0, 100, 'right')"
                    @mousedown="startDrag($event, 'dhr123', 'right', 0, 100)"
                    @touchstart="startDrag($event, 'dhr123', 'right', 0, 100)"
                  >
                    <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.dhr123[1]) }}</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <!-- 黄染1/2级筛选 -->
          <ul class="chandi_ul f_14 fl hang2" style="width:380px;">
            <li class="green length">黄染1/2级</li>
            <li style="width:240px;">
              <div class="vue-slider-container">
                <!-- 默认值显示在轨道两端，根据选中值距离决定是否显示 -->
                <div class="vue-slider-min" :class="{ show: shouldShowMinMax('hr12', 0, 100).showMin }">0</div>
                <div class="vue-slider-max" :class="{ show: shouldShowMinMax('hr12', 0, 100).showMax }">100</div>
                
                <!-- 轨道 -->
                <div class="vue-slider-track" @click="handleSliderClick($event, 'hr12', 0, 100)">
                  <div
                    class="vue-slider-bar"
                    :style="getSliderBarStyle('hr12', 0, 100)"
                  ></div>
                  
                  <!-- 左手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-left"
                    :style="getSliderHandleStyle('hr12', 0, 100, 'left')"
                    @mousedown="startDrag($event, 'hr12', 'left', 0, 100)"
                    @touchstart="startDrag($event, 'hr12', 'left', 0, 100)"
                  >
                    <div class="vue-slider-from">{{ formatSliderValue(rangeFilters.hr12[0]) }}</div>
                  </div>
                  
                  <!-- 右手柄和其选中值 -->
                  <div
                    class="vue-slider-handle vue-slider-handle-right"
                    :style="getSliderHandleStyle('hr12', 0, 100, 'right')"
                    @mousedown="startDrag($event, 'hr12', 'right', 0, 100)"
                    @touchstart="startDrag($event, 'hr12', 'right', 0, 100)"
                  >
                    <div class="vue-slider-to">{{ formatSliderValue(rangeFilters.hr12[1]) }}</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <div class="cls"></div>
        </div>

        <!-- 交货地筛选 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_30" id="jiaohuodi_ul" style="width:760px;">
          <li>交货地：</li>
          <li>
            <div id="jhd_click">
              <a
                v-for="item in jiaohuodi"
                :key="item.id"
                href="#."
                @click.prevent="toggleParentJiaohuodi(item)"
                :class="['clr_3', 'mr_10', selectedParentJiaohuodi.includes(item.id) ? 'xuan_cur' : '']"
                :myid="item.id"
              >
                {{ item.name }}
              </a>
            </div>
          </li>
        </ul>

        <!-- 子交货地容器 - 完全按照PHP原版 -->
        <div id="jhd_wraper" class="mt_10">
          <span v-for="parent in selectedParentJiaohuodiData" :key="parent.id" :id="`jhd_${parent.id}`">
            <a
              v-for="child in parent.children"
              :key="child.id"
              href="#."
              @click.prevent="toggleChildJiaohuodi(parent.id, child)"
              :class="['clr_3', 'mr_10', 'f_14', isChildJiaohuodiSelected(parent.id, child.id) ? 'xuan_cur' : '']"
              :pid="parent.id"
              :myid="child.id"
            >
              {{ child.name }}
            </a>
          </span>
        </div>

        <!-- 交货仓库和轧花厂输入 - 完全按照PHP原版 -->
        <ul class="chandi_ul f_14 mt_10">
          <li>
            交货仓库：<input 
              class="border1 f_14 clr_9 pad_5" 
              placeholder="请输入仓库名称"
              v-model="searchForm.cangchumingcheng" 
              style="width:350px;" 
            />
            轧花厂：<input 
              class="border1 f_14 clr_9 pad_5" 
              placeholder="请输入轧花厂名称或编号"
              v-model="searchForm.jiagongchang"  
              style="width:350px;" 
            />
            <input
              type="button"
              @click="() => handleSearch(1)"
              value="✓ 确认筛选　"
              class="f_14 clr_f bg_lan border0 pad_5"
            />
            <a 
              href="#."
              @click.prevent="addToDingzhi" 
              style="font-weight: 700; color: #F1830E; padding-left: 10px; font-size: 15px;"
            >
              +添加到个性需求
            </a>
          </li>
        </ul>
      </div>

      <!-- 排序和导出区域 - 完全按照PHP原版 -->
      <div class="border1 bg_f mt_10 pad_10">
        <ul class="paixu_ul f_14 clearfix">
          <li class="clr_b f_wei">综合排序</li>
          <li style="margin-right: 15px;">
            <label class="mr_5 rad5" style="background: #e8e3df;padding:1px 5px 3px 5px;">
              <input type="checkbox" class="ver_mid" v-model="filters.hot" @change="() => handleSearch(1)" />
              <span class="ver_mid">热门</span>
            </label>
          </li>
          <li style="border:0;">
            <input 
              v-model="exportCount" 
              placeholder="导出条数" 
              value="300" 
              class="ver_mid border1" 
              style="width:60px;" 
            />
            <a href="#." @click.prevent="exportExcel">
              <img src="/images/excel.png" class="dis_ib ver_mid" /> 
              <span class="ver_mid" style="color:#217346;">导出报价</span>
            </a>
          </li>
          <div class="fr" style="border:1px solid #fff;">
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.xunjia" @change="handleHeyueFilter('询价')" />
                <span class="ver_mid">询价</span>
              </label>
            </li>
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.dabao" @change="handleHeyueFilter('打包')" />
                <span class="ver_mid">打包</span>
              </label>
            </li>
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.jicha" @change="handleHeyueFilter('jicha')" />
                <span class="ver_mid">基差点价</span>
              </label>
            </li>
            <li style="border:0;">
              <label class="mr_5">
                <input type="checkbox" class="ver_mid" v-model="filters.yikoujia" @change="handleHeyueFilter('一口价')" />
                <span class="ver_mid">一口价</span>
              </label>
              <span class="ver_mid">共计{{ total }}批</span>
            </li>
          </div>
        </ul>

        <!-- 导出遮罩层 - 完全按照PHP原版 -->
        <div class="mask_bg dis_n" id="mask_bg">
          <div class="bg_f pad_40" style="width:500px;margin:200px auto; border-radius: 10px;">
            <div id="mask_tips">
              <span class="ver_mid">正在导出数据，请耐心等待</span>
              <img src="/images/loading.gif" style="width:50px;" class="ver_mid" />
            </div>
          </div>
        </div>
      </div>

      <!-- 产品列表 - 完全按照PHP原版 -->
      <div v-for="product in products" :key="product.id" class="border1 bg_gr pad_10 f_14 clr_3 clearfix mt_10 pos_rela" style="margin:10px 0;">
        <div class="fl" style="width:830px; font-size:16px;">
          <div style="font-size:16px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
            <a href="#." @click.prevent="viewProduct(product.id)" class="clr_b f_wei">{{ product.pihaoKunkao }}</a>
            <span class="ml_10">{{ product.leixing }}</span>
            <span class="ml_10">{{ product.baoshu }}件 &nbsp;</span>
            <span class="clr_3">{{ product.diqu_text }}</span>
            <span class="clr_9">加工厂 <span class="clr_3">{{ product.jiagongchang }}</span></span>
          </div>
          <div class="lh_30 mt_5" :title="product.yanseji_pinji" style="font-size:16px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
            <span class="clr_9">颜色 <span class="clr_3">{{ product.yansejiPinji }}</span></span>
          </div>
          <div class="lh_30" style="font-size:16px;">
            <span class="clr_9">长度 <span class="clr_3">{{ product.changdu }}</span></span>
            <span class="clr_9">强力 <span class="clr_3">{{ product.qiangli }}</span></span>
            <span class="clr_9">马值 <span class="clr_3">{{ product.mazhi }}</span></span>
            <span class="clr_9">含杂<span class="clr_3">{{ product.hanzalv }}</span></span>
            <span class="clr_9">回潮 <span class="clr_3">{{ product.huichaolv }}</span></span>
            <span class="clr_9">整齐度<span class="clr_3">{{ product.zhengqidu }}</span></span>
            <span class="clr_9">公重<span class="clr_3">{{ product.gongzhong }}吨</span></span>
            <span class="clr_9">轧工质量<span class="clr_3">{{ product.ygzl }}</span></span><br />
          </div>
          <div class="mt_5">
            <span class="clr_9">仓储名称 <span class="clr_3">{{ product.cangchumingcheng }}</span></span>
            <span class="clr_9" style="margin:0 15px;">
              <span class="clr_3">{{ product.diqu_text_cangku }}</span>
            </span>
            <span class="clr_9">更新时间： <span class="clr_3">{{ formatDate(product.addTime) }}</span></span>
          </div>
        </div>
        <div class="fr clearfix f_14 al_ct" style="width:250px;">
          <div class="clr_9">参考合约： <span>{{ product.dianjiaheyue }}</span></div>
          <div class="clr_9 pos_rela">基差： <span class="clr_3 f_20">{{ product.jicha }}</span></div>
          <a 
            v-if="product.is_favor === 1"
            href="#."
            @click.prevent="cancelFavor(product.id)" 
            class="f_14 zy_box clr_ora dis_ib cancel_favor mt_5" 
            style="background: #f1830e;color:#fff;"
          >
            <span class="ver_mid">已收藏</span>
          </a>
          <a 
            v-else
            href="#."
            @click.prevent="addFavor(product.id)" 
            class="f_14 zy_box clr_ora dis_ib add_favor mt_5"
          >
            <span class="ver_mid">收藏</span>
          </a>
        </div>
      </div>

      <!-- 分页 - 完全按照PHP原版 -->
      <div class="pager mt_20" v-if="total > 0">
        <!-- 首页 -->
        <a
          v-if="totalPages > 11 && currentPage > 6"
          href="javascript:;"
          @click="goToPage(1)"
        >
          首页
        </a>

        <!-- 上一页 -->
        <a
          v-if="currentPage > 1"
          href="javascript:;"
          @click="goToPage(currentPage - 1)"
        >
          &lt;&lt;
        </a>

        <!-- 页码按钮 -->
        <template v-for="page in visiblePages" :key="page">
          <span
            v-if="page === currentPage"
            class="current"
          >
            {{ page }}
          </span>
          <a
            v-else
            href="javascript:;"
            @click="goToPage(page)"
          >
            {{ page }}
          </a>
        </template>

        <!-- 下一页 -->
        <a
          v-if="currentPage < totalPages"
          href="javascript:;"
          @click="goToPage(currentPage + 1)"
        >&gt;&gt;
        </a>

        <!-- 尾页 -->
        <a
          v-if="totalPages > 11 && currentPage < totalPages - 5"
          href="javascript:;"
          @click="goToPage(totalPages)"
        >
          尾页
        </a>

        <!-- 记录总数显示 - 完全按照PHP原版 -->
        <label class="f_14 clr_6">共{{ total }}条</label>
      </div>
      <div class="pad_30"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProducts, getFilterOptions, addToFavorites, removeFromFavorites, exportProducts } from '@/api/products'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 响应式数据 - 完全按照PHP原版变量名
const chandi = ref<any[]>([])  // 主分类
const chandiDefault = ref<any[]>([])  // 产地选项
const xlsLeixing = ref<string[]>([])  // 类型选项
const xlsNiandu = ref<string[]>([])  // 年度选项
const jiaohuodi = ref<any[]>([])  // 交货地选项
const products = ref<any[]>([])  // 产品列表
const total = ref(0)  // 总数
const currentPage = ref(1)  // 当前页码
const pageSize = ref(10)  // 每页条数
const totalPages = ref(0)  // 总页数

// 当前选择的主分类
const currentDef = ref(1)

// 选中的筛选条件
const selectedParentChandi = ref<number[]>([])
const selectedChildChandi = ref<{[key: number]: number[]}>({})
const selectedLeixing = ref<string[]>([])
const selectedNiandu = ref<string[]>([])
const selectedParentJiaohuodi = ref<number[]>([])
const selectedChildJiaohuodi = ref<{[key: number]: number[]}>({})

// 滑动条拖拽状态
const isDragging = ref(false)
const dragState = ref({
  field: '',
  handle: '',
  min: 0,
  max: 100,
  startX: 0,
  startValue: 0
})

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn)

const selectedParentChandiData = computed(() => {
  return chandiDefault.value.filter(item => selectedParentChandi.value.includes(item.id))
})

const selectedParentJiaohuodiData = computed(() => {
  return jiaohuodi.value.filter(item => selectedParentJiaohuodi.value.includes(item.id))
})

// 分页相关计算属性 - 完全按照PHP原版逻辑
const visiblePages = computed(() => {
  const pages: number[] = []
  const total = totalPages.value
  const current = currentPage.value
  const rollPage = 11 // 分页栏每页显示的页数，与PHP保持一致

  if (total === 0) return pages

  // 计算分页临时变量，完全按照PHP逻辑
  const nowCoolPage = rollPage / 2
  const nowCoolPageCeil = Math.ceil(nowCoolPage)

  // 数字连接，完全按照PHP逻辑
  for (let i = 1; i <= rollPage; i++) {
    let page: number

    if ((current - nowCoolPage) <= 0) {
      page = i
    } else if ((current + nowCoolPage - 1) >= total) {
      page = total - rollPage + i
    } else {
      page = current - nowCoolPageCeil + i
    }

    if (page > 0 && page <= total) {
      pages.push(page)
    } else {
      break
    }
  }

  return pages
})

// 范围筛选 - 完全按照PHP原版
const rangeFilters = reactive({
  changdu: [25, 32],
  qiangli: [25, 32],
  mazhi: [2.5, 5.5],
  huichao: [0, 10],
  zhengqidu: [77, 90],
  hanza: [0, 6],
  bm123: [0, 100],
  bm45: [0, 100],
  ddw123: [0, 100],
  dhr123: [0, 100],
  hr12: [0, 100]
})

// 搜索表单
const searchForm = reactive({
  cangchumingcheng: '',
  jiagongchang: '',
  sk: ''
})

// 其他筛选
const filters = reactive({
  hot: false,
  xunjia: false,
  dabao: false,
  jicha: false,
  yikoujia: false
})

const exportCount = ref('300')

// 方法
const selectMainCategory = (id: number) => {
  currentDef.value = id
  handleSearch(1)
}

const toggleParentChandi = async (item: any) => {
  const index = selectedParentChandi.value.indexOf(item.id)
  if (index > -1) {
    selectedParentChandi.value.splice(index, 1)
    // 清除对应的子产地选择
    delete selectedChildChandi.value[item.id]
  } else {
    selectedParentChandi.value.push(item.id)
    // 获取子产地数据
    try {
      // 这里应该调用API获取子产地数据
      // const response = await getChildChandi(item.id)
      // item.children = response.data
    } catch (error) {
      ElMessage.error('获取子产地数据失败')
    }
  }
}

const toggleChildChandi = (parentId: number, child: any) => {
  if (!selectedChildChandi.value[parentId]) {
    selectedChildChandi.value[parentId] = []
  }

  const index = selectedChildChandi.value[parentId].indexOf(child.id)
  if (index > -1) {
    selectedChildChandi.value[parentId].splice(index, 1)
  } else {
    selectedChildChandi.value[parentId].push(child.id)
  }
}

const isChildSelected = (parentId: number, childId: number) => {
  return selectedChildChandi.value[parentId]?.includes(childId) || false
}

const toggleLeixing = (item: string) => {
  const index = selectedLeixing.value.indexOf(item)
  if (index > -1) {
    selectedLeixing.value.splice(index, 1)
  } else {
    selectedLeixing.value.push(item)
  }
}

const toggleNiandu = (item: string) => {
  const index = selectedNiandu.value.indexOf(item)
  if (index > -1) {
    selectedNiandu.value.splice(index, 1)
  } else {
    selectedNiandu.value.push(item)
  }
}

// 范围更新方法
const updateChangduRange = (value: number[]) => {
  rangeFilters.changdu = value
}

const updateQiangliRange = (value: number[]) => {
  rangeFilters.qiangli = value
}

const updateMazhiRange = (value: number[]) => {
  rangeFilters.mazhi = value
}

const updateHuichaoRange = (value: number[]) => {
  rangeFilters.huichao = value
}

const updateZhengqiduRange = (value: number[]) => {
  rangeFilters.zhengqidu = value
}

const updateHanzaRange = (value: number[]) => {
  rangeFilters.hanza = value
}

const updateBm123Range = (value: number[]) => {
  rangeFilters.bm123 = value
}

const updateBm45Range = (value: number[]) => {
  rangeFilters.bm45 = value
}

const updateDdw123Range = (value: number[]) => {
  rangeFilters.ddw123 = value
}

const updateDhr123Range = (value: number[]) => {
  rangeFilters.dhr123 = value
}

const updateHr12Range = (value: number[]) => {
  rangeFilters.hr12 = value
}

// 交货地筛选方法 - 完全按照PHP原版
const toggleParentJiaohuodi = async (item: any) => {
  const index = selectedParentJiaohuodi.value.indexOf(item.id)
  if (index > -1) {
    selectedParentJiaohuodi.value.splice(index, 1)
    // 清除对应的子交货地选择
    delete selectedChildJiaohuodi.value[item.id]
  } else {
    selectedParentJiaohuodi.value.push(item.id)
    // 获取子交货地数据
    try {
      // 这里应该调用API获取子交货地数据
      // const response = await getChildJiaohuodi(item.id)
      // item.children = response.data
    } catch (error) {
      ElMessage.error('获取子交货地数据失败')
    }
  }
}

const toggleChildJiaohuodi = (parentId: number, child: any) => {
  if (!selectedChildJiaohuodi.value[parentId]) {
    selectedChildJiaohuodi.value[parentId] = []
  }

  const index = selectedChildJiaohuodi.value[parentId].indexOf(child.id)
  if (index > -1) {
    selectedChildJiaohuodi.value[parentId].splice(index, 1)
  } else {
    selectedChildJiaohuodi.value[parentId].push(child.id)
  }
}

const isChildJiaohuodiSelected = (parentId: number, childId: number) => {
  return selectedChildJiaohuodi.value[parentId]?.includes(childId) || false
}

const handleHeyueFilter = (type: string) => {
  // 重置其他合约筛选
  Object.keys(filters).forEach(key => {
    if (key !== 'hot' && key !== type.toLowerCase()) {
      filters[key as keyof typeof filters] = false
    }
  })
  handleSearch(1)
}

// 分页方法
const goToPage = (page: number) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) {
    return
  }
  handleSearch(page)
}



const handleSearch = async (page: number = 1) => {
  try {
    const params = {
      def: currentDef.value,
      cd: selectedParentChandi.value.join(','),
      cd2: JSON.stringify(selectedChildChandi.value),
      leixing: selectedLeixing.value.join('|'),
      pihao_kunkao: selectedNiandu.value.join('|'),
      changdu_from: rangeFilters.changdu[0],
      changdu_to: rangeFilters.changdu[1],
      qiangli_from: rangeFilters.qiangli[0],
      qiangli_to: rangeFilters.qiangli[1],
      mazhi_from: rangeFilters.mazhi[0],
      mazhi_to: rangeFilters.mazhi[1],
      huichaolv_from: rangeFilters.huichao[0],
      huichaolv_to: rangeFilters.huichao[1],
      zhengqidu_from: rangeFilters.zhengqidu[0],
      zhengqidu_to: rangeFilters.zhengqidu[1],
      hanzalv_from: rangeFilters.hanza[0],
      hanzalv_to: rangeFilters.hanza[1],
      cangchumingcheng: searchForm.cangchumingcheng,
      jiagongchang: searchForm.jiagongchang,
      sk: searchForm.sk,
      hot: filters.hot ? 1 : 0,
      heyue: getSelectedHeyue(),
      current: page,  // 当前页码
      size: pageSize.value  // 每页条数
    }

    const response = await getProducts(params)
    products.value = response.data?.records || []
    total.value = response.data?.total || 0
    currentPage.value = response.data?.current || page
    pageSize.value = response.data?.size || 10
    totalPages.value = response.data?.pages || 0
  } catch (error) {
    ElMessage.error('获取产品数据失败')
  }
}

const getSelectedHeyue = () => {
  if (filters.xunjia) return '询价'
  if (filters.dabao) return '打包'
  if (filters.jicha) return 'jicha'
  if (filters.yikoujia) return '一口价'
  return ''
}

const viewProduct = (id: number) => {
  router.push(`/public/products/${id}`)
}

const addFavor = async (id: number) => {
  try {
    await addToFavorites(id)
    ElMessage.success('收藏成功')
    handleSearch(currentPage.value) // 重新获取数据
  } catch (error) {
    ElMessage.error('收藏失败')
  }
}

const cancelFavor = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认取消收藏？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await removeFromFavorites(id)
    ElMessage.success('取消收藏成功')
    handleSearch(currentPage.value) // 重新获取数据
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('取消收藏失败')
    }
  }
}

const addToDingzhi = () => {
  // 添加到个性需求的逻辑
  ElMessage.info('添加到个性需求功能开发中')
}

const exportExcel = async () => {
  try {
    if (!userStore.isLoggedIn) {
      ElMessage.error('请先登录')
      return
    }

    const count = parseInt(exportCount.value)
    if (!count || count <= 0) {
      ElMessage.error('请输入有效的导出条数')
      return
    }

    // 根据用户类型限制导出条数
    const maxCount = userStore.userInfo.member_type === 1 ? 45000 : 300
    if (count > maxCount) {
      ElMessage.error(`最大不超过${maxCount}条`)
      return
    }

    const params = {
      // ... 所有筛选参数
      export_count: count,
      act: 'excel'
    }

    const response = await exportProducts(params)
    if (response.data?.url) {
      window.location.href = response.data.url
    }
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Vue slider methods - 完全按照ionRangeSlider功能实现
const getSliderBarStyle = (field: string, min: number, max: number) => {
  const [from, to] = rangeFilters[field as keyof typeof rangeFilters]
  const fromPercent = ((from - min) / (max - min)) * 100
  const toPercent = ((to - min) / (max - min)) * 100
  
  return {
    left: `${fromPercent}%`,
    width: `${toPercent - fromPercent}%`
  }
}

const getSliderHandleStyle = (field: string, min: number, max: number, handle: string) => {
  const [from, to] = rangeFilters[field as keyof typeof rangeFilters]
  const value = handle === 'left' ? from : to
  const percent = ((value - min) / (max - min)) * 100
  
  return {
    left: `${percent}%`
  }
}

// 判断是否显示默认值（当选中值远离默认值时显示）
const shouldShowMinMax = (field: string, min: number, max: number) => {
  const [from, to] = rangeFilters[field as keyof typeof rangeFilters]
  const threshold = (max - min) * 0.1 // 10%的阈值
  
  return {
    showMin: from > min + threshold,
    showMax: to < max - threshold
  }
}

const handleSliderClick = (event: MouseEvent, field: string, min: number, max: number) => {
  if (isDragging.value) return
  
  const target = event.currentTarget as HTMLElement
  const rect = target.getBoundingClientRect()
  const clickPercent = (event.clientX - rect.left) / rect.width
  let clickValue = min + clickPercent * (max - min)
  
  // 保留1位小数
  clickValue = Math.round(clickValue * 10) / 10
  
  const [from, to] = rangeFilters[field as keyof typeof rangeFilters]
  const leftDist = Math.abs(clickValue - from)
  const rightDist = Math.abs(clickValue - to)
  
  if (leftDist < rightDist) {
    rangeFilters[field as keyof typeof rangeFilters][0] = Math.max(min, Math.min(clickValue, to))
  } else {
    rangeFilters[field as keyof typeof rangeFilters][1] = Math.max(from, Math.min(clickValue, max))
  }
}

const startDrag = (event: MouseEvent | TouchEvent, field: string, handle: string, min: number, max: number) => {
  event.preventDefault()
  
  // 获取客户端X坐标（支持触摸和鼠标事件）
  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
  
  isDragging.value = true
  dragState.value = {
    field,
    handle,
    min,
    max,
    startX: clientX,
    startValue: handle === 'left' ? rangeFilters[field as keyof typeof rangeFilters][0] : rangeFilters[field as keyof typeof rangeFilters][1]
  }
  
  // 添加拖拽状态的视觉反馈
  const handleElement = event.target as HTMLElement
  handleElement.classList.add('dragging')
  
  // 添加鼠标和触摸事件监听
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchmove', handleDrag, { passive: false })
  document.addEventListener('touchend', stopDrag)
}

const handleDrag = (event: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return
  
  // 阻止触摸事件的默认行为
  if ('touches' in event) {
    event.preventDefault()
  }
  
  const { field, handle, min, max, startX, startValue } = dragState.value
  
  // 获取客户端X坐标（支持触摸和鼠标事件）
  const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
  const deltaX = clientX - startX
  
  // 动态获取容器宽度
  const sliderTrack = document.querySelector('.vue-slider-track') as HTMLElement
  const containerWidth = sliderTrack ? sliderTrack.offsetWidth : 240
  
  const deltaPercent = deltaX / containerWidth
  const deltaValue = deltaPercent * (max - min)
  let newValue = Math.max(min, Math.min(max, startValue + deltaValue))
  
  // 保留1位小数
  newValue = Math.round(newValue * 10) / 10
  
  const currentValues = rangeFilters[field as keyof typeof rangeFilters]
  
  if (handle === 'left') {
    rangeFilters[field as keyof typeof rangeFilters][0] = Math.min(newValue, currentValues[1])
  } else {
    rangeFilters[field as keyof typeof rangeFilters][1] = Math.max(newValue, currentValues[0])
  }
}

const stopDrag = () => {
  if (!isDragging.value) return
  
  // 移除拖拽状态的视觉反馈
  const draggingElements = document.querySelectorAll('.vue-slider-handle.dragging')
  draggingElements.forEach(el => el.classList.remove('dragging'))
  
  isDragging.value = false
  dragState.value = {
    field: '',
    handle: '',
    min: 0,
    max: 100,
    startX: 0,
    startValue: 0
  }
  
  // 移除所有事件监听器
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', handleDrag)
  document.removeEventListener('touchend', stopDrag)
}

// 格式化显示数值，按照ionRangeSlider的规则（step=0.1时显示1位小数）
const formatSliderValue = (value: number) => {
  // ionRangeSlider在step=0.1时总是显示1位小数，即使是整数
  return value.toFixed(1)
}

// 获取筛选选项数据
const fetchFilterOptions = async () => {
  try {
    const response = await getFilterOptions()
    chandi.value = response.data?.chandi || []
    chandiDefault.value = response.data?.chandi_default || []
    xlsLeixing.value = response.data?.xls_leixing || []
    xlsNiandu.value = response.data?.xls_niandu || []
    jiaohuodi.value = response.data?.jiaohuodi || []
  } catch (error) {
    ElMessage.error('获取筛选选项失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchFilterOptions()
  handleSearch(1)
  
  // IE兼容性处理 - 完全按照PHP原版
  nextTick(() => {
    const isIE = () => {
      return !!(window as any).ActiveXObject || 'ActiveXObject' in window
    }
    
    if (isIE()) {
      // IE浏览器的样式调整
      const hang2Elements = document.querySelectorAll('.hang2')
      hang2Elements.forEach(el => {
        (el as HTMLElement).style.marginTop = '10px'
      })
      
      const lengthElements = document.querySelectorAll('.length')
      lengthElements.forEach(el => {
        (el as HTMLElement).style.top = '0'
      })
    } else {
      // 非IE浏览器的样式调整（已在CSS中设置，这里作为备用）
      const hang1Elements = document.querySelectorAll('.hang1')
      hang1Elements.forEach(el => {
        (el as HTMLElement).style.marginTop = '-20px'
      })
    }
  })
})

// 清理事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchmove', handleDrag)
  document.removeEventListener('touchend', stopDrag)
})
</script>

<style scoped>
/* 完全按照PHP原版样式 */

/* 主分类导航样式 - 完全按照PHP原版 */
.mian_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mian_ul li {
  float: left;
  margin-right: 10px;
}

.mian_ul li a {
  display: block;
  height: 30px;
  line-height: 30px;
  width: 120px;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
}

.mian_norm {
  background: #f7f7f7;
  border-top: 3px solid #f7f7f7;
  border-bottom: 3px solid #f7f7f7;
  color: #333;
}

.mian_norm:hover {
  background: #f0f0f0;
}

.mian_cur {
  border-top: 3px solid #50c6ee;
  border-bottom: 3px solid #fff;
  background: #fff;
  color: #28afe5 !important;
  font-weight: bold;
}

/* 筛选区域样式 - 完全按照PHP原版 */
.chandi_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.chandi_ul li {
  display: inline-block;
  margin-left: 5px;
  margin-right: 5px;
}

.chandi_ul li a {
  display: inline-block;
  text-decoration: none;
  transition: all 0.3s;
  cursor: pointer;
  color: #333;
}

.chandi_ul li a:hover {
  background: #f0f0f0;
}

.xuan_cur {
  border: 1px solid #0859a5 !important;
  border-radius: 5px !important;
  padding: 1px 5px !important;
  background: #e6f3ff !important;
  color: #0859a5 !important;
}

/* 范围滑块样式 - 完全按照PHP原版 */
.hang1, .hang2 {
  margin-bottom: 15px;
  position: relative;
}

/* 非 IE 浏览器的默认样式，完全按照PHP原版 */  
.hang1 {
  margin-top: -20px;
}

.hang2 {
  margin-top: 10px;
}

.hang1 li:last-child,
.hang2 li:last-child {
  margin-left: 10px;
  vertical-align: top;
}

.bottom{
  margin-bottom: 30px;
}
/* 颜色级筛选样式 - 完全按照PHP原版 */
.green {
  margin-top: 0px;
}

/* Vue滑块样式 - 完全按照ionRangeSlider .irs--round 规格 */
.vue-slider-container {
  position: relative;
  width: 100%;
  font-size: 12px;
  font-family: Arial, sans-serif;
  margin-top: 15px;
  margin-left: 8px;
}

/* 轨道：.irs--round .irs-line {top:36px;height:4px;background-color:#dee4ec;border-radius:4px} */
.vue-slider-track {
  position: absolute;
  top: 36px;
  left: 0;
  right: 0;
  height: 4px;
  background-color: #dee4ec;
  border-radius: 4px;
  cursor: pointer;
}

/* 进度条：.irs--round .irs-bar {top:36px;height:4px;background-color:#50c6ee;cursor:pointer} */
.vue-slider-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 4px;
  background-color: #50c6ee;
  cursor: pointer;
  border-radius: 4px 0 0 4px;
}

/* 手柄：.irs--round .irs-handle {top:30px;width:15px;height:15px;border:4px solid #50c6ee;background-color:white;border-radius:24px;box-shadow:0 1px 3px rgba(0,0,255,0.3)} */
.vue-slider-handle {
  position: absolute;
  top: -7px; /* 调整使手柄在轨道中间：36px(轨道top) - 2px(轨道高度一半) - 11.5px(手柄半径) = 22.5px，但ionRangeSlider是30px，所以用28px让它更居中 */
  width: 10px;
  height: 10px;
  border: 4px solid #50c6ee;
  background-color: white;
  border-radius: 50%; /* 完全圆形 */
  box-shadow: 0 1px 3px rgba(0,0,255,0.3);
  cursor: pointer;
  z-index: 5;
  margin-left: -11.5px;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  touch-action: none;
}

.vue-slider-handle:hover {
  background-color: #f0f6ff;
}

.vue-slider-handle.dragging {
  background-color: #e6f3ff;
  box-shadow: 0 2px 6px rgba(0,0,255,0.4);
  z-index: 10;
}

/* 默认值：默认隐藏，只有当选中值远离时才显示 */
.vue-slider-min, .vue-slider-max {
  position: absolute;
  top: 0;
  color: #333;
  font-size: 14px;
  line-height: 1;
  padding: 3px 5px;
  background-color: rgba(0,0,0,0.1);
  border-radius: 4px;
  cursor: default;
  opacity: 0; /* 默认隐藏 */
  transition: opacity 0.3s ease;
}

.vue-slider-min {
  left: 0;
}

.vue-slider-max {
  right: 0;
}

/* 当选中值远离默认值时显示默认值 */
.vue-slider-min.show, .vue-slider-max.show {
  opacity: 1;
}

/* 选中值：在手柄上方显示 */
.vue-slider-from, .vue-slider-to {
  position: absolute;
  top: -30px; /* 在手柄上方 */
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  line-height: 1;
  text-shadow: none;
  padding: 3px 5px;
  background-color: #50c6ee;
  color: white;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 10;
  cursor: default;
}

/* 小三角指向手柄 */
.vue-slider-from:before, .vue-slider-to:before {
  position: absolute;
  display: block;
  content: "";
  bottom: -6px;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -3px;
  overflow: hidden;
  border: 3px solid transparent;
  border-top-color: #50c6ee;
}

/* Element Plus滑块样式调整 */
:deep(.el-slider) {
  margin: 40px 0 10px 0;
  position: relative;
}

:deep(.el-slider__runway) {
  background-color: #e4e7ed;
}

:deep(.el-slider__bar) {
  background-color: #28afe5;
}

:deep(.el-slider__button) {
  border-color: #28afe5;
}

:deep(.el-slider__button:hover) {
  border-color: #1e9bd1;
}

/* 滑块容器对齐 - 完全按照PHP原版 */
.hang1 li[style*="width:240px"],
.hang2 li[style*="width:240px"] {
  position: relative;
  top: 0;
  display: inline-block;
  vertical-align: top;
  margin-top: 0;
  height: 65px; /* 与滑块容器高度匹配 */
}

/* 排序区域样式 - 完全按照PHP原版 */
.paixu_ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.paixu_ul li {
  display: inline-block;
  margin-right: 3px;
  border-right: 1px solid #eee;
  padding-right: 5px;
  vertical-align: middle;
}

.paixu_ul li:first-child {
  border: none;
  background: none;
  font-weight: bold;
  padding-right: 10px;
}

.paixu_ul li:last-child {
  border-right: none;
}

.paixu_ul li label {
  margin: 0;
  cursor: pointer;
  font-size: 14px;
}

.paixu_ul li input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

/* 产品列表样式 - 完全按照PHP原版 */
.bg_gr {
  background: #f9f9f9;
}

.product-item {
  border: 1px solid #ddd;
  background: #f9f9f9;
  padding: 10px;
  margin: 10px 0;
  position: relative;
  transition: background-color 0.3s;
}

.product-item:hover {
  background: #f5f5f5;
}

/* 收藏按钮样式 - 完全按照PHP原版 */
.zy_box {
  display: inline-block;
  padding: 5px 15px;
  border: 1px solid #f1830e;
  border-radius: 3px;
  text-decoration: none;
  transition: all 0.3s;
  cursor: pointer;
  background: #fff;
  color: #f1830e;
}

.zy_box:hover {
  background: #f1830e;
  color: #fff !important;
}

.cancel_favor {
  background: #f1830e !important;
  color: #fff !important;
}

.cancel_favor:hover {
  background: #e55a00 !important;
}

/* 输入框样式 - 完全按照PHP原版 */
.border1 {
  border: 1px solid #ddd;
}

.pad_5 {
  padding: 5px;
}

.bg_lan {
  background: #28afe5;
  cursor: pointer;
  border-radius: 3px;
  margin-left: 10px;
}

.bg_lan:hover {
  background: #1e9bd1;
}

/* 遮罩层样式 - 完全按照PHP原版 */
.mask_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}
</style>

