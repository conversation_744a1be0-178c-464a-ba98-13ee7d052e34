<template>
  <div class="favorites-page">
    <div class="container">
      <div class="page-header">
        <h1>我的收藏</h1>
        <div class="header-actions">
          <el-button @click="clearAll" type="danger" :disabled="favorites.length === 0">
            清空收藏
          </el-button>
        </div>
      </div>

      <!-- 收藏列表 -->
      <div class="favorites-grid" v-if="favorites.length > 0">
        <div v-for="(item, index) in favorites" :key="index" class="favorite-item">
          <div class="item-image">
            <img :src="getImageUrl(item.thumbs)" :alt="item.title" />
          </div>
          <div class="item-content">
            <h3 class="item-title">{{ item.pihaoKunkao || item.title }}</h3>
            <div class="item-details">
              <div class="detail-item" v-if="item.leixing">
                <span class="label">类型：</span>
                <span class="value">{{ item.leixing }}</span>
              </div>
              <div class="detail-item" v-if="item.yansejiPinji">
                <span class="label">颜色级：</span>
                <span class="value">{{ item.yansejiPinji }}</span>
              </div>
              <div class="detail-item" v-if="item.mazhi">
                <span class="label">马值：</span>
                <span class="value">{{ item.mazhi }}</span>
              </div>
              <div class="detail-item" v-if="item.changdu">
                <span class="label">长度：</span>
                <span class="value">{{ item.changdu }}</span>
              </div>
              <div class="detail-item" v-if="item.qiangli">
                <span class="label">强力：</span>
                <span class="value">{{ item.qiangli }}</span>
              </div>
              <div class="detail-item" v-if="item.jiagongchang">
                <span class="label">加工厂：</span>
                <span class="value">{{ item.jiagongchang }}</span>
              </div>
            </div>
            <div class="item-actions">
              <el-button type="primary" size="small" @click="viewDetail(item.id)">
                查看详情
              </el-button>
              <el-button type="danger" size="small" @click="removeFavorite(item.id)">
                取消收藏
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">❤️</div>
        <h3>暂无收藏</h3>
        <p>您还没有收藏任何商品，快去<router-link to="/public/products">棉花商城</router-link>看看吧！</p>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="total > pageSize">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFavorites, removeFavorite as removeFavoriteApi } from '@/api/favorites'

const router = useRouter()

// 响应式数据
const favorites = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 获取收藏列表
const fetchFavorites = async () => {
  try {
    const response = await getFavorites({
      current: currentPage.value,
      size: pageSize.value
    })
    favorites.value = response.data?.records || []
    total.value = response.data?.total || 0
  } catch (error) {
    ElMessage.error('获取收藏列表失败')
  }
}

// 获取图片URL
const getImageUrl = (thumbs: string) => {
  if (!thumbs) return '/default-product.jpg'
  if (thumbs.startsWith('http')) return thumbs
  return `/api/files/${thumbs}`
}

// 查看详情
const viewDetail = (id: number) => {
  router.push(`/public/products/${id}`)
}

// 取消收藏
const removeFavorite = async (id: number) => {
  try {
    await ElMessageBox.confirm('确认取消收藏吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await removeFavoriteApi(id)
    ElMessage.success('取消收藏成功')
    fetchFavorites()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消收藏失败')
    }
  }
}

// 清空收藏
const clearAll = async () => {
  try {
    await ElMessageBox.confirm('确认清空所有收藏吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // TODO: 实现清空收藏的API
    ElMessage.success('清空收藏成功')
    fetchFavorites()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空收藏失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchFavorites()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchFavorites()
}

// 页面加载时获取数据
onMounted(() => {
  fetchFavorites()
})
</script>

<style scoped>
.favorites-page {
  padding: 20px 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.favorite-item {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s;
}

.favorite-item:hover {
  transform: translateY(-2px);
}

.item-image {
  height: 200px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  padding: 15px;
}

.item-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.item-details {
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  margin-bottom: 5px;
}

.label {
  color: #666;
  margin-right: 5px;
  min-width: 60px;
}

.value {
  color: #333;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  border-radius: 8px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.empty-state p {
  color: #666;
  margin: 0;
}

.empty-state a {
  color: #409EFF;
  text-decoration: none;
}

.empty-state a:hover {
  text-decoration: underline;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .favorites-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .item-actions {
    flex-direction: column;
  }
}
</style>
