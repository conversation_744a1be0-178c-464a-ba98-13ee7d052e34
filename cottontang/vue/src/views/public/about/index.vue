<template>
  <div class="about-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <h1>关于我们</h1>
        <p>了解棉花棠 - 您值得信赖的棉花贸易伙伴</p>
      </div>
    </div>

    <!-- 公司简介 -->
    <div class="company-intro">
      <div class="container">
        <div class="intro-content">
          <div class="intro-text">
            <h2>公司简介</h2>
            <p>
              棉花棠成立于2010年，是一家专业从事棉花贸易的现代化企业。我们致力于为客户提供优质的棉花产品和专业的贸易服务，
              在新疆地区拥有多个仓储基地，年贸易量超过10万吨。
            </p>
            <p>
              公司秉承"诚信经营、品质至上"的经营理念，建立了完善的质量管理体系和供应链网络，
              为纺织企业提供稳定可靠的原料供应。
            </p>
            <div class="company-stats">
              <div class="stat-item">
                <div class="stat-number">13</div>
                <div class="stat-label">年行业经验</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">10+</div>
                <div class="stat-label">万吨年贸易量</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">500+</div>
                <div class="stat-label">合作客户</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">个仓储基地</div>
              </div>
            </div>
          </div>
          <div class="intro-image">
            <img src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg" alt="公司简介" />
          </div>
        </div>
      </div>
    </div>

    <!-- 企业文化 -->
    <div class="company-culture">
      <div class="container">
        <h2>企业文化</h2>
        <div class="culture-grid">
          <div class="culture-item">
            <div class="culture-icon">🎯</div>
            <h3>使命</h3>
            <p>为纺织行业提供优质棉花原料，推动行业健康发展</p>
          </div>
          <div class="culture-item">
            <div class="culture-icon">👁️</div>
            <h3>愿景</h3>
            <p>成为中国领先的棉花贸易服务商</p>
          </div>
          <div class="culture-item">
            <div class="culture-icon">⭐</div>
            <h3>价值观</h3>
            <p>诚信、专业、创新、共赢</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 发展历程 -->
    <div class="company-history">
      <div class="container">
        <h2>发展历程</h2>
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-year">2010</div>
            <div class="timeline-content">
              <h3>公司成立</h3>
              <p>在新疆乌鲁木齐成立，开始棉花贸易业务</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2013</div>
            <div class="timeline-content">
              <h3>业务扩展</h3>
              <p>在石河子、阿克苏等地建立仓储基地</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2016</div>
            <div class="timeline-content">
              <h3>质量认证</h3>
              <p>通过ISO9001质量管理体系认证</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2020</div>
            <div class="timeline-content">
              <h3>数字化转型</h3>
              <p>启动数字化平台建设，提升服务效率</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2023</div>
            <div class="timeline-content">
              <h3>持续发展</h3>
              <p>年贸易量突破10万吨，服务客户超过500家</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系信息 -->
    <div class="contact-info">
      <div class="container">
        <h2>联系我们</h2>
        <div class="contact-grid">
          <div class="contact-item">
            <div class="contact-icon">📍</div>
            <h3>公司地址</h3>
            <p>新疆乌鲁木齐市经济技术开发区</p>
          </div>
          <div class="contact-item">
            <div class="contact-icon">📞</div>
            <h3>联系电话</h3>
            <p>400-888-8888</p>
          </div>
          <div class="contact-item">
            <div class="contact-icon">✉️</div>
            <h3>邮箱地址</h3>
            <p><EMAIL></p>
          </div>
          <div class="contact-item">
            <div class="contact-icon">🌐</div>
            <h3>官方网站</h3>
            <p>www.cottontang.com</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
document.title = '关于我们 - 棉花棠'
</script>

<style scoped>
.about-page {
  min-height: 100vh;
}

.container {
  width: 1200px;
  max-width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.page-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.company-intro {
  padding: 80px 0;
  background: #f8f9fa;
}

.intro-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.intro-text h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #333;
}

.intro-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #666;
  margin-bottom: 1.5rem;
}

.company-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #666;
}

.intro-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.company-culture {
  padding: 80px 0;
}

.company-culture h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.culture-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

.culture-item {
  text-align: center;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background: white;
}

.culture-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.culture-item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.culture-item p {
  color: #666;
  line-height: 1.6;
}

.company-history {
  padding: 80px 0;
  background: #f8f9fa;
}

.company-history h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
}

.timeline-item {
  display: flex;
  margin-bottom: 3rem;
  align-items: flex-start;
}

.timeline-year {
  background: #667eea;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 50px;
  font-weight: 700;
  margin-right: 2rem;
  min-width: 80px;
  text-align: center;
}

.timeline-content h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.timeline-content p {
  color: #666;
  line-height: 1.6;
}

.contact-info {
  padding: 80px 0;
}

.contact-info h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #333;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.contact-item {
  text-align: center;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background: white;
}

.contact-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.contact-item h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #333;
}

.contact-item p {
  color: #666;
}

@media (max-width: 768px) {
  .intro-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .company-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .culture-grid {
    grid-template-columns: 1fr;
  }

  .contact-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .timeline-item {
    flex-direction: column;
    text-align: center;
  }

  .timeline-year {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}
</style>
