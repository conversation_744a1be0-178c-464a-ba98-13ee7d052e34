<template>
  <div class="store-container">
    <div class="top-actions">
      <el-button type="primary" @click="handleAddProduct">
        <el-icon><Plus /></el-icon>添加商品
      </el-button>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索商品名称/描述"
          clearable
          @clear="fetchProductList"
          @input="handleSearchInput"
        >
          <template #append>
            <el-button @click="fetchProductList">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="全部商品" name="all"></el-tab-pane>
      <el-tab-pane label="已上架" name="on"></el-tab-pane>
      <el-tab-pane label="已下架" name="off"></el-tab-pane>
    </el-tabs>
    
    <el-table v-loading="loading" :data="productList" border style="width: 100%">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="商品图片" width="120">
        <template #default="scope">
          <el-image
            style="width: 80px; height: 80px"
            :src="scope.row.imageUrl"
            :preview-src-list="[scope.row.imageUrl]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="商品名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="categoryName" label="分类" width="100" />
      <el-table-column prop="price" label="价格" width="100">
        <template #default="scope">
          ¥{{ scope.row.price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="stock" label="库存" width="80" />
      <el-table-column prop="sales" label="销量" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">上架中</el-tag>
          <el-tag v-else type="info">已下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button size="small" @click="handleEditProduct(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 1 ? 'warning' : 'success'" 
            @click="handleToggleStatus(scope.row)"
          >
            {{ scope.row.status === 1 ? '下架' : '上架' }}
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDeleteProduct(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 商品编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑商品' : '添加商品'" 
      width="650px" 
      :destroy-on-close="true"
    >
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品分类" prop="categoryId">
          <el-select v-model="productForm.categoryId" placeholder="请选择商品分类">
            <el-option 
              v-for="item in categoryOptions" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品图片" prop="imageUrl">
          <!-- 实际项目中应该有图片上传功能 -->
          <el-input v-model="productForm.imageUrl" placeholder="请输入图片URL">
            <template #append>
              <el-button @click="handleImageSelect">选择</el-button>
            </template>
          </el-input>
          <div class="image-preview" v-if="productForm.imageUrl">
            <el-image :src="productForm.imageUrl" style="width: 100px; height: 100px" fit="cover" />
          </div>
        </el-form-item>
        <el-form-item label="商品价格" prop="price">
          <el-input-number v-model="productForm.price" :precision="2" :step="0.1" :min="0" />
        </el-form-item>
        <el-form-item label="商品库存" prop="stock">
          <el-input-number v-model="productForm.stock" :min="0" :step="1" />
        </el-form-item>
        <el-form-item label="商品简介" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入商品简介"
          />
        </el-form-item>
        <el-form-item label="商品详情" prop="detail">
          <el-input
            v-model="productForm.detail"
            type="textarea"
            :rows="5"
            placeholder="请输入商品详情"
          />
        </el-form-item>
        <el-form-item label="上架状态" prop="status">
          <el-radio-group v-model="productForm.status">
            <el-radio :label="1">上架</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import {
  getStoreList,
  createStore,
  updateStore,
  deleteStore,
  updateStoreStatus,
  getCategoryList
} from '@/api/store'

// 表格数据
const loading = ref(false)
const productList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')
const activeTab = ref('all')

// 对话框控制
const dialogVisible = ref(false)
const isEdit = ref(false)
const productFormRef = ref<FormInstance>()

// 分类选项
const categoryOptions = ref([])

// 表单数据
const productForm = reactive({
  id: 0,
  name: '',
  categoryId: 0,
  categoryName: '',
  imageUrl: '',
  price: 0,
  stock: 0,
  sales: 0,
  description: '',
  detail: '',
  status: 1
})

// 表单验证规则
const productRules = reactive({
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  imageUrl: [{ required: true, message: '请输入商品图片', trigger: 'blur' }],
  price: [{ required: true, message: '请输入商品价格', trigger: 'blur' }],
  stock: [{ required: true, message: '请输入商品库存', trigger: 'blur' }],
  description: [{ required: true, message: '请输入商品简介', trigger: 'blur' }]
})

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchQuery.value,
      status: activeTab.value === 'all' ? '' : (activeTab.value === 'on' ? 1 : 0)
    }

    const response = await getStoreList(params)
    productList.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取商品列表失败')
    productList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索输入
let searchTimeout: number | null = null
const handleSearchInput = () => {
  // 防抖处理
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    currentPage.value = 1
    fetchProductList()
  }, 500)
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchProductList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchProductList()
}

// 处理标签切换
const handleTabClick = () => {
  fetchProductList()
}

// 添加商品
const handleAddProduct = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑商品
const handleEditProduct = (row: any) => {
  isEdit.value = true
  Object.assign(productForm, row)
  dialogVisible.value = true
}

// 切换商品状态
const handleToggleStatus = async (row: any) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '上架' : '下架'

  try {
    await ElMessageBox.confirm(
      `确定要${statusText}商品 "${row.name}" 吗?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateStoreStatus(row.id, newStatus)
    ElMessage({
      type: 'success',
      message: `${statusText}成功`
    })
    fetchProductList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 删除商品
const handleDeleteProduct = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除商品 "${row.name}" 吗?`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteStore(row.id)
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchProductList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 图片选择
const handleImageSelect = () => {
  // 实际项目中应该打开图片选择器
  ElMessage({
    type: 'info',
    message: '请实现图片上传功能'
  })
}

// 重置表单
const resetForm = () => {
  productForm.id = 0
  productForm.name = ''
  productForm.categoryId = 0
  productForm.categoryName = ''
  productForm.imageUrl = ''
  productForm.price = 0
  productForm.stock = 0
  productForm.sales = 0
  productForm.description = ''
  productForm.detail = ''
  productForm.status = 1
}

// 提交表单
const submitForm = async () => {
  if (!productFormRef.value) return

  await productFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 如果是添加商品，设置分类名称
        if (!isEdit.value) {
          const category = categoryOptions.value.find((item: any) => item.id === productForm.categoryId)
          if (category) {
            productForm.categoryName = category.name
          }
        }

        // 提交数据
        if (isEdit.value) {
          await updateStore(productForm.id.toString(), productForm)
        } else {
          await createStore(productForm)
        }

        ElMessage({
          type: 'success',
          message: isEdit.value ? '修改成功' : '添加成功'
        })
        dialogVisible.value = false
        fetchProductList()
      } catch (error) {
        ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
      }
    }
  })
}

// 获取分类列表
const fetchCategoryList = async () => {
  try {
    const response = await getCategoryList()
    categoryOptions.value = response.data || []
  } catch (error) {
    ElMessage.error('获取分类列表失败')
    categoryOptions.value = []
  }
}

// 页面加载时获取商品列表和分类列表
onMounted(() => {
  fetchProductList()
  fetchCategoryList()
})
</script>

<style scoped>
.store-container {
  padding: 20px 0;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 320px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.image-preview {
  margin-top: 10px;
}
</style> 