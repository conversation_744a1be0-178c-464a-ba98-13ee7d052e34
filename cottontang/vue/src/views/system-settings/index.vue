<template>
  <div class="system-settings-page">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>管理系统配置参数</p>
    </div>

    <el-row :gutter="20">
      <!-- 设置分组 -->
      <el-col :span="6">
        <el-card class="group-card">
          <template #header>
            <span>设置分组</span>
          </template>
          <el-menu
            :default-active="activeGroup"
            @select="handleGroupSelect"
            class="group-menu"
          >
            <el-menu-item index="basic">
              <el-icon><Setting /></el-icon>
              <span>基础设置</span>
            </el-menu-item>
            <el-menu-item index="system">
              <el-icon><Monitor /></el-icon>
              <span>系统配置</span>
            </el-menu-item>
            <el-menu-item index="email">
              <el-icon><Message /></el-icon>
              <span>邮件设置</span>
            </el-menu-item>
            <el-menu-item index="upload">
              <el-icon><Upload /></el-icon>
              <span>上传设置</span>
            </el-menu-item>
          </el-menu>
        </el-card>
      </el-col>

      <!-- 设置内容 -->
      <el-col :span="18">
        <el-card class="settings-card" v-loading="loading">
          <template #header>
            <div class="card-header">
              <span>{{ getGroupTitle(activeGroup) }}</span>
              <el-button type="primary" @click="handleSave">保存设置</el-button>
            </div>
          </template>

          <el-form :model="settingsForm" label-width="120px">
            <!-- 基础设置 -->
            <template v-if="activeGroup === 'basic'">
              <el-form-item label="网站名称">
                <el-input v-model="settingsForm.site_name" placeholder="请输入网站名称" />
              </el-form-item>
              <el-form-item label="网站标题">
                <el-input v-model="settingsForm.site_title" placeholder="请输入网站标题" />
              </el-form-item>
              <el-form-item label="网站描述">
                <el-input
                  v-model="settingsForm.site_description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入网站描述"
                />
              </el-form-item>
              <el-form-item label="网站关键词">
                <el-input v-model="settingsForm.site_keywords" placeholder="请输入网站关键词，用逗号分隔" />
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input v-model="settingsForm.contact_phone" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item label="联系邮箱">
                <el-input v-model="settingsForm.contact_email" placeholder="请输入联系邮箱" />
              </el-form-item>
            </template>

            <!-- 系统配置 -->
            <template v-if="activeGroup === 'system'">
              <el-form-item label="系统维护">
                <el-switch v-model="settingsForm.system_maintenance" />
                <span class="form-tip">开启后前台将显示维护页面</span>
              </el-form-item>
              <el-form-item label="用户注册">
                <el-switch v-model="settingsForm.user_register" />
                <span class="form-tip">是否允许用户注册</span>
              </el-form-item>
              <el-form-item label="评论审核">
                <el-switch v-model="settingsForm.comment_audit" />
                <span class="form-tip">是否需要审核用户评论</span>
              </el-form-item>
              <el-form-item label="分页大小">
                <el-input-number v-model="settingsForm.page_size" :min="10" :max="100" />
                <span class="form-tip">每页显示的记录数</span>
              </el-form-item>
            </template>

            <!-- 邮件设置 -->
            <template v-if="activeGroup === 'email'">
              <el-form-item label="SMTP服务器">
                <el-input v-model="settingsForm.smtp_host" placeholder="请输入SMTP服务器地址" />
              </el-form-item>
              <el-form-item label="SMTP端口">
                <el-input-number v-model="settingsForm.smtp_port" :min="1" :max="65535" />
              </el-form-item>
              <el-form-item label="发送邮箱">
                <el-input v-model="settingsForm.smtp_username" placeholder="请输入发送邮箱" />
              </el-form-item>
              <el-form-item label="邮箱密码">
                <el-input
                  v-model="settingsForm.smtp_password"
                  type="password"
                  placeholder="请输入邮箱密码或授权码"
                  show-password
                />
              </el-form-item>
              <el-form-item label="SSL加密">
                <el-switch v-model="settingsForm.smtp_ssl" />
              </el-form-item>
            </template>

            <!-- 上传设置 -->
            <template v-if="activeGroup === 'upload'">
              <el-form-item label="上传路径">
                <el-input v-model="settingsForm.upload_path" placeholder="请输入上传文件保存路径" />
              </el-form-item>
              <el-form-item label="允许扩展名">
                <el-input v-model="settingsForm.upload_extensions" placeholder="请输入允许的文件扩展名，用逗号分隔" />
              </el-form-item>
              <el-form-item label="最大文件大小">
                <el-input-number v-model="settingsForm.upload_max_size" :min="1" />
                <span class="form-tip">单位：MB</span>
              </el-form-item>
            </template>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Monitor, Message, Upload } from '@element-plus/icons-vue'
import { getAllSettings, setSettings } from '@/api/system-settings'

// 响应式数据
const loading = ref(false)
const activeGroup = ref('basic')

const settingsForm = reactive({
  // 基础设置
  site_name: '',
  site_title: '',
  site_description: '',
  site_keywords: '',
  contact_phone: '',
  contact_email: '',
  
  // 系统配置
  system_maintenance: false,
  user_register: true,
  comment_audit: false,
  page_size: 20,
  
  // 邮件设置
  smtp_host: '',
  smtp_port: 587,
  smtp_username: '',
  smtp_password: '',
  smtp_ssl: false,
  
  // 上传设置
  upload_path: 'upload/',
  upload_extensions: 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx',
  upload_max_size: 10
})

// 获取所有设置
const fetchSettings = async () => {
  loading.value = true
  try {
    const response = await getAllSettings()
    const settings = response.data
    
    // 将设置值填充到表单
    Object.keys(settingsForm).forEach(key => {
      if (settings[key] !== undefined) {
        // 处理布尔值
        if (typeof settingsForm[key] === 'boolean') {
          settingsForm[key] = settings[key] === 'true' || settings[key] === true
        }
        // 处理数字值
        else if (typeof settingsForm[key] === 'number') {
          settingsForm[key] = parseInt(settings[key]) || settingsForm[key]
        }
        // 处理字符串值
        else {
          settingsForm[key] = settings[key] || settingsForm[key]
        }
      }
    })
  } catch (error) {
    ElMessage.error('获取系统设置失败')
  } finally {
    loading.value = false
  }
}

// 分组选择
const handleGroupSelect = (group: string) => {
  activeGroup.value = group
}

// 获取分组标题
const getGroupTitle = (group: string) => {
  const titles = {
    basic: '基础设置',
    system: '系统配置',
    email: '邮件设置',
    upload: '上传设置'
  }
  return titles[group] || '设置'
}

// 保存设置
const handleSave = async () => {
  loading.value = true
  try {
    // 转换设置格式
    const settings = {}
    Object.keys(settingsForm).forEach(key => {
      settings[key] = String(settingsForm[key])
    })
    
    await setSettings(settings)
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('设置保存失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchSettings()
})
</script>

<style scoped>
.system-settings-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.group-card {
  height: fit-content;
}

.group-menu {
  border: none;
}

.settings-card {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}
</style>
