<template>
  <div class="custom-orders-page">
    <div class="page-header">
      <h1>定制订单管理</h1>
      <p>管理用户提交的定制订单</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="订制编号、批号、类型..."
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态">
            <el-option label="全部" value="" />
            <el-option label="待处理" :value="0" />
            <el-option label="已处理" :value="1" />
            <el-option label="已取消" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table :data="orders" v-loading="loading" stripe>
        <el-table-column prop="dingzhiNo" label="订制编号" width="150" />
        <el-table-column prop="leixing" label="类型" width="120" />
        <el-table-column prop="pihaoKunkao" label="批号/捆号" width="150" />
        <el-table-column prop="yansejiPinji" label="颜色级/品级" width="200" />
        <el-table-column prop="changdu" label="长度" width="100" />
        <el-table-column prop="qiangli" label="强力" width="100" />
        <el-table-column prop="jiagongchang" label="加工厂" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="addTime" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.addTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetail(row)">详情</el-button>
            <el-button 
              size="small" 
              type="success" 
              v-if="row.status === 0"
              @click="updateStatus(row.id, 1)"
            >
              处理
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              v-if="row.status === 0"
              @click="updateStatus(row.id, 2)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="订单详情" width="800px">
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订制编号">{{ currentOrder.dingzhiNo }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ currentOrder.leixing }}</el-descriptions-item>
          <el-descriptions-item label="批号/捆号">{{ currentOrder.pihaoKunkao }}</el-descriptions-item>
          <el-descriptions-item label="颜色级/品级">{{ currentOrder.yansejiPinji }}</el-descriptions-item>
          <el-descriptions-item label="马值">{{ currentOrder.mazhi }}</el-descriptions-item>
          <el-descriptions-item label="长度">{{ currentOrder.changdu }}</el-descriptions-item>
          <el-descriptions-item label="强力">{{ currentOrder.qiangli }}</el-descriptions-item>
          <el-descriptions-item label="含杂率">{{ currentOrder.hanzalv }}</el-descriptions-item>
          <el-descriptions-item label="回潮率">{{ currentOrder.huichaolv }}</el-descriptions-item>
          <el-descriptions-item label="整齐度">{{ currentOrder.zhengqidu }}</el-descriptions-item>
          <el-descriptions-item label="加工厂">{{ currentOrder.jiagongchang }}</el-descriptions-item>
          <el-descriptions-item label="仓储名称">{{ currentOrder.cangchumingcheng }}</el-descriptions-item>
          <el-descriptions-item label="基差">{{ currentOrder.jicha }}</el-descriptions-item>
          <el-descriptions-item label="点价合约">{{ currentOrder.dianjiaheyue }}</el-descriptions-item>
          <el-descriptions-item label="件数">{{ currentOrder.baoshu }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatTime(currentOrder.addTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2" v-if="currentOrder.beizhu">
            {{ currentOrder.beizhu }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdminCustomOrderList, updateCustomOrderStatus } from '@/api/custom-orders'

// 响应式数据
const loading = ref(false)
const orders = ref([])
const detailVisible = ref(false)
const currentOrder = ref(null)

const searchForm = reactive({
  keyword: '',
  status: ''
})

const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0
})

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword,
      status: searchForm.status
    }
    
    const response = await getAdminCustomOrderList(params)
    orders.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.pageNum = 1
  fetchOrders()
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  handleSearch()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchOrders()
}

const handleCurrentChange = (page: number) => {
  pagination.pageNum = page
  fetchOrders()
}

// 查看详情
const viewDetail = (order: any) => {
  currentOrder.value = order
  detailVisible.value = true
}

// 更新状态
const updateStatus = async (id: number, status: number) => {
  const statusText = status === 1 ? '处理' : '取消'
  try {
    await ElMessageBox.confirm(`确定要${statusText}这个订单吗？`, '确认操作')
    await updateCustomOrderStatus(id, status)
    ElMessage.success(`订单${statusText}成功`)
    fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`订单${statusText}失败`)
    }
  }
}

// 获取状态类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'warning'
    case 1: return 'success'
    case 2: return 'danger'
    default: return ''
  }
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '待处理'
    case 1: return '已处理'
    case 2: return '已取消'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleString()
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.custom-orders-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.order-detail {
  max-height: 500px;
  overflow-y: auto;
}
</style>
