<template>
  <div class="article-container">
    <div class="top-actions">
      <el-button type="primary" @click="handleAddArticle">
        <el-icon><Plus /></el-icon>添加文章
      </el-button>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索文章标题/内容"
          clearable
          @clear="fetchArticleList"
          @input="handleSearchInput"
        >
          <template #append>
            <el-button @click="fetchArticleList">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
    
    <el-table v-loading="loading" :data="articleList" border style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="category" label="分类" width="100">
        <template #default="scope">
          <el-tag>{{ scope.row.category }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="author" label="作者" width="100" />
      <el-table-column prop="publishTime" label="发布时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">已发布</el-tag>
          <el-tag v-else type="info">草稿</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="viewCount" label="阅读量" width="100" />
      <el-table-column label="操作" width="240">
        <template #default="scope">
          <el-button size="small" @click="handleViewArticle(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEditArticle(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleToggleStatus(scope.row)"
          >
            {{ scope.row.status === 1 ? '下架' : '发布' }}
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDeleteArticle(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 文章编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑文章' : '添加文章'" 
      width="80%" 
      :destroy-on-close="true"
    >
      <el-form
        ref="articleFormRef"
        :model="articleForm"
        :rules="articleRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="articleForm.title" placeholder="请输入文章标题" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="articleForm.category" placeholder="请选择文章分类">
            <el-option label="通知" value="通知" />
            <el-option label="新闻" value="新闻" />
            <el-option label="技术" value="技术" />
            <el-option label="公告" value="公告" />
            <el-option label="行情" value="行情" />
          </el-select>
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <el-input
            v-model="articleForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入文章摘要"
          />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <!-- 这里实际项目中应该使用富文本编辑器，如 wangEditor, CKEditor 等 -->
          <el-input
            v-model="articleForm.content"
            type="textarea"
            :rows="10"
            placeholder="请输入文章内容"
          />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="articleForm.author" placeholder="请输入作者" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="articleForm.status">
            <el-radio :label="1">发布</el-radio>
            <el-radio :label="0">草稿</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 文章详情对话框 -->
    <el-dialog 
      v-model="viewDialogVisible" 
      title="文章详情" 
      width="80%"
    >
      <div class="article-detail">
        <h2 class="article-title">{{ currentArticle.title }}</h2>
        <div class="article-meta">
          <span>作者: {{ currentArticle.author }}</span>
          <span>分类: {{ currentArticle.category }}</span>
          <span>发布时间: {{ currentArticle.publishTime }}</span>
          <span>阅读量: {{ currentArticle.viewCount }}</span>
        </div>
        <div class="article-summary">
          <strong>摘要:</strong> {{ currentArticle.summary }}
        </div>
        <div class="article-content">
          <div v-html="currentArticle.content"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import {
  getArticleList,
  createArticle,
  updateArticle,
  deleteArticle,
  updateArticleStatus
} from '@/api/article'

// 表格数据
const loading = ref(false)
const articleList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')

// 对话框控制
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const articleFormRef = ref<FormInstance>()
const currentArticle = ref({
  id: 0,
  title: '',
  category: '',
  author: '',
  publishTime: '',
  status: 0,
  viewCount: 0,
  summary: '',
  content: ''
})

// 表单数据
const articleForm = reactive({
  id: 0,
  title: '',
  category: '',
  summary: '',
  content: '',
  author: '',
  status: 1
})

// 表单验证规则
const articleRules = reactive({
  title: [{ required: true, message: '请输入文章标题', trigger: 'blur' }],
  category: [{ required: true, message: '请选择文章分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入文章内容', trigger: 'blur' }],
  author: [{ required: true, message: '请输入作者', trigger: 'blur' }]
})

// 获取文章列表
const fetchArticleList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchQuery.value,
      status: activeTab.value === 'all' ? '' : (activeTab.value === 'published' ? 1 : 0)
    }

    const response = await getArticleList(params)
    articleList.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取文章列表失败')
    articleList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索输入
const handleSearchInput = () => {
  // 防抖处理，实际项目中建议使用lodash的debounce
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchArticleList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchArticleList()
}

// 添加文章
const handleAddArticle = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑文章
const handleEditArticle = (row: any) => {
  isEdit.value = true
  Object.assign(articleForm, row)
  dialogVisible.value = true
}

// 查看文章
const handleViewArticle = (row: any) => {
  currentArticle.value = row
  viewDialogVisible.value = true
}

// 切换文章状态
const handleToggleStatus = async (row: any) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '发布' : '下架'

  try {
    await ElMessageBox.confirm(
      `确定要${statusText}该文章吗?`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateArticleStatus(row.id, newStatus)
    ElMessage({
      type: 'success',
      message: `${statusText}成功`
    })
    fetchArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 删除文章
const handleDeleteArticle = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文章 "${row.title}" 吗?`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteArticle(row.id)
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  articleForm.id = 0
  articleForm.title = ''
  articleForm.category = ''
  articleForm.summary = ''
  articleForm.content = ''
  articleForm.author = ''
  articleForm.status = 1
}

// 提交表单
const submitForm = async () => {
  if (!articleFormRef.value) return

  await articleFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 提交数据
        if (isEdit.value) {
          await updateArticle(articleForm.id.toString(), articleForm)
        } else {
          await createArticle(articleForm)
        }

        ElMessage({
          type: 'success',
          message: isEdit.value ? '修改成功' : '添加成功'
        })
        dialogVisible.value = false
        fetchArticleList()
      } catch (error) {
        ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
      }
    }
  })
}

// 页面加载时获取文章列表
onMounted(() => {
  fetchArticleList()
})
</script>

<style scoped>
.article-container {
  padding: 20px 0;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 320px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.article-detail {
  padding: 0 20px;
}

.article-title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.article-meta {
  display: flex;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  margin-bottom: 20px;
}

.article-meta span {
  margin: 0 10px;
}

.article-summary {
  background-color: #f5f7fa;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #606266;
}

.article-content {
  line-height: 1.8;
  color: #303133;
}
</style> 