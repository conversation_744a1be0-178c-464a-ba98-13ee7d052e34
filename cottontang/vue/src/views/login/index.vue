<template>
  <div class="login-page bg_f2">
    <!-- 顶部导航组件 -->
    <TopNavigation />

    <!-- 登录区域 -->
    <div class="login_bg min_w">
      <div class="m_auto clearfix">
        <div class="login_box bg_f fr mt_70">
          <div class="login_box_tt f_18 padl_30">用户登录</div>
          <div class="padl_40">
            <form @submit.prevent="handleLogin">
              <div class="border1 pad_10 mt_30">
                <img src="/images/user.png" class="ver_mid" />
                <input
                    v-model="loginForm.username"
                    placeholder="手机号"
                    class="f_16 clr_9 border0 ver_mid ml_10"
                    maxlength="11"
                />
              </div>

              <div class="border1 pad_10 mt_30">
                <img src="/images/password.png" class="ver_mid" />
                <input
                    v-model="loginForm.password"
                    placeholder="密码"
                    type="password"
                    class="f_16 clr_9 border0 ver_mid ml_10"
                />
              </div>

              <div class="padt_30">
                <input
                    type="submit"
                    value="登录"
                    :disabled="loading"
                    class="f_16 clr_f bg_b w_100p border0 padt_10 login-submit-btn"
                />

                <div class="f_14" style="margin-top:8px; float:left;">
                  <label>
                    <input
                        type="checkbox"
                        v-model="loginForm.member"
                        class="ver_mid"
                        :true-value="1"
                        :false-value="0"
                    >
                    <span class="ver_mid">记住密码</span>
                  </label>
                </div>

                <div class="f_14 al_rt mt_10">
                  <router-link to="/forget-password" class="clr_b">忘记密码？</router-link>
                  <router-link to="/register" class="clr_3">新用户注册</router-link>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <div class="min_w">
      <div class="f_14 clr_9 al_ct padt_10 lh_28 footer-content">
        版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>
        Copyright &copy; 2021 鲁ICP备2021017032号-1
      </div>
    </div>

    <!-- 侧边栏 -->
    <div class="side">
      <ul>
        <li class="sideewm">
          <i class="bgs3"></i>官方微信
          <div class="ewBox son"></div>
        </li>
        <li class="sideetel">
          <i class="bgs4"></i>联系电话
          <div class="telBox son">
            <dd class="bgs2"><span>手机</span>19853227218</dd>
          </div>
        </li>
        <li class="sidetop" @click="goTop">
          <i class="bgs6"></i>返回顶部
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {login} from '@/api/auth'
import {useUserStore} from '@/stores/user'
import TopNavigation from '@/components/TopNavigation.vue'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)

// 搜索功能已移到TopNavigation组件中

// Login form data - 完全按照PHP逻辑
const loginForm = reactive({
  username: '',
  password: '',
  member: 0  // 0: 不记住, 1: 记住7天
})

// Login handler
const handleLogin = async () => {
  // 简单验证
  if (!loginForm.username) {
    ElMessage.error('请输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.username)) {
    ElMessage.error('请输入正确的手机号')
    return
  }

  if (!loginForm.password) {
    ElMessage.error('请输入密码')
    return
  }

  try {
    loading.value = true

    const response = await login({
      username: loginForm.username,
      password: loginForm.password,
      member: loginForm.member
    })

    // 存储token和用户信息
    userStore.setToken(response.data.token)
    userStore.setUserInfo(response.data.userInfo)

    // 记住密码 - 按照PHP逻辑
    if (loginForm.member === 1) {
      localStorage.setItem('remembered_username', loginForm.username)
    } else {
      localStorage.removeItem('remembered_username')
    }

    ElMessage.success('登录成功')

    // 跳转到首页
    router.push('/public/home')
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 搜索功能已移到TopNavigation组件中

// 返回顶部
const goTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 页面加载时恢复记住的用户名
onMounted(() => {
  const rememberedUsername = localStorage.getItem('remembered_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.member = 1
  }
})
</script>

<style scoped>

/* 底部内容样式 */
.footer-content {
  padding: 20px 0;
}

/* 搜索样式已移到TopNavigation组件中 */

/* 页面整体 */
.login-page {
  min-height: 100vh;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

/* 登录背景 */
.login_bg {
  background: url('/images/login_bg.jpg') no-repeat center center;
  background-size: cover;
  height: 553px;
  position: relative;
}

/* 登录框 */
.login_box {
  width: 380px;
  background: #fff;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login_box_tt {
  height: 60px;
  background: #eee;
  line-height: 60px;
  font-weight: normal;
}

/* 输入框容器 */
.border1 {
  display: flex;
  align-items: center;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
}

/* 输入框样式 */
.login_box input[type="text"],
.login_box input[type="password"],
.login_box input:not([type="submit"]):not([type="checkbox"]) {
  width: 280px;
  outline: none;
  background: transparent;
  border: none;
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

.login_box input[type="text"]:focus,
.login_box input[type="password"]:focus {
  outline: none;
}

.login_box input::placeholder {
  color: #999;
}

/* 图标样式 */
.login_box img {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 登录按钮 */
.login-submit-btn {
  height: 45px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 16px;
  font-weight: normal;
  border-radius: 0;
}

.login-submit-btn:hover:not(:disabled) {
  background: #1182c4 !important;
}

.login-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 复选框样式 */
input[type="checkbox"] {
  margin-right: 5px;
  vertical-align: middle;
}

/* 侧边栏样式 */
.side {
  position: fixed;
  width: 60px;
  right: 0;
  top: 60%;
  margin-top: -200px;
  z-index: 100;
  border: 1px solid #e0e0e0;
  background: #fff;
  border-bottom: 0;
}

.side ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.side ul li {
  width: 60px;
  height: 70px;
  float: left;
  position: relative;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
  font-size: 14px;
  line-height: 38px;
  text-align: center;
  transition: all 0.3s;
  cursor: pointer;
}

.side ul li:hover {
  background: #f67524;
  color: #fff;
}

.side ul li i {
  height: 25px;
  margin-bottom: 1px;
  display: block;
  overflow: hidden;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: auto 25px;
  margin-top: 14px;
  transition: all 0.3s;
}

.side ul li i.bgs3 {
  background-image: url('/images/right_pic2.png');
}

.side ul li i.bgs4 {
  background-image: url('/images/right_pic1.png');
}

.side ul li i.bgs6 {
  background-image: url('/images/right_pic6_on.png');
}

.side ul li.sidetop {
  background: #f67524;
  color: #fff;
}

.side ul li.sidetop:hover {
  opacity: 0.8;
}

.side ul li.sideewm .ewBox.son {
  width: 238px;
  display: none;
  color: #363636;
  text-align: center;
  padding-top: 212px;
  position: absolute;
  left: -240px;
  top: 0;
  background-image: url('/images/leftewm.png');
  background-repeat: no-repeat;
  background-position: center center;
  border: 1px solid #e0e0e0;
}

.side ul li.sideetel .telBox.son {
  width: 240px;
  height: 214px;
  display: none;
  color: #fff;
  text-align: left;
  position: absolute;
  left: -240px;
  top: -72px;
  background: #f67524;
}

.side ul li.sideetel .telBox dd {
  display: block;
  height: 118.5px;
  overflow: hidden;
  padding-left: 82px;
  line-height: 24px;
  font-size: 18px;
  margin: 0;
}

.side ul li.sideetel .telBox dd span {
  display: block;
  line-height: 28px;
  height: 28px;
  overflow: hidden;
  margin-top: 32px;
  font-size: 18px;
}

.side ul li.sideetel .telBox dd.bgs2 {
  background: url('/images/right_pic9.png') 28px center no-repeat;
}

.side ul li:hover .son {
  display: block !important;
  animation: fadein 1s;
}


</style>
