<template>
  <div class="homepage-data-container">
    <div class="top-actions">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜索首页数据..."
          :prefix-icon="Search"
          @input="handleSearchInput"
          clearable
        />
      </div>
      <el-button type="primary" :icon="Plus" @click="handleAddData">
        添加数据
      </el-button>
    </div>

    <el-table
      :data="dataList"
      v-loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="标题" min-width="200" />
      <el-table-column prop="danType" label="类型" width="120">
        <template #default="scope">
          <el-tag :type="getTypeTagType(scope.row.danType)">
            {{ getTypeName(scope.row.danType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" width="120">
        <template #default="scope">
          <span v-if="scope.row.price">¥{{ scope.row.price }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="isRecom" label="推荐" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.isRecom ? 'success' : 'info'">
            {{ scope.row.isRecom ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="创建时间" width="180">
        <template #default="scope">
          {{ formatTime(scope.row.addTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="handlePreviewData(scope.row)">预览</el-button>
          <el-button size="small" type="primary" @click="handleEditData(scope.row)">编辑</el-button>
          <el-button
            size="small"
            type="danger"
            @click="handleDeleteData(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑首页数据' : '添加首页数据'"
      width="600px"
    >
      <el-form
        ref="dataFormRef"
        :model="dataForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="dataForm.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="类型" prop="danType">
          <el-select v-model="dataForm.danType" placeholder="请选择类型">
            <el-option label="产品" value="product" />
            <el-option label="新闻" value="news" />
            <el-option label="公告" value="notice" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="dataForm.price" :min="0" :precision="2" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="dataForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="是否推荐" prop="isRecom">
          <el-switch v-model="dataForm.isRecom" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import {
  getAdminHomepageList,
  createHomepage,
  updateHomepage,
  deleteHomepage
} from '@/api/homepage'

// 表格数据
const loading = ref(false)
const dataList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')

// 对话框
const dialogVisible = ref(false)
const isEdit = ref(false)
const dataFormRef = ref<FormInstance>()

// 表单数据
const dataForm = reactive({
  id: 0,
  title: '',
  danType: '',
  price: 0,
  description: '',
  isRecom: 0
})

// 表单验证规则
const formRules = reactive({
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  danType: [{ required: true, message: '请选择类型', trigger: 'change' }]
})

// 获取首页数据列表
const fetchDataList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchQuery.value
    }
    
    const response = await getAdminHomepageList(params)
    dataList.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取首页数据列表失败')
    dataList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索输入
const handleSearchInput = () => {
  // 防抖处理，实际项目中建议使用lodash的debounce
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  fetchDataList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchDataList()
}

// 添加数据
const handleAddData = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑数据
const handleEditData = (row: any) => {
  isEdit.value = true
  Object.assign(dataForm, row)
  dialogVisible.value = true
}

// 预览数据
const handlePreviewData = (row: any) => {
  ElMessage.info('预览功能待实现')
}

// 删除数据
const handleDeleteData = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数据 "${row.title}" 吗?`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteHomepage(row.id)
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchDataList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  dataForm.id = 0
  dataForm.title = ''
  dataForm.danType = ''
  dataForm.price = 0
  dataForm.description = ''
  dataForm.isRecom = 0
}

// 提交表单
const submitForm = async () => {
  if (!dataFormRef.value) return
  
  await dataFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 提交数据
        if (isEdit.value) {
          await updateHomepage(dataForm.id.toString(), dataForm)
        } else {
          await createHomepage(dataForm)
        }
        
        ElMessage({
          type: 'success',
          message: isEdit.value ? '修改成功' : '添加成功'
        })
        dialogVisible.value = false
        fetchDataList()
      } catch (error) {
        ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
      }
    }
  })
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    product: 'success',
    news: 'primary',
    notice: 'warning',
    other: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取类型名称
const getTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    product: '产品',
    news: '新闻',
    notice: '公告',
    other: '其他'
  }
  return typeMap[type] || '未知'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return '-'
  return new Date(timestamp * 1000).toLocaleString()
}

// 页面加载时获取数据列表
onMounted(() => {
  fetchDataList()
})
</script>

<style scoped>
.homepage-data-container {
  padding: 20px 0;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.search-box {
  width: 320px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
