declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'vue-router' {
  import type { RouteRecordRaw } from 'vue-router'
  export { RouteRecordRaw }
  export function useRoute(): any
  export function useRouter(): any
  export function createRouter(options: any): any
  export function createWebHistory(base?: string): any
}

declare module '@element-plus/icons-vue' {
  import type { Component } from 'vue'
  export const HomeFilled: Component
  export const User: Component
  export const House: Component
  export const Document: Component
  export const Picture: Component
  export const ShoppingCart: Component
  export const Expand: Component
  export const Fold: Component
  export const CaretBottom: Component
} 