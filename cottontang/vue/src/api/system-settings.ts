import request from '@/utils/request'

// 获取所有系统设置
export function getAllSettings() {
  return request({
    url: '/system-settings',
    method: 'get'
  })
}

// 根据键获取设置值
export function getSettingValue(key: string) {
  return request({
    url: `/system-settings/${key}`,
    method: 'get'
  })
}

// 根据分组获取设置列表
export function getSettingsByGroup(group: string) {
  return request({
    url: `/system-settings/group/${group}`,
    method: 'get'
  })
}

// 设置配置值
export function setSetting(key: string, value: string) {
  return request({
    url: `/system-settings/${key}`,
    method: 'post',
    params: {
      value
    }
  })
}

// 批量设置配置
export function setSettings(settings: Record<string, string>) {
  return request({
    url: '/system-settings/batch',
    method: 'post',
    data: settings
  })
}

// 删除设置
export function deleteSetting(key: string) {
  return request({
    url: `/system-settings/${key}`,
    method: 'delete'
  })
}
