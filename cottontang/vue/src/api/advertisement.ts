import request from '@/utils/request'

// 获取广告列表
export function getAdvertisementList(params: any) {
  return request({
    url: '/advertisement/list',
    method: 'get',
    params
  })
}

// 根据ID获取广告详情
export function getAdvertisementById(id: string) {
  return request({
    url: `/advertisement/${id}`,
    method: 'get'
  })
}

// 创建广告
export function createAdvertisement(data: any) {
  return request({
    url: '/advertisement',
    method: 'post',
    data
  })
}

// 更新广告
export function updateAdvertisement(id: string, data: any) {
  return request({
    url: `/advertisement/${id}`,
    method: 'put',
    data
  })
}

// 删除广告
export function deleteAdvertisement(id: string) {
  return request({
    url: `/advertisement/${id}`,
    method: 'delete'
  })
}

// 更新广告状态
export function updateAdvertisementStatus(id: string, status: number) {
  return request({
    url: `/advertisement/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}
