import request from '@/utils/request'

// 获取仪表板统计数据
export function getDashboardStats() {
  return request({
    url: '/dashboard/stats',
    method: 'get'
  })
}

// 获取最近文章
export function getRecentArticles(limit = 5) {
  return request({
    url: '/dashboard/recent-articles',
    method: 'get',
    params: {
      limit
    }
  })
}

// 获取通知列表
export function getNotifications(limit = 5) {
  return request({
    url: '/dashboard/notifications',
    method: 'get',
    params: {
      limit
    }
  })
}

// 获取系统概览数据
export function getSystemOverview() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  })
}

// 获取用户活动统计
export function getUserActivityStats(days = 7) {
  return request({
    url: '/dashboard/user-activity',
    method: 'get',
    params: {
      days
    }
  })
}

// 获取销售统计
export function getSalesStats(period = 'month') {
  return request({
    url: '/dashboard/sales-stats',
    method: 'get',
    params: {
      period
    }
  })
}
