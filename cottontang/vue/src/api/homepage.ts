import request from '@/utils/request'

// 获取首页数据列表（公开接口）
export function getHomepageList(params: any) {
  return request({
    url: '/homepage/public/list',
    method: 'get',
    params
  })
}

// 根据ID获取首页数据详情
export function getHomepageById(id: string) {
  return request({
    url: `/homepage/${id}`,
    method: 'get'
  })
}

// 根据类型获取首页数据
export function getHomepageByType(danType: string, params: any) {
  return request({
    url: `/homepage/type/${danType}`,
    method: 'get',
    params
  })
}

// 管理员接口
export function getAdminHomepageList(params: any) {
  return request({
    url: '/homepage/admin/list',
    method: 'get',
    params
  })
}

export function createHomepage(data: any) {
  return request({
    url: '/homepage',
    method: 'post',
    data
  })
}

export function updateHomepage(id: string, data: any) {
  return request({
    url: `/homepage/${id}`,
    method: 'put',
    data
  })
}

export function deleteHomepage(id: string) {
  return request({
    url: `/homepage/${id}`,
    method: 'delete'
  })
}

// 收藏相关接口
export function addFavorite(favorId: number, favorType: number) {
  return request({
    url: '/favorites',
    method: 'post',
    params: {
      favorId,
      favorType
    }
  })
}

export function removeFavorite(favorId: number) {
  return request({
    url: `/favorites/${favorId}`,
    method: 'delete'
  })
}

export function checkFavorite(favorId: number) {
  return request({
    url: `/favorites/check/${favorId}`,
    method: 'get'
  })
}

export function getUserFavorites(params: any) {
  return request({
    url: '/favorites/list',
    method: 'get',
    params
  })
}
