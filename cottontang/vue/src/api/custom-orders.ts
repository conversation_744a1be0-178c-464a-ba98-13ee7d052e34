import request from '@/utils/request'

// 获取定制订单列表（管理员）
export function getAdminCustomOrderList(params: any) {
  return request({
    url: '/custom-orders/admin/list',
    method: 'get',
    params
  })
}

// 获取用户定制订单列表
export function getUserCustomOrderList(params: any) {
  return request({
    url: '/custom-orders/user/list',
    method: 'get',
    params
  })
}

// 根据ID获取定制订单详情
export function getCustomOrderById(id: string) {
  return request({
    url: `/custom-orders/${id}`,
    method: 'get'
  })
}

// 创建定制订单
export function createCustomOrder(data: any) {
  return request({
    url: '/custom-orders',
    method: 'post',
    data
  })
}

// 更新定制订单
export function updateCustomOrder(id: string, data: any) {
  return request({
    url: `/custom-orders/${id}`,
    method: 'put',
    data
  })
}

// 删除定制订单
export function deleteCustomOrder(id: string) {
  return request({
    url: `/custom-orders/${id}`,
    method: 'delete'
  })
}

// 更新订单状态
export function updateCustomOrderStatus(id: number, status: number) {
  return request({
    url: `/custom-orders/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}
