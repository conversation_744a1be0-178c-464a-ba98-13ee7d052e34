import request from '@/utils/request'

// 用户登录
export function login(data: any) {
  return request({
    url: '/user-auth/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data: any) {
  return request({
    url: '/user-auth/register',
    method: 'post',
    data
  })
}

// 获取当前用户信息
export function getUserInfo() {
  return request({
    url: '/user-auth/info',
    method: 'get'
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/user-auth/logout',
    method: 'post'
  })
}

// 检查用户名是否存在
export function checkUsername(username: string) {
  return request({
    url: '/user-auth/check-username',
    method: 'get',
    params: {
      username
    }
  })
}

// 修改密码
export function changePassword(data: any) {
  return request({
    url: '/user-auth/change-password',
    method: 'post',
    data
  })
}

// 更新用户信息
export function updateUserInfo(data: any) {
  return request({
    url: '/user-auth/update-info',
    method: 'put',
    data
  })
}
