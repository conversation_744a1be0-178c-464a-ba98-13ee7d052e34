import request from '@/utils/request'

// 获取用户列表
export function getUserList(params: any) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

// 根据ID获取用户详情
export function getUserById(id: string) {
  return request({
    url: `/user/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data: any) {
  return request({
    url: '/user',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id: string, data: any) {
  return request({
    url: `/user/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id: string) {
  return request({
    url: `/user/${id}`,
    method: 'delete'
  })
}

// 审核用户
export function checkUser(id: string, isCheck: number) {
  return request({
    url: `/user/${id}/check`,
    method: 'put',
    params: {
      isCheck
    }
  })
}

// 重置用户密码
export function resetUserPassword(id: string, password: string) {
  return request({
    url: `/user/${id}/reset-password`,
    method: 'put',
    params: {
      password
    }
  })
}
