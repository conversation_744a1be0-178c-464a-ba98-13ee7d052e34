import request from '@/utils/request'

// 获取收藏列表
export function getFavorites(params: any) {
  return request({
    url: '/favorites/list',
    method: 'get',
    params
  })
}

// 添加收藏
export function addFavorite(data: any) {
  return request({
    url: '/favorites',
    method: 'post',
    data
  })
}

// 取消收藏
export function removeFavorite(id: number) {
  return request({
    url: `/favorites/${id}`,
    method: 'delete'
  })
}

// 检查是否已收藏
export function checkFavorite(targetId: number, targetType: string) {
  return request({
    url: '/favorites/check',
    method: 'get',
    params: {
      targetId,
      targetType
    }
  })
}

// 批量删除收藏
export function batchRemoveFavorites(ids: number[]) {
  return request({
    url: '/favorites/batch',
    method: 'delete',
    data: { ids }
  })
}
