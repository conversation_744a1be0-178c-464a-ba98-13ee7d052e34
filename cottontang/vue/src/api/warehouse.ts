import request from '@/utils/request'

// 获取仓库列表
export function getWarehouseList(params: any) {
  return request({
    url: '/warehouse/list',
    method: 'get',
    params
  })
}

// 根据ID获取仓库详情
export function getWarehouseById(id: string) {
  return request({
    url: `/warehouse/${id}`,
    method: 'get'
  })
}

// 创建仓库
export function createWarehouse(data: any) {
  return request({
    url: '/warehouse',
    method: 'post',
    data
  })
}

// 更新仓库
export function updateWarehouse(id: string, data: any) {
  return request({
    url: `/warehouse/${id}`,
    method: 'put',
    data
  })
}

// 删除仓库
export function deleteWarehouse(id: string) {
  return request({
    url: `/warehouse/${id}`,
    method: 'delete'
  })
}

// 获取仓库库存
export function getWarehouseInventory(warehouseId: string, params?: any) {
  return request({
    url: `/warehouse/${warehouseId}/inventory`,
    method: 'get',
    params
  })
}

// 更新仓库状态
export function updateWarehouseStatus(id: string, status: number) {
  return request({
    url: `/warehouse/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}
