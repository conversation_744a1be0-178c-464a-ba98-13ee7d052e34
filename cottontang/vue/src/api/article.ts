import request from '@/utils/request'

// 获取文章列表
export function getArticleList(params: any) {
  return request({
    url: '/articles/page',
    method: 'get',
    params
  })
}

// 根据ID获取文章详情
export function getArticleById(id: string) {
  return request({
    url: `/articles/${id}`,
    method: 'get'
  })
}

// 创建文章
export function createArticle(data: any) {
  return request({
    url: '/articles',
    method: 'post',
    data
  })
}

// 更新文章
export function updateArticle(id: string, data: any) {
  return request({
    url: `/articles/${id}`,
    method: 'put',
    data
  })
}

// 删除文章
export function deleteArticle(id: string) {
  return request({
    url: `/articles/${id}`,
    method: 'delete'
  })
}

// 更新文章状态
export function updateArticleStatus(id: string, status: number) {
  return request({
    url: `/articles/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}
